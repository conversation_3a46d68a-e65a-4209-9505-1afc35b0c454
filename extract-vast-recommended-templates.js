#!/usr/bin/env node

/**
 * Extract detailed information from Vast.ai recommended templates
 * Clicks on edit pencil for each recommended template to get full details
 */

import { spawn } from 'child_process';
import { config } from 'dotenv';
import fs from 'fs';

// Load environment variables
config();

console.log('🚀 Extracting Vast.ai Recommended Templates with Details...');

class VastTemplateExtractor {
    constructor() {
        this.mcpProcess = null;
        this.templates = [];
    }

    async startMCPServer() {
        return new Promise((resolve, reject) => {
            this.mcpProcess = spawn('npx', ['@brightdata/mcp'], {
                env: {
                    ...process.env,
                    API_TOKEN: process.env.API_TOKEN
                },
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.mcpProcess.stderr.on('data', (data) => {
                const output = data.toString();
                console.log('📡 MCP Server:', output);
                if (output.includes('Starting server...')) {
                    setTimeout(resolve, 2000); // Wait for server to be ready
                }
            });

            this.mcpProcess.on('error', reject);
        });
    }

    async sendMCPCommand(method, params = {}) {
        return new Promise((resolve, reject) => {
            const request = {
                jsonrpc: "2.0",
                id: Date.now(),
                method: method,
                params: params
            };

            console.log(`📤 Sending: ${method}`, params);

            this.mcpProcess.stdin.write(JSON.stringify(request) + '\n');

            let responseData = '';
            const onData = (data) => {
                responseData += data.toString();
                try {
                    const response = JSON.parse(responseData);
                    this.mcpProcess.stdout.removeListener('data', onData);
                    console.log(`📥 Response: ${method}`, response);
                    resolve(response);
                } catch (e) {
                    // Continue collecting data
                }
            };

            this.mcpProcess.stdout.on('data', onData);

            setTimeout(() => {
                this.mcpProcess.stdout.removeListener('data', onData);
                reject(new Error(`Timeout waiting for response to ${method}`));
            }, 30000);
        });
    }

    async navigateToTemplatesPage() {
        console.log('🌐 Navigating to Vast.ai templates page...');
        
        const response = await this.sendMCPCommand('tools/call', {
            name: 'scraping_browser_navigate',
            arguments: {
                url: 'https://cloud.vast.ai/templates/'
            }
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        return response;
    }

    async takeScreenshot(filename = 'templates-page.png') {
        console.log(`📸 Taking screenshot: ${filename}`);
        
        const response = await this.sendMCPCommand('tools/call', {
            name: 'scraping_browser_screenshot',
            arguments: {}
        });

        if (response.result && response.result.content) {
            // Save screenshot data if available
            console.log('✅ Screenshot taken successfully');
        }
        
        return response;
    }

    async findRecommendedTemplates() {
        console.log('🔍 Looking for recommended templates...');
        
        // Get all links on the page to identify template cards
        const linksResponse = await this.sendMCPCommand('tools/call', {
            name: 'scraping_browser_links',
            arguments: {}
        });

        console.log('🔗 Found links:', linksResponse);

        // Get page text to identify recommended sections
        const textResponse = await this.sendMCPCommand('tools/call', {
            name: 'scraping_browser_get_text',
            arguments: {}
        });

        console.log('📝 Page text content extracted');
        
        return { links: linksResponse, text: textResponse };
    }

    async clickEditButtons() {
        console.log('✏️ Looking for edit pencil buttons...');
        
        // Look for edit buttons (pencil icons) - these are typically SVG icons or buttons with specific classes
        const editSelectors = [
            'button[title*="edit"]',
            'button[aria-label*="edit"]', 
            '.edit-button',
            '[data-testid*="edit"]',
            'svg[data-icon="edit"]',
            '.fa-edit',
            '.fa-pencil'
        ];

        for (const selector of editSelectors) {
            try {
                console.log(`🎯 Trying selector: ${selector}`);
                
                const clickResponse = await this.sendMCPCommand('tools/call', {
                    name: 'scraping_browser_click',
                    arguments: {
                        selector: selector
                    }
                });

                if (clickResponse.result) {
                    console.log(`✅ Successfully clicked edit button with selector: ${selector}`);
                    
                    // Wait for modal/form to load
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    
                    // Extract the template details
                    const details = await this.extractTemplateDetails();
                    this.templates.push(details);
                    
                    // Close modal/form (try common close methods)
                    await this.closeModal();
                    
                    // Wait before next action
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
                
            } catch (error) {
                console.log(`⚠️ Selector ${selector} not found or failed:`, error.message);
            }
        }
    }

    async extractTemplateDetails() {
        console.log('📋 Extracting template details from form/modal...');
        
        // Get HTML content of the current state (with modal open)
        const htmlResponse = await this.sendMCPCommand('tools/call', {
            name: 'scraping_browser_get_html',
            arguments: {}
        });

        // Get text content
        const textResponse = await this.sendMCPCommand('tools/call', {
            name: 'scraping_browser_get_text',
            arguments: {}
        });

        // Take screenshot of the modal
        await this.takeScreenshot(`template-details-${Date.now()}.png`);

        return {
            timestamp: new Date().toISOString(),
            html: htmlResponse.result?.content?.[0]?.text || '',
            text: textResponse.result?.content?.[0]?.text || ''
        };
    }

    async closeModal() {
        console.log('❌ Attempting to close modal...');
        
        const closeSelectors = [
            'button[aria-label*="close"]',
            'button[title*="close"]',
            '.close-button',
            '.modal-close',
            '[data-testid*="close"]',
            '.fa-times',
            '.fa-close',
            'button:contains("Cancel")',
            'button:contains("Close")',
            '[role="button"][aria-label="Close"]'
        ];

        for (const selector of closeSelectors) {
            try {
                await this.sendMCPCommand('tools/call', {
                    name: 'scraping_browser_click',
                    arguments: {
                        selector: selector
                    }
                });
                console.log(`✅ Modal closed with selector: ${selector}`);
                return;
            } catch (error) {
                // Try next selector
            }
        }

        // Try pressing Escape key
        try {
            await this.sendMCPCommand('tools/call', {
                name: 'scraping_browser_type',
                arguments: {
                    selector: 'body',
                    text: '',
                    key: 'Escape'
                }
            });
            console.log('✅ Modal closed with Escape key');
        } catch (error) {
            console.log('⚠️ Could not close modal');
        }
    }

    async saveResults() {
        const results = {
            extractedAt: new Date().toISOString(),
            url: 'https://cloud.vast.ai/templates/',
            templatesFound: this.templates.length,
            templates: this.templates
        };

        const filename = `vast-recommended-templates-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(results, null, 2));
        
        console.log(`💾 Results saved to: ${filename}`);
        console.log(`📊 Found ${this.templates.length} template details`);
        
        return filename;
    }

    async cleanup() {
        if (this.mcpProcess) {
            this.mcpProcess.kill('SIGINT');
        }
    }

    async run() {
        try {
            await this.startMCPServer();
            await this.navigateToTemplatesPage();
            await this.takeScreenshot('initial-page.png');
            await this.findRecommendedTemplates();
            await this.clickEditButtons();
            const filename = await this.saveResults();
            
            console.log('✅ Extraction completed successfully!');
            console.log(`📁 Results saved in: ${filename}`);
            
        } catch (error) {
            console.error('❌ Error during extraction:', error.message);
        } finally {
            await this.cleanup();
        }
    }
}

// Run the extractor
const extractor = new VastTemplateExtractor();
extractor.run().then(() => {
    process.exit(0);
}).catch((error) => {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
});
