/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  ConnectionInfo,
  ConnectionInfo$inboundSchema,
  ConnectionInfo$Outbound,
  ConnectionInfo$outboundSchema,
} from "./connectioninfo.js";
import {
  Tool,
  Tool$inboundSchema,
  Tool$Outbound,
  Tool$outboundSchema,
} from "./tool.js";

/**
 * Information about the server's security status
 */
export type ServerDetailResponseSecurity = {
  /**
   * Whether the server has passed security checks
   */
  scanPassed?: boolean | null | undefined;
};

export type ServerDetailResponse = {
  /**
   * Qualified name of the MCP server in the format `owner/repository`
   */
  qualifiedName: string;
  /**
   * Human-readable name of the MCP server
   */
  displayName: string;
  /**
   * URL to the server's icon image
   */
  iconUrl?: string | null | undefined;
  /**
   * Whether this server is a remote server
   */
  remote?: boolean | undefined;
  /**
   * Specifies how to connect to this server
   */
  connections: Array<ConnectionInfo>;
  /**
   * Information about the server's security status
   */
  security?: ServerDetailResponseSecurity | null | undefined;
  /**
   * List of tools that this server provides
   */
  tools?: Array<Tool> | null | undefined;
};

/** @internal */
export const ServerDetailResponseSecurity$inboundSchema: z.ZodType<
  ServerDetailResponseSecurity,
  z.ZodTypeDef,
  unknown
> = z.object({
  scanPassed: z.nullable(z.boolean()).optional(),
});

/** @internal */
export type ServerDetailResponseSecurity$Outbound = {
  scanPassed?: boolean | null | undefined;
};

/** @internal */
export const ServerDetailResponseSecurity$outboundSchema: z.ZodType<
  ServerDetailResponseSecurity$Outbound,
  z.ZodTypeDef,
  ServerDetailResponseSecurity
> = z.object({
  scanPassed: z.nullable(z.boolean()).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ServerDetailResponseSecurity$ {
  /** @deprecated use `ServerDetailResponseSecurity$inboundSchema` instead. */
  export const inboundSchema = ServerDetailResponseSecurity$inboundSchema;
  /** @deprecated use `ServerDetailResponseSecurity$outboundSchema` instead. */
  export const outboundSchema = ServerDetailResponseSecurity$outboundSchema;
  /** @deprecated use `ServerDetailResponseSecurity$Outbound` instead. */
  export type Outbound = ServerDetailResponseSecurity$Outbound;
}

export function serverDetailResponseSecurityToJSON(
  serverDetailResponseSecurity: ServerDetailResponseSecurity,
): string {
  return JSON.stringify(
    ServerDetailResponseSecurity$outboundSchema.parse(
      serverDetailResponseSecurity,
    ),
  );
}

export function serverDetailResponseSecurityFromJSON(
  jsonString: string,
): SafeParseResult<ServerDetailResponseSecurity, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ServerDetailResponseSecurity$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ServerDetailResponseSecurity' from JSON`,
  );
}

/** @internal */
export const ServerDetailResponse$inboundSchema: z.ZodType<
  ServerDetailResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  qualifiedName: z.string(),
  displayName: z.string(),
  iconUrl: z.nullable(z.string()).optional(),
  remote: z.boolean().optional(),
  connections: z.array(ConnectionInfo$inboundSchema),
  security: z.nullable(z.lazy(() => ServerDetailResponseSecurity$inboundSchema))
    .optional(),
  tools: z.nullable(z.array(Tool$inboundSchema)).optional(),
});

/** @internal */
export type ServerDetailResponse$Outbound = {
  qualifiedName: string;
  displayName: string;
  iconUrl?: string | null | undefined;
  remote?: boolean | undefined;
  connections: Array<ConnectionInfo$Outbound>;
  security?: ServerDetailResponseSecurity$Outbound | null | undefined;
  tools?: Array<Tool$Outbound> | null | undefined;
};

/** @internal */
export const ServerDetailResponse$outboundSchema: z.ZodType<
  ServerDetailResponse$Outbound,
  z.ZodTypeDef,
  ServerDetailResponse
> = z.object({
  qualifiedName: z.string(),
  displayName: z.string(),
  iconUrl: z.nullable(z.string()).optional(),
  remote: z.boolean().optional(),
  connections: z.array(ConnectionInfo$outboundSchema),
  security: z.nullable(
    z.lazy(() => ServerDetailResponseSecurity$outboundSchema),
  ).optional(),
  tools: z.nullable(z.array(Tool$outboundSchema)).optional(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ServerDetailResponse$ {
  /** @deprecated use `ServerDetailResponse$inboundSchema` instead. */
  export const inboundSchema = ServerDetailResponse$inboundSchema;
  /** @deprecated use `ServerDetailResponse$outboundSchema` instead. */
  export const outboundSchema = ServerDetailResponse$outboundSchema;
  /** @deprecated use `ServerDetailResponse$Outbound` instead. */
  export type Outbound = ServerDetailResponse$Outbound;
}

export function serverDetailResponseToJSON(
  serverDetailResponse: ServerDetailResponse,
): string {
  return JSON.stringify(
    ServerDetailResponse$outboundSchema.parse(serverDetailResponse),
  );
}

export function serverDetailResponseFromJSON(
  jsonString: string,
): SafeParseResult<ServerDetailResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ServerDetailResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ServerDetailResponse' from JSON`,
  );
}
