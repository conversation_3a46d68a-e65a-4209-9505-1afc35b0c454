Metadata-Version: 2.4
Name: opentelemetry-distro
Version: 0.56b0
Summary: OpenTelemetry Python Distro
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/opentelemetry-distro
Project-URL: Repository, https://github.com/open-telemetry/opentelemetry-python-contrib
Author-email: OpenTelemetry Authors <<EMAIL>>
License-Expression: Apache-2.0
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Typing :: Typed
Requires-Python: >=3.9
Requires-Dist: opentelemetry-api~=1.12
Requires-Dist: opentelemetry-instrumentation==0.56b0
Requires-Dist: opentelemetry-sdk~=1.13
Provides-Extra: otlp
Requires-Dist: opentelemetry-exporter-otlp==1.35.0; extra == 'otlp'
Description-Content-Type: text/x-rst

OpenTelemetry Distro
====================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-distro.svg
   :target: https://pypi.org/project/opentelemetry-distro/

Installation
------------

::

    pip install opentelemetry-distro


This package provides entrypoints to configure OpenTelemetry.

References
----------

* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `Example using opentelemetry-distro <https://opentelemetry.io/docs/instrumentation/python/distro/>`_
