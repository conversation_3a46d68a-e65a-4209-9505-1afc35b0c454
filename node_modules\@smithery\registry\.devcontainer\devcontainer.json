// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/images/tree/main/src/typescript-node
{
    "name": "TypeScript",
    "image": "mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye",
    // Features to add to the dev container. More info: https://containers.dev/features.
    // "features": {},
    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    // "forwardPorts": [],
    // Use 'postCreateCommand' to run commands after the container is created.
    "postCreateCommand": "sudo chmod +x ./.devcontainer/setup.sh && ./.devcontainer/setup.sh",
    "customizations": {
        "vscode": {
            "extensions": [
                "ms-vscode.vscode-typescript-tslint-plugin",
                "esbenp.prettier-vscode",
                "github.vscode-pull-request-github"
            ],
            "settings": {
                "files.eol": "\n",
                "editor.formatOnSave": true,
                "typescript.tsc.autoDetect": "on",
                "typescript.updateImportsOnFileMove.enabled": "always",
                "typescript.preferences.importModuleSpecifier": "relative",
                "[typescript]": {
                    "editor.codeActionsOnSave": {
                        "source.organizeImports": true
                    }
                },
                "[typescriptreact]": {
                    "editor.codeActionsOnSave": {
                        "source.organizeImports": true
                    }
                }
            }
        },
        "codespaces": {
            "openFiles": [
                ".devcontainer/README.md"
            ]
        }
    }
    // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
    // "remoteUser": "root"
}
