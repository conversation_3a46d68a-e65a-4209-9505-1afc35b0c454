name: Release
on:
  push:
    branches:
      - main
jobs:
  test:
    environment: release
    name: Test
    strategy:
      fail-fast: true
      matrix:
        node:
          - 22
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
    steps:
      - name: setup repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: setup node.js
        uses: actions/setup-node@v4
        with:
          cache: "pnpm"
          node-version: ${{ matrix.node }}
      - name: Setup NodeJS ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: "pnpm"
          cache-dependency-path: "**/pnpm-lock.yaml"
      - name: Install dependencies
        run: pnpm install
      - name: Run lint
        run: pnpm lint
      - name: Run tests
        run: pnpm test
      - name: Build
        run: pnpm build
      - name: Release
        run: pnpm semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
