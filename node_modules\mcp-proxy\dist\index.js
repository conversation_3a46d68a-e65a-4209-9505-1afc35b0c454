import {
  InMemoryEventStore,
  proxyServer,
  startHTTPServer
} from "./chunk-MXVPEZER.js";

// src/startStdioServer.ts
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
var ServerType = /* @__PURE__ */ ((ServerType2) => {
  ServerType2["HTTPStream"] = "HTTPStream";
  ServerType2["SSE"] = "SSE";
  return ServerType2;
})(ServerType || {});
var startStdioServer = async ({
  initStdioServer,
  initStreamClient,
  serverType,
  transportOptions = {},
  url
}) => {
  let transport;
  switch (serverType) {
    case "SSE" /* SSE */:
      transport = new SSEClientTransport(new URL(url), transportOptions);
      break;
    default:
      transport = new StreamableHTTPClientTransport(
        new URL(url),
        transportOptions
      );
  }
  const streamClient = initStreamClient ? await initStreamClient() : new Client(
    {
      name: "mcp-proxy",
      version: "1.0.0"
    },
    {
      capabilities: {}
    }
  );
  await streamClient.connect(transport);
  const serverVersion = streamClient.getServerVersion();
  const serverCapabilities = streamClient.getServerCapabilities();
  const stdioServer = initStdioServer ? await initStdioServer() : new Server(serverVersion, {
    capabilities: serverCapabilities
  });
  const stdioTransport = new StdioServerTransport();
  await stdioServer.connect(stdioTransport);
  await proxyServer({
    client: streamClient,
    server: stdioServer,
    serverCapabilities
  });
  return stdioServer;
};

// src/tapTransport.ts
var tapTransport = (transport, eventHandler) => {
  const originalClose = transport.close.bind(transport);
  const originalOnClose = transport.onclose?.bind(transport);
  const originalOnError = transport.onerror?.bind(transport);
  const originalOnMessage = transport.onmessage?.bind(transport);
  const originalSend = transport.send.bind(transport);
  const originalStart = transport.start.bind(transport);
  transport.close = async () => {
    eventHandler({
      type: "close"
    });
    return originalClose?.();
  };
  transport.onclose = async () => {
    eventHandler({
      type: "onclose"
    });
    return originalOnClose?.();
  };
  transport.onerror = async (error) => {
    eventHandler({
      error,
      type: "onerror"
    });
    return originalOnError?.(error);
  };
  transport.onmessage = async (message) => {
    eventHandler({
      message,
      type: "onmessage"
    });
    return originalOnMessage?.(message);
  };
  transport.send = async (message) => {
    eventHandler({
      message,
      type: "send"
    });
    return originalSend?.(message);
  };
  transport.start = async () => {
    eventHandler({
      type: "start"
    });
    return originalStart?.();
  };
  return transport;
};
export {
  InMemoryEventStore,
  ServerType,
  proxyServer,
  startHTTPServer,
  startStdioServer,
  tapTransport
};
//# sourceMappingURL=index.js.map