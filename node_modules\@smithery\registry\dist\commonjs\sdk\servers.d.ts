import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { PageIterator } from "../types/operations.js";
export declare class Servers extends ClientSDK {
    /**
     * List Servers
     *
     * @remarks
     * Retrieves a paginated list of all available servers with optional filtering.
     */
    list(request: operations.ListServersRequest, options?: RequestOptions): Promise<PageIterator<operations.ListServersResponse, {
        page: number;
    }>>;
    /**
     * Get Server
     *
     * @remarks
     * Retrieves detailed information about a specific server by its qualified name.
     */
    get(request: operations.GetServerRequest, options?: RequestOptions): Promise<components.ServerDetailResponse>;
}
//# sourceMappingURL=servers.d.ts.map