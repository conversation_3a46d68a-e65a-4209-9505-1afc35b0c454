{"name": "@smithery/registry", "version": "0.3.7", "author": "Speakeasy", "type": "module", "tshy": {"sourceDialects": ["@smithery/registry/source"], "exports": {".": "./src/index.ts", "./package.json": "./package.json", "./types": "./src/types/index.ts", "./models/errors": "./src/models/errors/index.ts", "./models/components": "./src/models/components/index.ts", "./models/operations": "./src/models/operations/index.ts", "./*.js": "./src/*.ts", "./*": "./src/*.ts"}}, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/smithery-ai/sdk.git", "directory": "typescript/registry"}, "scripts": {"lint": "eslint --cache --max-warnings=0 src", "build": "tshy", "prepublishOnly": "npm run build"}, "peerDependencies": {"zod": ">= 3"}, "devDependencies": {"@eslint/js": "^9.19.0", "eslint": "^9.19.0", "globals": "^15.14.0", "tshy": "^2.0.0", "typescript": "^5.4.5", "typescript-eslint": "^8.22.0", "zod": "^3.23.4"}, "dependencies": {}, "exports": {".": {"import": {"@smithery/registry/source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json", "./types": {"import": {"@smithery/registry/source": "./src/types/index.ts", "types": "./dist/esm/types/index.d.ts", "default": "./dist/esm/types/index.js"}, "require": {"types": "./dist/commonjs/types/index.d.ts", "default": "./dist/commonjs/types/index.js"}}, "./models/errors": {"import": {"@smithery/registry/source": "./src/models/errors/index.ts", "types": "./dist/esm/models/errors/index.d.ts", "default": "./dist/esm/models/errors/index.js"}, "require": {"types": "./dist/commonjs/models/errors/index.d.ts", "default": "./dist/commonjs/models/errors/index.js"}}, "./models/components": {"import": {"@smithery/registry/source": "./src/models/components/index.ts", "types": "./dist/esm/models/components/index.d.ts", "default": "./dist/esm/models/components/index.js"}, "require": {"types": "./dist/commonjs/models/components/index.d.ts", "default": "./dist/commonjs/models/components/index.js"}}, "./models/operations": {"import": {"@smithery/registry/source": "./src/models/operations/index.ts", "types": "./dist/esm/models/operations/index.d.ts", "default": "./dist/esm/models/operations/index.js"}, "require": {"types": "./dist/commonjs/models/operations/index.d.ts", "default": "./dist/commonjs/models/operations/index.js"}}, "./*.js": {"import": {"@smithery/registry/source": "./src/*.ts", "types": "./dist/esm/*.d.ts", "default": "./dist/esm/*.js"}, "require": {"types": "./dist/commonjs/*.d.ts", "default": "./dist/commonjs/*.js"}}, "./*": {"import": {"@smithery/registry/source": "./src/*.ts", "types": "./dist/esm/*.d.ts", "default": "./dist/esm/*.js"}, "require": {"types": "./dist/commonjs/*.d.ts", "default": "./dist/commonjs/*.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js"}