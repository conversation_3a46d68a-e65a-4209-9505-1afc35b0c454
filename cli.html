<!DOCTYPE html><html lang="en">
<!-- Mirrored from docs.vast.ai/cli by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 12 Jul 2025 12:01:17 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head><meta charSet="utf-8" data-next-head=""/><title data-next-head="">CLI - Guides</title><meta name="description" content="Guides" data-next-head=""/><meta name="image" content="" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:title" content="CLI - Guides" data-next-head=""/><meta name="twitter:description" content="Guides" data-next-head=""/><meta name="twitter:image" content="" data-next-head=""/><meta property="og:title" content="CLI - Guides" data-next-head=""/><meta property="og:type" content="product" data-next-head=""/><meta property="og:image" content="" data-next-head=""/><meta property="og:description" content="Guides" data-next-head=""/><meta name="language" content="en" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=6.0, user-scalable=1" data-next-head=""/><link rel="shortcut icon" href="../images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/K8kL9zPy3Yuuym96Ony5l_vast-social-profile-photo.png" data-next-head=""/><script type="text/javascript" data-next-head="">
window.$crisp=[];
window.CRISP_WEBSITE_ID="734d7b1a-86fc-470d-b60a-f6d4840573ae";

// Set up the ready trigger before loading Crisp
window.CRISP_READY_TRIGGER = function() {
    // Set current page URL as session data
    $crisp.push(["set", "session:data", [[
        "current_page", window.location.href
    ]]]);
};

(function(){
    d=document;
    s=d.createElement("script");
    s.src="../client.crisp.chat/l.js";
    s.async=1;
    d.getElementsByTagName("head")[0].appendChild(s);
})();

// Also track URL changes if you have a single-page application
window.addEventListener('popstate', function() {
    if (window.$crisp.is("website:available")) {
        $crisp.push(["set", "session:data", [[
            "current_page", window.location.href
        ]]]);
    }
});
</script><script async="" src="../external.html?link=https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG" data-next-head=""></script><script data-next-head="">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-DG15WC8WXG');
</script><style data-next-head="">
  /* Adjust chat button position */
  .crisp-client {
    --crisp-position-reverse: 1 !important; /* Left side positioning */
    --crisp-button-color: #4B5563 !important; /* Custom button color */
  }
  
  
  
  /* Optional: Hide chat widget on mobile */
  @media (max-width: 768px) {
    .crisp-client {
      display: none !important;
    }
  }
</style><link rel="preconnect" href="../external.html?link=https://cdn.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://cdnjs.cloudflare.com/"/><link rel="preconnect" href="../external.html?link=https://fonts.googleapis.com/"/><link rel="preconnect" href="../external.html?link=https://fonts.gstatic.com/" crossorigin=""/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="apple-mobile-web-app-capable" content="yes"/><link href="../external.html?link=https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/><link type="text/css" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/public/normalize.css"/><style data-emotion-css="1uiwhkr 1ro3qzv 1i6d6vk rbmaoo ydpz48 1dgdik1 v5vzns 1fl8vw4 u4v8xq 1t9hfmb roftna 1b94hea 1ydoibe whnwm4 w36zip 9whsf3 1b4fjyx ypjd7c t7putw 1tv6ror 130k67h vxmz0c 1ys6oi3 1srr18y p1cn1o 64jqxe qizsd 5mgflz 10e4hpw blbbuw hlwo6c 49mvvd 1149rud 17q9201 hkc7xg 7lplk8 cjepy5 1724ov3 xw1pqi 87h1z1 1whh9et 1qet1p ggw9mt sn6axx 1tx6k1l 4zrcm0 12n8vte rw7xsu jgdyep x4c2bn 1p004sg 71vex2 4dpz50 5knetf ay17xk j88me8 1iamho1 1bz6gk4 tkabts 1ms681s esg2bf 1lljsge 1306ufs wufc4w gn5mcr n6a6j4 dild6v vkd4 ffvq9h 1c21mnf 1bgl7dd v9rovc pnt15a 18h9xkc 3kfn3n 1gbzpq5 euksn3 1u12gcq 13g7z6x jknmwy ghq01l 1yku8fa 1l091ft tfaolw 13u1zyh 13qucz6 1321q7x 1gygdlm fzs3dc 1pjpc1 veby95 1of59pv 1bteluw q5fwlo 11o1lfo 7mn3wt vyvlbq 1y1dhpv 1xi6o7y 6zvay4 wwft1u 8k8wai 1ngj57b jjsjcg 18wotnf 9pg19u cn6axj 1nqd0y1 rxerzz 7sluno 1r7wx12 l81hax 1ea3n21 1he04i1 1qy29vz 1o2veex 1nwilma w25jvf 1wsux5p x7qujw 63d9l0 1gradx1 1v9g95b ee07zz 11qudoy 6q3mca 1k5vv28 s33lbd re0pe fe43hj mcv8nk u0dj5y 4779de 1os98ik 1ecznfq 14se2g5 bmwl98 zj4zjz 1k7mldi 1iu3i8p rn2g6o twhrqb cekyvf 14elb1x 1qwrbaq pm8ktx 3fu96b mhu4l3 hqfggw z6hwls 1uedzoz tomm9q 1ezqz7u 1wbwnsm 1fmx8db 1ob8ogp 1xofzud 2ooi2b 19zi4nq 1mh5ntg 1dlirv8 mh68s5 1bdk9n3 11263bm za4e7k yi4an8 w6crv0 1i6ajij ae5q20 18yrr1h 8he6ch ex4rbf 1fnanxh r0k8ai 1p6ltz1 hw7h1m o1kdlj 1epjdjo od8u8e 1ggsefg 1myj5jx 16290om b3zxhy 16kn019 13dgy09 jl0nxc f7ifu9 19zrndo euad1m 15slsu4 1aja3f2 hrn9nv ppe5mx 15y0cxp blrl92 12f3pwh 1uye9f0 63ihqe nfhqnj 11a4vci wkw5oq 1b3nikh c2lo6u zfdg47 gie19p qxkkhs 1nm73v8 q8renl 95er94 1ink54o ju1r6u fd4syp jvtmgo va58lw 1u5t5yx 1nlzxni 1u0lf8v ao6spu am5scv 1nsqb76 tplma3 77goi7 eqvarw 1jzmuxk 1mb92vv 1cjyxhb 18wgnvz kkia1w 1apyou2 1sirhxu 1d8xnr6 1mp63us 14jremo l75tm1 1u0h1r2 t78dr6 dvcgkz frjp74 1d5czsu jk60lu 1wsgm2l 1izd2ee h3mayo 11tke5i 3b4wvg 135mz3m 14xxzio 1jxigfm 1potje1 na5ygo a6hlge 1uprvsl 4wxci8 6va3et ttmdd3 kx3ph9 bkpjdf ifzjjm zdyskf wxglv8 1qfaqb7 s2lg7y gb732g 1q4xixa 18pr5mp g8bmer yfh3wc a2aim 1cjgca1 1vcs6o7 j2z86 1ieqxtj 1kagpwb 9qukwk 6sjddz 1fy1f3j sr2yzv yh5qkw 16jwaqy 1pe94ip uglexu 1xcrlpv wi7rvl 1bxrxj0 xbugmj 1xp4b63 143nwtb 17z7aj8 esdacs 1kaono8 uiowgc lty3ak ub2tfp q5fyyv qf3nol 1i7f6np fpivd w46nsu 1ascyj2 3802nw gwo6kx 19a7cd6 yyj7by it1cmi 1kg35zq 1ekr0xj 1ipukr5 czjwz7 11ehpp2 1u85wfq 1ahz8dq 14s6dul 14sirbb 16bdtp4 v990xg pchpie e9x7n0 5z0b09 13ub60e 1kqr105">:root{--smokey-grey:#d1d5db;--blue:#2166ae;--dark-blue:#0C121D;--red:#E95C5C;--grey:#0C121D;--light-grey:#9ca3af;--very-light-grey:#d1d5db;--smokey-grey-background:#f3f4f6;--line-grey:#d1d5db;}.graphiql-container .topBar{height:auto!important;}.doc-explorer-back{overflow:hidden;}.doc-explorer-title{overflow:hidden;}@-webkit-keyframes animation-1i6d6vk{0%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0.7);}70%{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);transform:scale(1);box-shadow:0 0 0 10px rgba(255, 82, 82, 0);}100%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0);}}@keyframes animation-1i6d6vk{0%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0.7);}70%{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);transform:scale(1);box-shadow:0 0 0 10px rgba(255, 82, 82, 0);}100%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0);}}:root{--ab-accent-color:#2166ae;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}.css-ydpz48{letter-spacing:-0.1rem;}.css-1dgdik1{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);}.css-v5vzns:hover{stroke:#2166ae;}.css-1fl8vw4{color:#ffffff;background-color:#2166ae!important;}.css-1fl8vw4:hover{background-color:#194d83!important;}.css-u4v8xq{height:inherit;max-width:inherit;}.css-1t9hfmb{border-color:#2166ae;}.css-1t9hfmb:hover{background-color:#eff6fd;}.css-roftna{border-color:transparent;}.css-1b94hea{margin-top:14px;margin-bottom:14px;font-weight:600;}.css-1ydoibe{max-width:calc(100% - 18px);}.css-whnwm4{margin-top:14px;margin-bottom:14px;font-weight:400;}.css-w36zip{margin-top:28px;margin-bottom:14px;font-weight:600;}.css-9whsf3{max-width:100%;}.css-1b4fjyx{color:#2166ae!important;}.css-ypjd7c{-webkit-transition:border 200ms ease-out;transition:border 200ms ease-out;}.css-ypjd7c:hover{color:#2166ae;}.css-ypjd7c:hover .navigation-arrow{stroke:#2166ae;}:root{--ab-accent-color:#171717;--ab-bg-color:#ececec;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006699;--ab-bg-color:#cbf0ff;--ab-hover-bg-color:#e4f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7950F2;--ab-bg-color:#F3F0FF;--ab-hover-bg-color:#f7f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b36f5;--ab-bg-color:#ebeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#F2F7FD;--ab-hover-bg-color:#eef4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5c98ce;--ab-bg-color:#e4ecf4;--ab-hover-bg-color:#f0f4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c8252c;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e88004;--ab-bg-color:#fbe7d2;--ab-hover-bg-color:#fdf4eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B9132F;--ab-bg-color:#fde9e9;--ab-hover-bg-color:#fef3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f47c6a;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#38645a;--ab-bg-color:#F4EFE9;--ab-hover-bg-color:#f8f4ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#93c741;--ab-bg-color:#e4efd9;--ab-hover-bg-color:#eff6e9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4a61e0;--ab-bg-color:#eaedfd;--ab-hover-bg-color:#f4f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de23fc;--ab-bg-color:#fce6fc;--ab-hover-bg-color:#fdf0fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4D87E2;--ab-bg-color:#e6ecfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6239EB;--ab-bg-color:#F1EAFE;--ab-hover-bg-color:#f7f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#1f9cff;--ab-hover-bg-color:#ecf6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#543af4;--ab-bg-color:#e9e9fd;--ab-hover-bg-color:#f3f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#27ae60;--ab-bg-color:#c6f5de;--ab-hover-bg-color:#defbed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6921ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b98ce;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#F4EAF6;--ab-hover-bg-color:#f8f2f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ae8221;--ab-bg-color:#f9eac7;--ab-hover-bg-color:#fcf4e0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4F2ba7;--ab-bg-color:#311c6b;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#155d84;--ab-bg-color:#ddedfb;--ab-hover-bg-color:#f1f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#45ada0;--ab-bg-color:#d9efef;--ab-hover-bg-color:#edf7f7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7e5efd;--ab-bg-color:#eeeafd;--ab-hover-bg-color:#f6f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1463ff;--ab-bg-color:#e6eeff;--ab-hover-bg-color:#f0f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#10c180;--ab-bg-color:#b9f7df;--ab-hover-bg-color:#dcfbed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#13b0fc;--ab-bg-color:#d1f1fc;--ab-hover-bg-color:#e5f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1463ff;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1f2fa3;--ab-bg-color:#e7eafc;--ab-hover-bg-color:#f5f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4750EF;--ab-bg-color:#C9D8FD;--ab-hover-bg-color:#f1f4fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7d40cd;--ab-bg-color:#d2bee4;--ab-hover-bg-color:#f8f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#911211;--ab-bg-color:#c3a0a2;--ab-hover-bg-color:#fbf5f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#35b234;--ab-bg-color:#ddf1dd;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4c4c4c;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#EA1D25;--ab-bg-color:#fce5e5;--ab-hover-bg-color:#fef4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0051a4;--ab-bg-color:#dbedff;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#664FEE;--ab-bg-color:#664fee1e;--ab-hover-bg-color:rgba(244,244,254,0.12);--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#2da295;--ab-bg-color:#d4f0ee;--ab-hover-bg-color:#ecf7f7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#01d093;--ab-bg-color:#91fede;--ab-hover-bg-color:#cdfef0;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#199aab;--ab-bg-color:#d4f1fa;--ab-hover-bg-color:#e8f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6262F5;--ab-bg-color:#e9e9fd;--ab-hover-bg-color:#f3f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#97c634;--ab-bg-color:#e4efc9;--ab-hover-bg-color:#f2f6e5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#62c7c8;--ab-bg-color:#daefef;--ab-hover-bg-color:#eaf6f6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#180459;--ab-bg-color:#d5d0ef;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#053cfe;--ab-bg-color:#e6edfe;--ab-hover-bg-color:#f0f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#116530;--ab-bg-color:#c0f7d5;--ab-hover-bg-color:#defbe7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a23aff;--ab-bg-color:#f4e9ff;--ab-hover-bg-color:#f9f3ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006d00;--ab-bg-color:#a9ffa9;--ab-hover-bg-color:#d6ffd6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6e01fa;--ab-bg-color:#f3e8fe;--ab-hover-bg-color:#f8f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc4c02;--ab-bg-color:#fde6dd;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3483DC;--ab-bg-color:#FFFFFF;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8b6dff;--ab-bg-color:#efeaff;--ab-hover-bg-color:#f7f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5056ea;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21aea1;--ab-bg-color:#e3fdf8;--ab-hover-bg-color:#d9fdf6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#55a51c;--ab-bg-color:#d0f6b1;--ab-hover-bg-color:#e8fbd9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070740;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#45ada0;--ab-bg-color:#ececf7;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6f00ff;--ab-bg-color:#f3e6ff;--ab-hover-bg-color:#faf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c79817;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6550ff;--ab-bg-color:#edebff;--ab-hover-bg-color:#f6f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#33285d;--ab-bg-color:#eaeaf6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#333366;--ab-bg-color:#eaeaf6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#194896;--ab-bg-color:#e5ebfc;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#191847;--ab-bg-color:#ececf7;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0092;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6a4ee1;--ab-bg-color:#e8e8f9;--ab-hover-bg-color:#f4f4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff6f61;--ab-bg-color:#ffe6e3;--ab-hover-bg-color:#fff4f2;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#003655;--ab-bg-color:#d2f0ff;--ab-hover-bg-color:#e6f8ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff361d;--ab-bg-color:#ff361d85;--ab-hover-bg-color:rgba(255,241,239,0.52);--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0C121D;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f5a400;--ab-bg-color:#ffe9b4;--ab-hover-bg-color:#fff3d7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#004d47;--ab-bg-color:#ade0bc;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2d88e9;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c91589;--ab-bg-color:#fce4f0;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8566ab;--ab-bg-color:#f1ebf7;--ab-hover-bg-color:#f6f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#096dd9;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#96c534;--ab-bg-color:#e3efc8;--ab-hover-bg-color:#f1f6e4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ed3831;--ab-bg-color:#fce5e5;--ab-hover-bg-color:#fef4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5145bf;--ab-bg-color:#e9e9f6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4ab423;--ab-bg-color:#c9f7ba;--ab-hover-bg-color:#e5fbdd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#245dc1;--ab-bg-color:#e8ebff;--ab-hover-bg-color:#f2f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2ed167;--ab-bg-color:#EBFFF4;--ab-hover-bg-color:#d2ffe5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8b5cf7;--ab-bg-color:#ede8fc;--ab-hover-bg-color:#f5f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a04a21;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#009e74;--ab-bg-color:#8fffde;--ab-hover-bg-color:#cbfff0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FA530B;--ab-bg-color:#D9D2C3;--ab-hover-bg-color:#f7f4f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#574feb;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#244481;--ab-hover-bg-color:#f2f5f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0bb599;--ab-bg-color:#b7f7ee;--ab-hover-bg-color:#dafbf5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3b82f6;--ab-bg-color:#e5ebfc;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3074d9;--ab-bg-color:#e1ecfc;--ab-hover-bg-color:#f0f5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f47715;--ab-bg-color:#fae7d3;--ab-hover-bg-color:#fdf4ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fe5d27;--ab-bg-color:#fee6db;--ab-hover-bg-color:#fef3ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae6a;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1d4354;--ab-bg-color:#e4edf4;--ab-hover-bg-color:#f0f4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#007235;--ab-bg-color:#9affca;--ab-hover-bg-color:#ccffe6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9649b6;--ab-bg-color:#efe9f6;--ab-hover-bg-color:#f8f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0c0c0c;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1e94e6;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5646C0;--ab-bg-color:#ddf1dd;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FF5000;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e41689;--ab-bg-color:#fce4f0;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2bccbd;--ab-bg-color:#baf6e8;--ab-hover-bg-color:#ddfbf4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#004b87;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00c0ff;--ab-bg-color:#cdf0ff;--ab-hover-bg-color:#e6f8ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f84b0b;--ab-bg-color:#ffe7cd;--ab-hover-bg-color:#fff3e6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f98858;--ab-bg-color:#fbe6df;--ab-hover-bg-color:#fdf2ee;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f84b0b;--ab-bg-color:#fce8e2;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#160a70;--ab-bg-color:#ededfd;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb609e;--ab-bg-color:#fefbfd;--ab-hover-bg-color:#fcf3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0050a0;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#f7e8fc;--ab-hover-bg-color:#fbf2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4da0ff;--ab-bg-color:#deefff;--ab-hover-bg-color:#edf6ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de1a1c;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1b6aee;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5D29FF;--ab-bg-color:#BDA7FF;--ab-hover-bg-color:#f6f2ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2b2b2b;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2611e7;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#223f99;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#29294E;--ab-bg-color:#1CCDD8;--ab-hover-bg-color:#dcfbfb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6421ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f28e00;--ab-bg-color:#ffe7ca;--ab-hover-bg-color:#fff4e8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00162b;--ab-bg-color:#daedff;--ab-hover-bg-color:#eef7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2366ad;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#016a4b;--ab-bg-color:#94fedf;--ab-hover-bg-color:#d0fef1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#192d89;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3694fc;--ab-bg-color:#94c2fa;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1D7EA9;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#74b01a;--ab-bg-color:#d1f6ac;--ab-hover-bg-color:#e8fad4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#001267;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff6600;--ab-bg-color:#ffe7d7;--ab-hover-bg-color:#fff3eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0069ff;--ab-bg-color:#dcedff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4360a4;--ab-bg-color:#e9e9f6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1c48d9;--ab-bg-color:#e6ebfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0f50;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c490d1;--ab-bg-color:#f0e8f5;--ab-hover-bg-color:#f7f4fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc6201;--ab-bg-color:#fee6db;--ab-hover-bg-color:#fef3ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae35;--ab-bg-color:#bdf8cc;--ab-hover-bg-color:#e0fce7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#16a1e1;--ab-bg-color:#d1f0fa;--ab-hover-bg-color:#e5f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#863CFF;--ab-bg-color:#F6F0FF;--ab-hover-bg-color:#f9f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#29cf92;--ab-bg-color:#FF0079;--ab-hover-bg-color:#fff0f8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#2b7fd7;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#d45e30;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ae217e;--ab-bg-color:#fce5f2;--ab-hover-bg-color:#fef4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1888af;--ab-bg-color:#d2f0fa;--ab-hover-bg-color:#e6f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#58cd8a;--ab-bg-color:#dcf0e7;--ab-hover-bg-color:#ecf7f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2aa3dc;--ab-bg-color:#d9effb;--ab-hover-bg-color:#edf7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#328af6;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#24bf43;--ab-bg-color:#bef8cd;--ab-hover-bg-color:#e1fce8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1cce86;--ab-bg-color:#bef8db;--ab-hover-bg-color:#dcfbec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8cc63f;--ab-bg-color:#e3eed7;--ab-hover-bg-color:#f1f7eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6A47FF;--ab-bg-color:#F6F3FF;--ab-hover-bg-color:#F6F3FF;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fd5f03;--ab-bg-color:#ffe2d3;--ab-hover-bg-color:#fff2ec;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f6922e;--ab-bg-color:#fae7d3;--ab-hover-bg-color:#fdf4ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070A0D;--ab-bg-color:#F6F8F9;--ab-hover-bg-color:#f3f6f7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#cc0100;--ab-bg-color:#ffe6e5;--ab-hover-bg-color:#fff5f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0068ac;--ab-bg-color:#d4efff;--ab-hover-bg-color:#e8f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#086454;--ab-bg-color:#b5f7ec;--ab-hover-bg-color:#d8fbf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1563ff;--ab-bg-color:#e2ebff;--ab-hover-bg-color:#f1f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c57a1c;--ab-bg-color:#fbe9d6;--ab-hover-bg-color:#fdf3ea;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1f9387;--ab-bg-color:#c1f5ea;--ab-hover-bg-color:#dbfbf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb6334;--ab-bg-color:#134dce;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#9146ED;--ab-bg-color:#e7e513;--ab-hover-bg-color:#f8f8bd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#402E96;--ab-bg-color:#F5F5FA;--ab-hover-bg-color:#F5F5FA;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#85b1d5;--ab-bg-color:#e5ecf4;--ab-hover-bg-color:#f1f5f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#005eb8;--ab-bg-color:#dbedff;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1ecbb6;--ab-bg-color:#eaf5f5;--ab-hover-bg-color:#edf7f7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00a1d8;--ab-bg-color:#cef1ff;--ab-hover-bg-color:#e7f8ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4bc2fa;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0000;--ab-bg-color:#ffe6e6;--ab-hover-bg-color:#fff5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e1676a;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#be2026;--ab-bg-color:#cfcfce;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4692c0;--ab-bg-color:#e6edf4;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6e69f3;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#56296f;--ab-bg-color:#5a96f0;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0772ec;--ab-bg-color:#72ffff;--ab-hover-bg-color:#c2ffff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb609e;--ab-bg-color:#FEFBFD;--ab-hover-bg-color:#fcf3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#339af0;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0096DC;--ab-bg-color:#E5EFF7;--ab-hover-bg-color:#f1f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f06292;--ab-bg-color:#fde9ee;--ab-hover-bg-color:#fef3f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e55775;--ab-bg-color:#fce5eb;--ab-hover-bg-color:#fef4f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9ec500;--ab-bg-color:#cafc00;--ab-hover-bg-color:#e7ff84;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00B373;--ab-bg-color:#F9FAE4;--ab-hover-bg-color:#f6f7d8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FF6E00;--ab-bg-color:#E6E6E6;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#616194;--ab-bg-color:#ececf4;--ab-hover-bg-color:#f5f5fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#365742;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#24cca7;--ab-bg-color:#b6f7e7;--ab-hover-bg-color:#d9fbf2;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ca1289;--ab-bg-color:#fce4f2;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2A9D8F;--ab-bg-color:#efefef;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#87ca29;--ab-bg-color:#d4f5b2;--ab-hover-bg-color:#e8fad5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f55cc;--ab-bg-color:#ebecf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb6334;--ab-bg-color:#fbe6de;--ab-hover-bg-color:#fef5f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4f2ba7;--ab-bg-color:#311c6b;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#5fa846;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#04c9b5;--ab-bg-color:#2c2c2c;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0b382b;--ab-bg-color:#c6f4e7;--ab-hover-bg-color:#defaf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0079c1;--ab-bg-color:#d0f1ff;--ab-hover-bg-color:#e4f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3bb76b;--ab-bg-color:#dbf0e6;--ab-hover-bg-color:#ebf7f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae62;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#1100A7;--ab-hover-bg-color:#f8f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#6c26db;--ab-bg-color:#ede8fc;--ab-hover-bg-color:#f5f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#225d3a;--ab-bg-color:#f5f3ef;--ab-hover-bg-color:#f7f5f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#089488;--ab-bg-color:#ddd;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#272a3b;--ab-bg-color:#e9ecf0;--ab-hover-bg-color:#f2f4f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0f2db3;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#419BF9;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b5b5b5;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#121313;--ab-bg-color:#e8ebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#911211;--ab-bg-color:#cd1b18;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#ea0e89;--ab-bg-color:#fddaeb;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc3229;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0083b3;--ab-bg-color:#ccf0ff;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#007b86;--ab-bg-color:#eeeeee;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9542e0;--ab-bg-color:#f3e9fd;--ab-hover-bg-color:#f8f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1a73e8;--ab-bg-color:#e2ebfc;--ab-hover-bg-color:#f1f4fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5d1996;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3a4958;--ab-bg-color:#e7ecf0;--ab-hover-bg-color:#f3f6f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#625DEC;--ab-bg-color:#EFEFFD;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#191a1a;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3c9a35;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3E95D1;--ab-bg-color:#deecf9;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0745de;--ab-bg-color:#f9f9f9;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#002BFF;--ab-hover-bg-color:#f5f6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#9B69FF;--ab-bg-color:#efefef;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#0568f7;--ab-hover-bg-color:#f2f7fe;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#ed7919;--ab-bg-color:#fbe9d7;--ab-hover-bg-color:#fdf4eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e51743;--ab-bg-color:#fce6e9;--ab-hover-bg-color:#fef5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae72;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8353f9;--ab-bg-color:#eee9fd;--ab-hover-bg-color:#f5f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#212121;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#657220;--ab-bg-color:#D9F400;--ab-hover-bg-color:#eeff4f;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ffa200;--ab-bg-color:#ffe9b4;--ab-hover-bg-color:#fff3d7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f2aa3;--ab-bg-color:#eeecf9;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000116;--ab-bg-color:#e8e9ff;--ab-hover-bg-color:#f2f3ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2470bf;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ee8031;--ab-bg-color:#fbe6db;--ab-hover-bg-color:#fdf3ef;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1100A7;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#52a9c1;--ab-bg-color:#deeff1;--ab-hover-bg-color:#eef7f8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6eb3f3;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#d95729;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1E3D59;--ab-bg-color:#BBF245;--ab-hover-bg-color:#eef9cc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4f90d3;--ab-bg-color:#e3eef8;--ab-hover-bg-color:#eff5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2485cc;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#402e96;--ab-bg-color:#ebebf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2350af;--ab-bg-color:#e6ecfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1568E4;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000116;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff361d;--ab-bg-color:#ffe7c3;--ab-hover-bg-color:#fff3e1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070b46;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#F72585;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f2a808;--ab-bg-color:#f8ebc1;--ab-hover-bg-color:#fbf4df;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#226da7;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fda22f;--ab-bg-color:#02012D;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0e71cc;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#141414;--ab-bg-color:#ececec;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#db332c;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FA3803;--ab-bg-color:#fce6dd;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de4a00;--ab-bg-color:#ffe5d9;--ab-hover-bg-color:#fff3ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2365a9;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b64ee;--ab-bg-color:#ececfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B0820C;--ab-bg-color:#f8eabe;--ab-hover-bg-color:#fbf4dc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#96c534;--ab-bg-color:#394915;--ab-hover-bg-color:#f1f6e4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#20A8D8;--ab-hover-bg-color:#e8f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fd0084;--ab-bg-color:#ffe4f2;--ab-hover-bg-color:#fff3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00A2E9;--ab-bg-color:#DBF4FF;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00255f;--ab-bg-color:#e1ecff;--ab-hover-bg-color:#f0f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2B8E94;--ab-bg-color:#f4f8fb;--ab-hover-bg-color:#f0f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2D2836;--ab-bg-color:#FBF9FE;--ab-hover-bg-color:#f5f1fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f1602b;--ab-bg-color:#fbe6df;--ab-hover-bg-color:#fdf2ee;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#008179;--ab-bg-color:#77fff7;--ab-hover-bg-color:#c7fffa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#db372d;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6228C0;--ab-bg-color:#eeebf7;--ab-hover-bg-color:#f4f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1E8AAE;--ab-bg-color:#EDF5F7;--ab-hover-bg-color:#f1f7f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#158C7B;--ab-bg-color:#eaf0f7;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#278b9b;--ab-bg-color:#d6f0f5;--ab-hover-bg-color:#eaf7fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1501ff;--ab-bg-color:#eeecff;--ab-hover-bg-color:#f7f6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff93c1;--ab-bg-color:#ffe3f1;--ab-hover-bg-color:#fff2f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7516D8;--ab-bg-color:#f1e6fc;--ab-hover-bg-color:#f9f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#489be5;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f66334;--ab-bg-color:#f8fafc;--ab-hover-bg-color:#f0f4f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a4c417;--ab-bg-color:#def49a;--ab-hover-bg-color:#eef9cc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7921ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#263B2C;--ab-bg-color:#e1ede7;--ab-hover-bg-color:#f0f7f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#14467a;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#276d9b;--ab-bg-color:#e2edf8;--ab-hover-bg-color:#eef5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff5722;--ab-bg-color:#ffe6db;--ab-hover-bg-color:#fff1ea;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#DC143C;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f2a928;--ab-bg-color:#f9e9c8;--ab-hover-bg-color:#fcf3e1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3e69bc;--ab-bg-color:#ebecf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#30cabb;--ab-bg-color:#c9f4ec;--ab-hover-bg-color:#e1faf5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6639b7;--ab-bg-color:#ede8f5;--ab-hover-bg-color:#f6f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ee9cc9;--ab-bg-color:#10092a;--ab-hover-bg-color:#f5f3fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0080ff;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2e8ef0;--ab-bg-color:#ddecfb;--ab-hover-bg-color:#f1f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e6007e;--ab-bg-color:#ffe6f3;--ab-hover-bg-color:#fff0f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00cc9f;--ab-bg-color:#8bffe4;--ab-hover-bg-color:#ccfff1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4ab848;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}</style><link rel="stylesheet" href="../cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"/><link rel="preload" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/css/b69acd3085e4480a.css" as="style"/><link rel="stylesheet" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/css/b69acd3085e4480a.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/polyfills-42372ed130431b0a.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5810.e6a0789a46dba285.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1cc2734a-a2bd46b48dcfb414.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1976-3a1e16ed39f257ff.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4747-66e20966a06d24d8.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7764-f3b14f364a0d1b52.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/416-97c1497291412984.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4225-22f657dc0f739d91.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1539-00ecead0d9ee55cc.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/6652-fc6900588edd6271.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5176-afc4cc13abd1d35c.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9370-c81561713f82c466.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/111-6561208ec0d2be20.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7020-baaec7260f5e6fc1.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9292-f2fe7a82877d47bf.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5089.32630609a654ddd8.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5159-41323e3b79d2d278.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/509-bc3789384db0db85.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4638-8cca8ce68235216a.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5118-075b126111014c7d.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4024-970f73672e22cd2c.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7968-7d9b753edf6c1f99.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7187-496bdd6418e0be57.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3395.9eef5e529d29ff6f.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/8992-017f87979f45ecc7.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2541.bb0abb704527903d.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3156.a91f1bbd6e134c38.js"></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/webpack-91f79a938b1b1dfb.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/framework-5648639edb99e1bd.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/main-2255b1a2dca1d2dd.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/pages/_app-73995db85270ee67.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/48e0f7fa-2695100ece8812c4.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2edb282b-e1fef83e163cf249.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9a20ca01-4cbfe13ddba0b528.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/91bbf309-07ffab0a9657d31d.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/8bd53eb9-3850191f2ece3222.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3514-c52c7aad05a7e48f.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7422-4c22c8698c393ea5.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3424-375f0b69ace252f6.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9442-be30be087aa68cc6.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/562-dc8635dfded4d697.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5913-96d7a578979c260a.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/6342-6d0fbb2ac6aa8697.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2893-f98caa64b2e95191.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9849-f5651e5e76c2f026.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/pages/public/%5b%5b...slug%5d%5d-26ef0790e35d5b2c.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/dBe88brPNkUEoyQSYuIle/_buildManifest.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/dBe88brPNkUEoyQSYuIle/_ssgManifest.js" defer=""></script><meta name="sentry-trace" content="4bb30f276f204df7710330abcf652fa2-8702f339213075f4-1"/><meta name="baggage" content="sentry-environment=live,sentry-release=96c7914b49966e400b4f53ac904fa80945d640a2,sentry-public_key=ad82dc9f244e490fbe33eeb465274185,sentry-trace_id=4bb30f276f204df7710330abcf652fa2,sentry-sample_rate=1,sentry-transaction=GET%20%2Fcli,sentry-sampled=true"/><style id="__jsx-2251086973">#nprogress{pointer-events:none}#nprogress .bar{background:#2166ae;background:--ab-accent-color;position:fixed;z-index:1031;top:0;left:0;width:100%;height:2px}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;-webkit-box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;-moz-box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;opacity:1;-webkit-transform:rotate(3deg)translate(0px,-4px);-ms-transform:rotate(3deg)translate(0px,-4px);-moz-transform:rotate(3deg)translate(0px,-4px);-o-transform:rotate(3deg)translate(0px,-4px);transform:rotate(3deg)translate(0px,-4px)}</style></head><body style="display:block"><div id="__next"><div class="h-full w-full"><link rel="dns-prefetch" href="../external.html?link=https://app.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://app.archbee.com/"/><link rel="dns-prefetch" href="../external.html?link=https://cdn.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://cdn.archbee.com/"/><div class="h-full w-full" style="visibility:hidden"><div data-overlayscrollbars-initialize="" id="docs-scroll-container" class="ab-space ab-collection min-h-full min-w-full h-full dark:text-white fixed top-0 left-0 right-0 bottom-0 print:overflow-visible print:!static print:!h-auto"><div data-overlayscrollbars-contents=""><div class="ab-space-container ab-collection-container flex flex-col w-full justify-center bg-white dark:bg-gray-900"><nav role="navigation" class="ab-top-navbar flex flex-col z-20 sticky top-0 items-center bg-white/70 dark:bg-gray-900/80 border-b border-gray-100 dark:border-gray-800 css-1dgdik1" id="ab-public-nav-header"><div class="w-full mx-auto px-7"><div class="flex items-center justify-between w-full py-4"><a class="ab-public-logo ab-tab-focus flex items-center max-w-[160px] h-[48px] relative justify-start" tabindex="0" href="../external.html?link=https://vast.ai/" aria-label="website logo"><img src="../external.html?link=https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/hY3Z66NYu_wT-evx8EFi5_logo-symbol-dark.svg?format=webp&amp;width=400" class="w-full block mx-auto py-1 object-contain css-u4v8xq" alt="Website logo"/></a><div class="flex items-center print:hidden"><div class="flex items-center text-gray-400 cursor-pointer mx-4"><div class="flex items-center xl:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 cursor-pointer css-v5vzns"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 cursor-pointer stroke-current"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg></div><div class="flex items-center gap-2"><div class="flex items-center justify-center p-2.5 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-400 dark:text-gray-100" type="button" id="radix-:R1kijamm:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg></div></div></div></div><div class="hidden xl:flex flex-1 items-center justify-end gap-2"><a href="../external.html?link=https://cloud.vast.ai/" tabindex="0" class="ab-external-link-btn py-3 px-7 font-semibold rounded-lg text-lg btn-blue relative inline-flex items-center leading-6 css-1fl8vw4">Console</a><a href="../external.html?link=https://discord.gg/hSuEbSQ4X8" tabindex="0" class="ab-external-link-btn py-3 px-7 font-semibold rounded-lg text-lg btn ab-tab-focus text-gray-700 dark:text-gray-100 hover:text-gray-800 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 undefined&#x27;,">Discord</a></div></div><div class="no-print hidden xl:flex w-full pt-1 overflow-x-auto ab-scrollbars"><div class="ab-public-space-links-wrap flex items-center flex-1 -left-4"><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg css-1t9hfmb" role="link" tabindex="0"><div class="w-auto max-w-250 truncate font-semibold text-gray-900 dark:text-white" data-state="closed">Guides</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">Instances</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">Serverless</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">API</div></div></div></div></div></nav><style data-emotion="css 14yoxd">.css-14yoxd{z-index:1200;}</style><div class="ab-space-content ab-collection-content w-full mx-auto relative xl:flex xl:flex-row justify-between px-7 xl:px-0"><div class="ab-tree-navigation no-print sticky flex-col border-r border-gray-100 dark:border-gray-800 w-[360px] hidden xl:flex xl:flex-shrink-0 transition-width transition-slowest ease" style="top:100px;height:calc(100vh - 100px);max-height:calc(100vh - 100px)"><div class="ab-left-nav-public-header flex flex-col w-full px-6 xl:px-7 xl:pt-7 pb-6 !px-0"><div class="flex w-full xl:hidden ab-space-navigation mb-4"><div class="flex flex-col w-full p-6 bg-gray-200 border-b border-gray-300 dark:bg-gray-700 dark:border-gray-600 ab-space-navigation"><div class="flex justify-between font-semibold items-center text-gray-700 dark:text-gray-200"><span>Navigate through spaces</span><div class="text-gray-400 dark:text-gray-500 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="strike-current"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></svg></div></div></div></div><div role="search" class="ab-public-search flex justify-center px-6 xl:px-7"><div class="w-full flex h-11 items-center border pl-4 pr-2 rounded-lg hover:shadow-sm border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-xs"><div class="w-6 h-6 flex items-center justify-center mr-0 lg:mr-2 shrink-0 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="css-v5vzns"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg></div><input class="ab-search-input border-none bg-transparent placeholder-gray-400::placeholder w-full leading-none" placeholder="Search or ask..." value=""/><kbd class="inline-flex justify-center items-center p-2 rounded border border-gray-300 dark:border-gray-700 align-middle leading-3 cursor-default text-gray-600 dark:text-gray-400 border-none bg-gray-50 dark:bg-gray-800 font-medium mx-0 ml-1 shadow-none" style="height:22px;min-width:22px">⌘</kbd><kbd class="inline-flex justify-center items-center p-2 rounded border border-gray-300 dark:border-gray-700 align-middle cursor-default text-gray-600 dark:text-gray-400 text-xs border-none bg-gray-50 dark:bg-gray-800 font-medium mx-0 ml-1 shadow-none" style="height:22px;min-width:22px">K</kbd></div></div></div><div data-overlayscrollbars-initialize="" id="ab-left-nav-public-wrap" class="ab-left-nav-public scroll-smooth flex flex-1 flex-col items-center max-h-full xl:px-7 xl:pb-5"><div data-overlayscrollbars-contents=""><div role="navigation" class="w-full relative overflow-x-hidden px-6 xl:px-0"><div title="Overview"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-1b94hea" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Overview"><span class="truncate">Overview</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron transform rotate-90 text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a><div class="flex-1 max-w-full pl-7 ml-4"><div title="Introduction"><div href="index.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="Introduction"><span class="truncate">Introduction</span></div></a></div></div><div title="QuickStart"><div href="quickstart.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="QuickStart"><span class="truncate">QuickStart</span></div></a></div></div><div title="FAQ"><div href="faq.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="FAQ"><span class="truncate">FAQ</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div></div></div></div><div title="Use Cases"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Use Cases"><span class="truncate">Use Cases</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Teams"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Teams"><span class="truncate">Teams</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Hosting"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Hosting"><span class="truncate">Hosting</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Distributed Computing"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Distributed Computing"><span class="truncate">Distributed Computing</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Console"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Console"><span class="truncate">Console</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Specific GPUs"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Specific GPUs"><span class="truncate">Specific GPUs</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron transform rotate-90 text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a><div class="flex-1 max-w-full pl-7 ml-4"><div title="RTX 5 Series"><div href="jDpX-XUZdy-zKWbAZQPNK.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="RTX 5 Series"><span class="truncate">RTX 5 Series</span></div></a></div></div></div></div></div></div></div><div class="px-[24px] flex my-4"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-full"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div><div role="main" class="ab-center-column md:flex justify-center grow xl:px-7 xl:overflow-x-hidden"><div class="w-full max-w-[768px]" id="main-column"><div><div class="flex flex-col justify-center pt-4 xl:pt-7"><div class="flex flex-1 w-full justify-center pb-0"><div class="flex flex-initial flex-col w-full"><div class="flex items-center pl-0 pr-2 mb-5 xl:-mt-1.5"><div class="flex"><div><div class="flex text-gray-400 flex-wrap"><div class="flex"><div class="flex pr-2 text-gray-500 dark:text-gray-400 items-center font-semibold css-1b4fjyx">Console</div></div></div></div></div></div><h1 class="ab-doc-name h1 font-bold text-5xl break-words w-full max-w-full mt-0 pb-0 xl:-mt-1.5 mb-0 css-ydpz48">CLI</h1><div class="flex gap-6 text-sm mt-5 mb-6 text-gray-400 dark:text-gray-500"><div class="flex items-center gap-1.5 flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="1rem" height="1rem" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="stroke-current"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg><span>2 min</span></div></div><div><div class="max-h-dvh invisible"> this page contains some of the more commonly used cli commands for your convenience to reference for the entire list of vast cli commands visit our cli docs https //docs vast ai/api/overview and quickstart page walkthrough common questions what can i do with vast cli? just about everything! vast&#x27;s cli repository is open source and we are constantly improving our cli functions! for more information about our cli visit our cli documentation https //cloud vast ai/cli/ for the comprehensive list of the current commands </div></div></div></div><div class="flex flex-col"><div class="no-print flex flex-col justify-end sm:flex-col w-full max-w-full pt-16"><div class="ab-doc-template-footer-container flex flex-col md:flex-row md:pt-7 md:justify-between sm:items-center 2xl:pb-7 border-t border-gray-100 dark:border-gray-800"><div class="flex flex-1 flex-col 2xl:mb-0 p-6 md:p-0 items-center md:items-start"><div class="flex items-center text-gray-500 dark:text-gray-400 text-base p-1"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1.5 stroke-current"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>Updated<!-- --> <!-- -->25 Apr 2025</div></div><div><div class="flex flex-col items-center p-6 pr-0 w-full border-t border-gray-100 dark:border-gray-800 2xl:items-center 2xl:justify-end 2xl:p-0 md:rounded md:items-end md:border-none md:w-fit md:p-4 md:pr-0 bg-white dark:bg-transparent text-gray-600 dark:text-gray-300"><div class="flex w-full justify-center md:justify-start text-base mb-4 md:mb-2 text-gray-500 dark:text-gray-300">Did this page help you?</div><div class="flex flex-wrap md:flex-nowrap justify-center md:justify-end"></div></div></div></div><div class="border-t border-gray-100 dark:border-gray-800 my-7 2xl:mt-0"></div><div class="flex flex-col lg:flex-row justify-between w-full dark:border-gray-800"><a title="Keys" tabindex="0" class="ab-nav-left ab-tab-focus flex flex-1 items-center lg:max-w-[calc(50%-0.5rem)] justify-start text-left mb-2 rounded-2xl cursor-pointer py-3 px-4 bg-gray-50 dark:bg-gray-850 hover:bg-gray-100 dark:hover:bg-gray-800 css-ypjd7c" href="keys.html"><div class="mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="navigation-arrow stroke-current text-gray-300 dark:text-gray-500 transition-all"><circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line></svg></div><div class="max-w-[90%]"><div class="text-gray-500 dark:text-gray-400 text-xs font-bold mt-0.5 text-left">PREVIOUS</div><div class="ab-nav-left-text text-left font-bold text-lg truncate">Keys</div></div></a><a title="Templates" tabindex="0" class="ab-nav-right ab-tab-focus flex flex-1 items-center lg:max-w-[calc(50%-0.5rem)] justify-end text-right mb-2 rounded-2xl cursor-pointer py-3 px-4 bg-gray-50 dark:bg-gray-850 hover:bg-gray-100 dark:hover:bg-gray-800 css-ypjd7c" href="templates.html"><div class="max-w-[90%]"><div class="text-gray-500 dark:text-gray-400 text-xs font-bold mt-0.5">NEXT</div><div class="ab-nav-right-text text-right max-w-full font-bold text-lg truncate">Templates</div></div><div class="ml-3"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="navigation-arrow stroke-current text-gray-300 dark:text-gray-500 transition-all"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></svg></div></a></div><div class="flex justify-center mt-12"><div class="my-2 flex mb-8 xl:hidden"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-[248px]"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div></div></div></div></div></div></div><div class="hidden"><div class="w-0 h-0 fixed right-0 bottom-0 pr-2 pt-7 xl:h-screen xl:w-1/3 xl:min-w-[33.33%] 2xl:min-w-[auto] 2xl:max-w-[450px] bg-white dark:bg-gray-900" style="top:100px"><div class="overflow-hidden h-full"><div id="ab-code-drawer" class="h-full"></div></div></div></div><div class="hidden xl:block w-[256px] 2xl:w-[360px] xl:flex-shrink-0"><div class="ab-right-column"><div class="ab-toc-container"><div id="ab-toc-portal"></div></div></div><div class="my-2 hidden"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-full"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div></div></div></div></div></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"hostname":"docs.vast.ai","pdfExport":false,"showToC":true,"shareableToken":"","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","docId":"/cli","_doc":{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","name":"CLI","icon":"","title":"CLI","previewImageURL":"","summary":null,"urlKey":"cli","description":"","urlAlias":"","data":{"nodes":[{"id":"IvLHZyslis2UJh_Xtix-N","type":"paragraph","children":[{"text":"This page contains some of the more commonly used CLI commands for your convenience to reference."}]},{"id":"DykPE2VzC6uZK14Vjn60r","type":"paragraph","children":[{"text":"For the entire list of Vast CLI commands visit our "},{"id":"FT50yvAAd4ziDYBc2dK70","type":"link","data":{"href":"https://docs.vast.ai/api/overview-and-quickstart","newTab":false},"children":[{"text":"CLI docs"}]},{"text":"."}]},{"id":"64v4PHrnGW7ZaooGx18Yc","type":"h1","children":[{"text":"Page Walkthrough"}]},{"id":"-_Ix04w_NzUFhVTbDswFK","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/dJPTWeywG4ENhWn4DWnfX_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/dJPTWeywG4ENhWn4DWnfX_image.png","size":100,"caption":"","isUploading":false,"width":713,"height":417},"children":[{"text":""}]},{"id":"i58ZosnOav4RnsiIXOD6h","type":"h2","children":[{"text":"Common Questions"}]},{"id":"_sS7Ah3I3S6Qkjd4ZWGHk","type":"h3","children":[{"text":"What can I do with Vast CLI?"}]},{"id":"wcTDO7_n0MWjKGpF-BbnY","type":"paragraph","children":[{"text":"Just about everything! Vast's CLI repository is open-source and we are constantly improving our CLI functions! For more information about our CLI visit our "},{"id":"ELRWu-JvMxCURJQP09xZH","type":"link","data":{"href":"https://cloud.vast.ai/cli/","newTab":false},"children":[{"text":"CLI documentation"}]},{"text":" for the comprehensive list of the current commands."}]}],"metadata":{"type":"doc","version":"v2","convertedDate":"2025-01-13T21:20:39.235Z"}},"version":11,"privacy":"shared with team","shareableToken":"livfRVpMQPp2WgzcbM2qc","tags":[],"docTags":[],"children":[],"hasDraft":false,"createdByUserId":"l7ewxgfVU1YxmFCmcCgeu","createdBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"lastModifiedByUserId":"WL2az7o_8HmPbkHvSjU-Q","lastModifiedBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"contributorsDetails":[],"watchers":[],"isArchbeeBrandVisible":false,"customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","parentDocId":null,"docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","updatedAt":"2025-04-25T01:08:15.701Z","createdAt":"2025-01-13T21:20:40.590Z","deletedAt":null,"editable":false,"expanded":true,"reusableContentVariables":[{"contentVariableId":"q3exSma0JEJZI5e1dH_V6","name":"Worker_Groups","content":"Worker Groups","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"UP_Tl2jcgO5gOIMbrO-GS","name":"Endpoints","content":"Endpoints","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"5aHM10OFigKWTwHuB3fMP","name":"PyWorker","content":"PyWorker","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The Vast PyWorker is a Python web server designed to run alongside a machine learning model instance, providing autoscaler compatibility."},{"contentVariableId":"4t0syUbNAbAxpMhfF1SS9","name":"Worker_Group","content":"Worker Group","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"07Mp-kz9OjvJv1gj7w4H2","name":"Endpoint","content":"Endpoint","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"sP383XCx12brqPeq_8qVA","name":"min_cold_workers","content":"min_cold_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The minimum number of workers you want to keep \"cold\" (meaning stopped and fully loaded) when your group has no load."},{"contentVariableId":"ud8V8Q4s-JoB5vW8wVEJS","name":"max_workers","content":"max_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The maximum number of workers your router group can have."}],"reusableContentDocRefs":{},"externalSync":"","contentFromExternalLink":"","leftDoc":{"id":"PUBLISHED-3Yf2niu0UjnpDreE9E94e","children":[],"expanded":false,"name":"Keys","urlKey":"keys","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},"rightDoc":{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","children":[{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Templates","urlKey":"templates","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]}},"_docSpace":{"id":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","name":"Guides","organizationName":"Vast.ai 2","icon":"","type":"team","publicDocsTree":[{"id":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","isCategory":true,"categoryName":"Overview","gitHubPath":"docs/overview","children":[{"id":"PUBLISHED-704s_lXkTRgEFkRLGeF1p","isCategory":false,"categoryName":"1-introduction","children":[],"expanded":false,"name":"Introduction","urlKey":"","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-aKvlCTuCrf4gl2NR7hO2W","isCategory":false,"categoryName":"2-quickstart","children":[],"expanded":false,"name":"QuickStart","urlKey":"quickstart","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","children":[{"id":"PUBLISHED-_biJv6YaRTqcfsFCsFS50","children":[],"expanded":false,"name":"Instances Help","urlKey":"instances-help","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-PlnZzvcNBmO9p7BEBwDFS","children":[],"expanded":false,"name":"Billing Help","urlKey":"billing-help","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-sVUb5JPCzlvMnKDfU-2Op","children":[],"expanded":false,"name":"Networking","urlKey":"networking","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_0-0A7rQNefJSou1KR6CK","children":[],"expanded":false,"name":"Troubleshooting","urlKey":"troubleshooting","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-VfosYMdo2IumeiqSJdd2g","children":[],"expanded":false,"name":"Data Movement","urlKey":"data-movement","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]}],"name":"FAQ","expanded":false,"urlKey":"faq","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]}],"name":"overview","expanded":true,"urlKey":"N2so-overview","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-oocGb1edF0t5aErXyJR2f","isCategory":true,"categoryName":"Use Cases","gitHubPath":"docs/use-cases","children":[{"id":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","children":[{"id":"PUBLISHED-aNV7igw-IqgsWPPhf73et","children":[],"expanded":false,"name":"Creating Templates for GROBID","urlKey":"creating-templates-for-grobid","icon":"","parentDocId":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","conditionalRuleId":"","docTags":[]}],"name":"Creating a Custom Template","expanded":false,"urlKey":"creating-a-custom-template","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-8OgArwW8g6Y_17wGT-SNf","children":[{"id":"PUBLISHED-k-Bha16hWnYinwkb4lD2O","children":[],"expanded":false,"name":"PyTorch","urlKey":"pytorch","icon":"","parentDocId":"PUBLISHED-8OgArwW8g6Y_17wGT-SNf","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI/ML Frameworks","name":"AI/ML Frameworks","expanded":false,"urlKey":"aiml-frameworks","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-FGY05fV4_DdhtOukqQRu7","children":[{"id":"PUBLISHED-jTdh63niYUhgnZqlLJN9y","children":[],"expanded":false,"name":"CUDA","urlKey":"cuda","icon":"","parentDocId":"PUBLISHED-FGY05fV4_DdhtOukqQRu7","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"GPU Programming","name":"GPU Programming","expanded":false,"urlKey":"gpu-programming","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-4TMoTzzxDABXAuieagxQu","children":[{"id":"PUBLISHED-ODY3UoJAhLsqR71AARmJJ","children":[],"expanded":false,"name":"Linux Virtual Desktop","urlKey":"linux-virtual-desktop","icon":"","parentDocId":"PUBLISHED-4TMoTzzxDABXAuieagxQu","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-qklVPbUTSdi2iDW1S8HLN","children":[],"expanded":false,"name":"Linux Virtual Machines","urlKey":"linux-virtual-machines","icon":"","parentDocId":"PUBLISHED-4TMoTzzxDABXAuieagxQu","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Virtual Computing","name":"Virtual Computing","expanded":false,"urlKey":"virtual-computing","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-20FJ-CC_3dkM_s4PSdZ2F","children":[{"id":"PUBLISHED-WPQDtP5RBJyVW-5J1Q2M8","children":[],"expanded":false,"name":"TTS with Nari Labs Dia","urlKey":"tts-with-nari-labs-dia","icon":"","parentDocId":"PUBLISHED-20FJ-CC_3dkM_s4PSdZ2F","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Audio Generation","expanded":false,"name":"AI Audio Generation","urlKey":"ai-audio-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-SHmVLRqlmubft4sblMWsR","children":[{"id":"PUBLISHED-Pe3Za4T6xtQeIXLMP5Qqb","children":[],"expanded":false,"name":"Ollama + Webui","urlKey":"ollama-webui","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-787gRc7SEF6IXK4TzZYoZ","isCategory":false,"categoryName":"1-oobabooga","children":[],"expanded":false,"name":"Oobabooga (LLM webui)","urlKey":"oobabooga-llm-webui","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-p379GpTWJQL7SU2H0AqZk","isCategory":false,"categoryName":"8-tgi-llama3","children":[],"expanded":false,"name":"Huggingface TGI with LLama3","urlKey":"huggingface-tgi-with-llama3","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-jDgLap_KE2zPowqF-tpeu","children":[],"expanded":false,"name":"Quantized GGUF models (cloned)","urlKey":"quantized-gguf-models-cloned","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-6rtLs7cFxc61_uHKG8Ez9","children":[],"expanded":false,"name":"vLLM (LLM inference and serving)","urlKey":"vllm-llm-inference-and-serving","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Text Generation","name":"AI Text Generation","expanded":false,"urlKey":"ai-text-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cit6SDDxsqPFy7W79duf2","children":[{"id":"PUBLISHED-GHt2haaEuiL6GPBQyTBxF","children":[],"expanded":false,"name":"Image Generation","urlKey":"image-generation","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-k7iTLYdjB0Frdv3rQW_ca","children":[],"expanded":false,"name":"Stable Diffusion","urlKey":"stable-diffusion","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-zq3-YW2UEXhITlDCCTg6r","isCategory":false,"categoryName":"3-disco-diffusion","children":[],"expanded":false,"name":"Disco Diffusion","urlKey":"disco-diffusion","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Image Generation","name":"AI Image Generation","expanded":false,"urlKey":"ai-image-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-pB-IyK90u76M-ersF3WVv","children":[{"id":"PUBLISHED-oIHRc63tcSWGEACRRhHkI","children":[],"expanded":false,"name":"Video Generation","urlKey":"video-generation","icon":"","parentDocId":"PUBLISHED-pB-IyK90u76M-ersF3WVv","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Video Generation","expanded":false,"name":"AI Video Generation","urlKey":"ai-video-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--OQJF1kNwwKNJ08fC6Yp-","children":[{"id":"PUBLISHED-ii8e4fy1Tlup9M977dXpb","isCategory":false,"categoryName":"10-serving-infinity","children":[],"expanded":false,"name":"Infinity Embeddings","urlKey":"infinity-embeddings","icon":"","parentDocId":"PUBLISHED--OQJF1kNwwKNJ08fC6Yp-","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Text Embeddings","name":"Text Embeddings","expanded":false,"urlKey":"text-embeddings","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","children":[{"id":"PUBLISHED-mMqv1J1TQtpgiXH-eVm14","children":[],"expanded":false,"name":"Blender in the Cloud","urlKey":"blender-in-the-cloud","icon":"","parentDocId":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-AIPiFyiKn52NGHCH5nq1x","children":[],"expanded":false,"name":"Blender Batch Rendering","urlKey":"blender-batch-rendering","icon":"","parentDocId":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"3D Rendering","name":"3D Rendering","expanded":false,"urlKey":"3d-rendering","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-o-tDtwQLB3sjdU5HXlAH4","children":[{"id":"PUBLISHED-1FvJXdy3RgsmFzXR-4Wo2","isCategory":false,"categoryName":"7-mining-on-bittensor","children":[],"expanded":false,"name":"Mining on Bittensor","urlKey":"mining-on-bittensor","icon":"","parentDocId":"PUBLISHED-o-tDtwQLB3sjdU5HXlAH4","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Cryptocurrency","name":"Cryptocurrency","expanded":false,"urlKey":"cryptocurrency","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Yb3VYsvpheOhYYR_1WcII","children":[{"id":"PUBLISHED--X-CVuYaTu3w0xlokb91n","children":[],"expanded":false,"name":"Google Colab","urlKey":"google-colab","icon":"","parentDocId":"PUBLISHED-Yb3VYsvpheOhYYR_1WcII","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Development Tools","name":"Development Tools","expanded":false,"urlKey":"development-tools","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-0E8Al-YdTkoXx5j9keaaQ","children":[{"id":"PUBLISHED-dHQJOGIJkb6T0ZbxrZXSl","children":[],"expanded":false,"name":"Whisper ASR Guide","urlKey":"whisper-asr-guide","icon":"","parentDocId":"PUBLISHED-0E8Al-YdTkoXx5j9keaaQ","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Audio-to-Text","name":"Audio-to-Text","expanded":false,"urlKey":"audio-to-text","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]}],"name":"use-cases","isFoldedByDefault":true,"expanded":false,"urlKey":"use-cases","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-bbpobgkuArCgDczPxCtwT","isCategory":true,"categoryName":"Teams","gitHubPath":"docs/team","children":[{"id":"PUBLISHED-xv11ltkqGShfl-mT7XNLu","children":[],"expanded":false,"name":"Teams Overview","urlKey":"teams-overview","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Ynj96qfNyErZfqAwa2LFD","children":[{"id":"PUBLISHED-BXJXoCydqAATNaD288O0B","children":[],"expanded":false,"name":"Edit team","urlKey":"edit-team","icon":"","parentDocId":"PUBLISHED-Ynj96qfNyErZfqAwa2LFD","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Teams Quickstart","urlKey":"teams-quickstart","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-ACyO06JuEKgjfmMjBzaU9","children":[],"expanded":false,"name":"Team Creation","urlKey":"team-creation","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Hlv-kWiMRQoJ9t01lmNbW","children":[],"expanded":false,"name":"Teams Invitations","urlKey":"teams-invitations","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--XLpXkdTANYzK8ApWif5B","children":[],"expanded":false,"name":"Teams Roles","urlKey":"teams-roles","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-qlS14p3D6OOTphgBEQHOV","children":[],"expanded":false,"name":"Transfer Team Ownership","urlKey":"transfer-team-ownership","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]}],"name":"team","isFoldedByDefault":true,"expanded":false,"urlKey":"team","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","isCategory":true,"categoryName":"Hosting","gitHubPath":"docs/hosting","children":[{"id":"PUBLISHED-m8TTE0HPiTQoEwvtuGXkz","children":[],"expanded":false,"name":"Overview","urlKey":"overview","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cqNh9syMhS3kCV0cBIVEZ","isCategory":false,"categoryName":"2-taxes","children":[],"expanded":false,"name":"Guide to Taxes","urlKey":"guide-to-taxes","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-xal9KdbH6xQ8XSLsN-33N","isCategory":false,"categoryName":"3-datacenter","children":[],"expanded":false,"name":"Datacenter Status","urlKey":"datacenter-status","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-jUSbVYLeXgnYRIW7-27Ol","isCategory":false,"categoryName":"3-payment","children":[],"expanded":false,"name":"Payment","urlKey":"payment","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-VxN0-4JlmoIUdou8WmY1y","isCategory":false,"categoryName":"4-verification-stages","children":[],"expanded":false,"name":"Verification Stages","urlKey":"verification-stages","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-BMs7hJRHIZKzTgQVsYSVB","isCategory":false,"categoryName":"5-vms","children":[],"expanded":false,"name":"VMs","urlKey":"vms","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-1fI7lKmyAWNjt6O0SBcEt","children":[],"expanded":false,"name":"Clusters","urlKey":"clusters","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]}],"name":"hosting","isFoldedByDefault":true,"expanded":false,"urlKey":"hosting","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-3tqe-Mndyz9u9qhOMafF7","isCategory":true,"categoryName":"Distributed Computing","gitHubPath":"docs/distributed-computing","children":[{"id":"PUBLISHED-fAVEqWxj7VHiS6pEHzul0","children":[],"name":"Multi-Node training using Torch + NCCL","urlKey":"multi-node-training-using-torch-nccl","icon":"","parentDocId":"PUBLISHED-3tqe-Mndyz9u9qhOMafF7","conditionalRuleId":"","expanded":false,"docTags":[]}],"name":"distributed-computing","isFoldedByDefault":true,"expanded":false,"urlKey":"distributed-computing","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","isCategory":true,"categoryName":"Console","gitHubPath":"docs/console","children":[{"id":"PUBLISHED-QiRgigF76ZPU_lTGkGoLS","isCategory":false,"categoryName":"1-introduction","children":[],"expanded":false,"name":"Introduction","urlKey":"introduction","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-6s1uSe7_teJrmXnaQD36N","isCategory":false,"categoryName":"2-account","children":[],"expanded":false,"name":"Settings","urlKey":"settings","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-3Yf2niu0UjnpDreE9E94e","children":[],"expanded":false,"name":"Keys","urlKey":"keys","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","isCategory":false,"categoryName":"4-cli","children":[],"expanded":false,"name":"CLI","urlKey":"cli","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","children":[{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Templates","urlKey":"templates","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-se59-5_jE_1KHjoVaYm2k","children":[],"expanded":false,"name":"Volumes","urlKey":"volumes","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-BWpjFVMPqBj_DXfYnPOpG","children":[],"expanded":false,"name":"Billing","urlKey":"billing","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-8gVeMK-hQqPNOq_hHuKlK","children":[],"expanded":false,"name":"Earning","urlKey":"earning","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-v5ZbddMWMtwvGHat6WNu-","children":[],"expanded":false,"name":"Instances Guide","urlKey":"instances-guide","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-za2xagq8dMhXLJVRkj6Mh","children":[],"expanded":false,"name":"Search","urlKey":"search","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-s7iBVewEBNHjyaKXGI018","isCategory":false,"categoryName":"referrals","children":[],"expanded":false,"name":"Referral Program","urlKey":"referral-program","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--7dVUasT1ajlfn9VvagMv","children":[],"expanded":false,"name":"Members","urlKey":"members","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]}],"name":"Console","isFoldedByDefault":true,"expanded":false,"urlKey":"console","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_wACicNun9GwX_re6EJaE","children":[{"id":"PUBLISHED-UPthhpssnt-ANcq6-Hxwa","children":[],"expanded":false,"name":"RTX 5 Series","urlKey":"jDpX-XUZdy-zKWbAZQPNK","icon":"","parentDocId":"PUBLISHED-_wACicNun9GwX_re6EJaE","conditionalRuleId":"","docTags":[]}],"name":"Specific GPUs","isFoldedByDefault":false,"isCategory":true,"categoryName":"Specific GPUs","expanded":true,"urlKey":"specific-gpus","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]}],"hostingTitle":"","logoRedirectURL":"","hasPrimaryColorLinks":true,"hostingColor":"#2166ae","darkHostingColor":"#2166ae","secondaryColor":"#e0eefc","darkSecondaryColor":"#14467a","isIndexable":true,"template":"stripe","contentLayout":"two-column","hostname":"docs.vast.ai","hostnamePath":"","proxyDomain":"","publicLogoURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/hY3Z66NYu_wT-evx8EFi5_logo-symbol-dark.svg?format=webp","darkPublicLogoURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/BBMribM7Vqri9n_fLelvV_logo-symbol-light.svg?format=webp","publicTheme":"dark","faviconURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/K8kL9zPy3Yuuym96Ony5l_vast-social-profile-photo.png","spaceLinks":[{"label":"Guides","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","hostnamePath":"","versionLinks":[],"icon":"","hostname":"docs.vast.ai"},{"label":"Instances","docSpaceId":"PUBLISHED-3IG3Byg89UtcvaXwjp66z","icon":"","hostnamePath":"instances","hostname":"docs.vast.ai","versionLinks":[]},{"label":"Serverless","docSpaceId":"PUBLISHED-5WC30cNrS-qdU0I5VeweU","icon":"","hostnamePath":"serverless","hostname":"docs.vast.ai","versionLinks":[]},{"label":"API","docSpaceId":"PUBLISHED-yC9YDSGxNOpyhS0i8A-6f","icon":"","hostnamePath":"api","hostname":"docs.vast.ai","versionLinks":[]}],"versionLinks":[],"externalLinks":[{"label":"Console","url":"https://cloud.vast.ai/","isPrimary":true},{"label":"Discord","url":"https://discord.gg/hSuEbSQ4X8","isPrimary":false}],"landingPageType":"first-doc","landingTemplate":"","landingPageHeaderText":"","landingPageSubheaderText":"","landingHeroBgLightURL":"","landingHeroBgDarkURL":"","footerTemplate":"","headerIncludes":"\u003cscript type=\"text/javascript\"\u003e\nwindow.$crisp=[];\nwindow.CRISP_WEBSITE_ID=\"734d7b1a-86fc-470d-b60a-f6d4840573ae\";\n\n// Set up the ready trigger before loading Crisp\nwindow.CRISP_READY_TRIGGER = function() {\n    // Set current page URL as session data\n    $crisp.push([\"set\", \"session:data\", [[\n        \"current_page\", window.location.href\n    ]]]);\n};\n\n(function(){\n    d=document;\n    s=d.createElement(\"script\");\n    s.src=\"https://client.crisp.chat/l.js\";\n    s.async=1;\n    d.getElementsByTagName(\"head\")[0].appendChild(s);\n})();\n\n// Also track URL changes if you have a single-page application\nwindow.addEventListener('popstate', function() {\n    if (window.$crisp.is(\"website:available\")) {\n        $crisp.push([\"set\", \"session:data\", [[\n            \"current_page\", window.location.href\n        ]]]);\n    }\n});\n\u003c/script\u003e\n\n\u003c!-- Google tag (gtag.js) --\u003e\n\u003cscript async src=\"https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG\"\u003e\u003c/script\u003e\n\u003cscript\u003e\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-DG15WC8WXG');\n\u003c/script\u003e","jwtRedirectURL":"","googleAnalyticsId":"","intercomId":"","isArchbeeBrandVisible":false,"archbeeBrandPosition":"doc-tree","i18nLanguage":"en","showReadTime":true,"showLastUpdate":true,"showThemeSwitcher":true,"showContributors":false,"showDocFeedback":true,"showEditInGitApp":true,"showDocNavigationButtons":true,"revisions":[],"customJS":"","customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","customFont":null,"custom404":"","createdAt":"2025-01-04T02:25:03.718Z","showPdfBookLink":false,"pdfBookLink":"","llmsTxtLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/llms-txt/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-llms.txt","llmsFullTxtLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/llms-txt/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-llms-full.txt","sitemapXmlLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/sitemap-xml/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-sitemap.xml","protectionType":"None","isAuthenticated":true,"tagsCache":[],"isQAndAEnabled":false,"isQAndAPublicEnabled":false,"isQAndAAIEnabled":false,"isQAndAApprovalEnabled":false,"isLlmEnabled":true,"isLlmInternal":true,"isLlmExternal":true,"authPageLayout":"one-column","authPagePosition":"left","authBgImageURL":"","authDarkBgImageURL":"","gitAppRepoId":"","gitAppRepo":null,"isGithubEnabled":false,"isBitbucketEnabled":false,"doc":{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","name":"CLI","icon":"","title":"CLI","previewImageURL":"","summary":null,"urlKey":"cli","description":"","urlAlias":"","data":{"nodes":[{"id":"IvLHZyslis2UJh_Xtix-N","type":"paragraph","children":[{"text":"This page contains some of the more commonly used CLI commands for your convenience to reference."}]},{"id":"DykPE2VzC6uZK14Vjn60r","type":"paragraph","children":[{"text":"For the entire list of Vast CLI commands visit our "},{"id":"FT50yvAAd4ziDYBc2dK70","type":"link","data":{"href":"https://docs.vast.ai/api/overview-and-quickstart","newTab":false},"children":[{"text":"CLI docs"}]},{"text":"."}]},{"id":"64v4PHrnGW7ZaooGx18Yc","type":"h1","children":[{"text":"Page Walkthrough"}]},{"id":"-_Ix04w_NzUFhVTbDswFK","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/dJPTWeywG4ENhWn4DWnfX_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/dJPTWeywG4ENhWn4DWnfX_image.png","size":100,"caption":"","isUploading":false,"width":713,"height":417},"children":[{"text":""}]},{"id":"i58ZosnOav4RnsiIXOD6h","type":"h2","children":[{"text":"Common Questions"}]},{"id":"_sS7Ah3I3S6Qkjd4ZWGHk","type":"h3","children":[{"text":"What can I do with Vast CLI?"}]},{"id":"wcTDO7_n0MWjKGpF-BbnY","type":"paragraph","children":[{"text":"Just about everything! Vast's CLI repository is open-source and we are constantly improving our CLI functions! For more information about our CLI visit our "},{"id":"ELRWu-JvMxCURJQP09xZH","type":"link","data":{"href":"https://cloud.vast.ai/cli/","newTab":false},"children":[{"text":"CLI documentation"}]},{"text":" for the comprehensive list of the current commands."}]}],"metadata":{"type":"doc","version":"v2","convertedDate":"2025-01-13T21:20:39.235Z"}},"version":11,"privacy":"shared with team","shareableToken":"livfRVpMQPp2WgzcbM2qc","tags":[],"docTags":[],"children":[],"hasDraft":false,"createdByUserId":"l7ewxgfVU1YxmFCmcCgeu","createdBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"lastModifiedByUserId":"WL2az7o_8HmPbkHvSjU-Q","lastModifiedBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"contributorsDetails":[],"watchers":[],"isArchbeeBrandVisible":false,"customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","parentDocId":null,"docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","updatedAt":"2025-04-25T01:08:15.701Z","createdAt":"2025-01-13T21:20:40.590Z","deletedAt":null,"editable":false,"expanded":true,"reusableContentVariables":[{"contentVariableId":"q3exSma0JEJZI5e1dH_V6","name":"Worker_Groups","content":"Worker Groups","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"UP_Tl2jcgO5gOIMbrO-GS","name":"Endpoints","content":"Endpoints","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"5aHM10OFigKWTwHuB3fMP","name":"PyWorker","content":"PyWorker","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The Vast PyWorker is a Python web server designed to run alongside a machine learning model instance, providing autoscaler compatibility."},{"contentVariableId":"4t0syUbNAbAxpMhfF1SS9","name":"Worker_Group","content":"Worker Group","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"07Mp-kz9OjvJv1gj7w4H2","name":"Endpoint","content":"Endpoint","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"sP383XCx12brqPeq_8qVA","name":"min_cold_workers","content":"min_cold_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The minimum number of workers you want to keep \"cold\" (meaning stopped and fully loaded) when your group has no load."},{"contentVariableId":"ud8V8Q4s-JoB5vW8wVEJS","name":"max_workers","content":"max_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The maximum number of workers your router group can have."}],"reusableContentDocRefs":{},"externalSync":"","contentFromExternalLink":"","leftDoc":{"id":"PUBLISHED-3Yf2niu0UjnpDreE9E94e","children":[],"expanded":false,"name":"Keys","urlKey":"keys","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},"rightDoc":{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","children":[{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Templates","urlKey":"templates","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]}},"lastPublishedToPreview":"2025-07-08T00:23:45.674Z","lastPublishedToProduction":"2025-07-08T19:41:39.988Z","customQAndAModalDescription":"","customEndingSignature":"","customForgotPasswordMessage":"","customNewAccountMessage":"","customEmailSenderName":"","autoPublishInterval":null,"autoPublishLastRun":null,"autoPublishEnabled":false,"isPublicSubscribeEnabled":false,"isCategoryPageDataEnabled":false,"portalId":"lOjtkBF-PmAqJkTBobhAY","showFullTitleInLeftNavDocsTree":false},"isHosted":true,"isMobile":false,"headerIncludes":"\u003cscript type=\"text/javascript\"\u003e\nwindow.$crisp=[];\nwindow.CRISP_WEBSITE_ID=\"734d7b1a-86fc-470d-b60a-f6d4840573ae\";\n\n// Set up the ready trigger before loading Crisp\nwindow.CRISP_READY_TRIGGER = function() {\n    // Set current page URL as session data\n    $crisp.push([\"set\", \"session:data\", [[\n        \"current_page\", window.location.href\n    ]]]);\n};\n\n(function(){\n    d=document;\n    s=d.createElement(\"script\");\n    s.src=\"https://client.crisp.chat/l.js\";\n    s.async=1;\n    d.getElementsByTagName(\"head\")[0].appendChild(s);\n})();\n\n// Also track URL changes if you have a single-page application\nwindow.addEventListener('popstate', function() {\n    if (window.$crisp.is(\"website:available\")) {\n        $crisp.push([\"set\", \"session:data\", [[\n            \"current_page\", window.location.href\n        ]]]);\n    }\n});\n\u003c/script\u003e\n\n\u003c!-- Google tag (gtag.js) --\u003e\n\u003cscript async src=\"https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG\"\u003e\u003c/script\u003e\n\u003cscript\u003e\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-DG15WC8WXG');\n\u003c/script\u003e","passwordChangeToken":"","jwt":"","reloadAuth":false,"isBot":true,"isWidget":false,"template":"","docs":[],"search":""}},"page":"/public/[[...slug]]","query":{"slug":["","%2Fcli"],"hostname":"docs.vast.ai","isHosted":"true","pdfExport":"","paginationFromPdf":"","paginationToPdf":"","paginationLimitPdf":"","showToC":"","shareableToken":"","passwordChangeToken":"","search":"","template":"","jwt":"","isWidget":"false","tabNav":"","reload":"false"},"buildId":"dBe88brPNkUEoyQSYuIle","assetPrefix":"https://cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2","runtimeConfig":{"NEXT_PUBLIC_ENV_ID":"app.archbee.com","NEXT_PUBLIC_ENV":"live","NEXT_PUBLIC_DOMAIN":"app.archbee.com","NEXT_PUBLIC_ASSET_PREFIX":"https://cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2","NEXT_PUBLIC_VERSION":"96c7914b49966e400b4f53ac904fa80945d640a2","NEXT_PUBLIC_BUILD_DATE":"2025-07-11T11:21:54.672Z","NEXT_RTS_DOMAIN":"rts.archbee.com"},"isFallback":false,"isExperimentalCompile":false,"dynamicIds":[5810,79658,2541],"gip":true,"scriptLoader":[]}</script><script defer src="../external.html?link=https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"95e0629415a675d5","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"cd97f360f847483b94d308aff2d8fe85"}' crossorigin="anonymous"></script>
</body>
<!-- Mirrored from docs.vast.ai/cli by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 12 Jul 2025 12:01:18 GMT -->
</html>