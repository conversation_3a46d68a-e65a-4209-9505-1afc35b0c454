import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type ListServersRequest = {
    /**
     * Search query for semantic search. Can include special filters:
     *
     * @remarks
     * - `owner:username` to filter by repository owner
     * - `repo:repository-name` to filter by repository name
     * - `is:deployed` to show only deployed servers
     * - `is:verified` to show only verified servers
     */
    q?: string | undefined;
    /**
     * Page number for pagination
     */
    page?: number | undefined;
    /**
     * Number of items per page
     */
    pageSize?: number | undefined;
};
export type ListServersResponse = {
    result: components.ServerListResponse;
};
/** @internal */
export declare const ListServersRequest$inboundSchema: z.ZodType<ListServersRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type ListServersRequest$Outbound = {
    q?: string | undefined;
    page: number;
    pageSize: number;
};
/** @internal */
export declare const ListServersRequest$outboundSchema: z.ZodType<ListServersRequest$Outbound, z.ZodTypeDef, ListServersRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ListServersRequest$ {
    /** @deprecated use `ListServersRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ListServersRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `ListServersRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ListServersRequest$Outbound, z.ZodTypeDef, ListServersRequest>;
    /** @deprecated use `ListServersRequest$Outbound` instead. */
    type Outbound = ListServersRequest$Outbound;
}
export declare function listServersRequestToJSON(listServersRequest: ListServersRequest): string;
export declare function listServersRequestFromJSON(jsonString: string): SafeParseResult<ListServersRequest, SDKValidationError>;
/** @internal */
export declare const ListServersResponse$inboundSchema: z.ZodType<ListServersResponse, z.ZodTypeDef, unknown>;
/** @internal */
export type ListServersResponse$Outbound = {
    Result: components.ServerListResponse$Outbound;
};
/** @internal */
export declare const ListServersResponse$outboundSchema: z.ZodType<ListServersResponse$Outbound, z.ZodTypeDef, ListServersResponse>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ListServersResponse$ {
    /** @deprecated use `ListServersResponse$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ListServersResponse, z.ZodTypeDef, unknown>;
    /** @deprecated use `ListServersResponse$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ListServersResponse$Outbound, z.ZodTypeDef, ListServersResponse>;
    /** @deprecated use `ListServersResponse$Outbound` instead. */
    type Outbound = ListServersResponse$Outbound;
}
export declare function listServersResponseToJSON(listServersResponse: ListServersResponse): string;
export declare function listServersResponseFromJSON(jsonString: string): SafeParseResult<ListServersResponse, SDKValidationError>;
//# sourceMappingURL=listservers.d.ts.map