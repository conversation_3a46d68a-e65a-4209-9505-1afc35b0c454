#!/usr/bin/env python3
"""
Requirements Analysis System for VastWizard Template Generation
Analyzes AI/ML tools and extracts system requirements for Vast.ai template creation
"""

import json
import re
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from pathlib import Path

@dataclass
class SystemRequirements:
    """Data class to store system requirements for an AI/ML tool"""
    tool_name: str
    github_url: str
    
    # GPU Requirements
    min_gpu: Optional[str] = None
    min_vram: Optional[str] = None
    recommended_gpu: Optional[str] = None
    recommended_vram: Optional[str] = None
    gpu_required: bool = False
    
    # System Requirements
    python_version: Optional[str] = None
    cuda_version: Optional[str] = None
    min_ram: Optional[str] = None
    recommended_ram: Optional[str] = None
    storage_space: Optional[str] = None
    
    # Dependencies
    key_dependencies: List[str] = None
    docker_support: bool = False
    
    # Performance Data
    performance_data: Dict[str, str] = None
    
    # Installation Notes
    installation_notes: List[str] = None
    
    def __post_init__(self):
        if self.key_dependencies is None:
            self.key_dependencies = []
        if self.performance_data is None:
            self.performance_data = {}
        if self.installation_notes is None:
            self.installation_notes = []

class RequirementsAnalyzer:
    """Analyzes AI/ML tools and extracts system requirements"""
    
    def __init__(self):
        self.gpu_patterns = {
            'rtx_4090': r'RTX\s*4090',
            'rtx_3090': r'RTX\s*3090',
            'a100': r'A100',
            'rtx_series': r'RTX\s*\d{4}',
            'nvidia': r'NVIDIA',
            'cuda': r'CUDA'
        }
        
        self.memory_patterns = {
            'vram': r'(\d+)\s*GB.*VRAM',
            'ram': r'(\d+)\s*GB.*RAM',
            'memory': r'(\d+)\s*GB.*memory'
        }
        
        self.python_patterns = {
            'version': r'Python\s*(\d+\.\d+)',
            'requirement': r'python.*(\d+\.\d+)'
        }
    
    def analyze_ace_step(self) -> SystemRequirements:
        """Analyze ACE-Step requirements based on gathered information"""
        
        requirements = SystemRequirements(
            tool_name="ACE-Step",
            github_url="https://github.com/ace-step/ACE-Step"
        )
        
        # GPU Requirements (from performance table)
        requirements.min_gpu = "NVIDIA RTX 3090"
        requirements.min_vram = "8GB"
        requirements.recommended_gpu = "NVIDIA RTX 4090"
        requirements.recommended_vram = "16GB+"
        requirements.gpu_required = True
        
        # System Requirements
        requirements.python_version = "3.10+"
        requirements.cuda_version = "CUDA 12.6"
        requirements.min_ram = "16GB"
        requirements.recommended_ram = "32GB+"
        requirements.storage_space = "20GB+"
        
        # Key Dependencies
        requirements.key_dependencies = [
            "torch", "torchaudio", "torchvision",
            "diffusers>=0.33.0", "transformers==4.50.0",
            "gradio", "librosa==0.11.0", "soundfile==0.13.1",
            "pytorch_lightning==2.5.1", "accelerate==1.6.0"
        ]
        
        # Docker Support
        requirements.docker_support = True
        
        # Performance Data
        requirements.performance_data = {
            "RTX 4090": "34.48x RTF (1.74s for 1min audio)",
            "A100": "27.27x RTF (2.20s for 1min audio)", 
            "RTX 3090": "12.76x RTF (4.70s for 1min audio)",
            "MacBook M2 Max": "2.27x RTF (26.43s for 1min audio)"
        }
        
        # Installation Notes
        requirements.installation_notes = [
            "Requires NVIDIA GPU for optimal performance",
            "Windows users need triton-windows for torch.compile",
            "Memory optimization available with --cpu_offload flag",
            "Supports 19 languages with top 10 performing well",
            "Real-time factor (RTF) performance varies by hardware",
            "Docker and docker-compose support available",
            "ComfyUI integration available"
        ]
        
        return requirements
    
    def extract_gpu_requirements(self, text: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract GPU requirements from text"""
        min_gpu = None
        recommended_gpu = None
        
        # Look for specific GPU mentions
        if re.search(self.gpu_patterns['rtx_4090'], text, re.IGNORECASE):
            recommended_gpu = "NVIDIA RTX 4090"
        elif re.search(self.gpu_patterns['rtx_3090'], text, re.IGNORECASE):
            min_gpu = "NVIDIA RTX 3090"
        elif re.search(self.gpu_patterns['a100'], text, re.IGNORECASE):
            recommended_gpu = "NVIDIA A100"
        
        return min_gpu, recommended_gpu
    
    def extract_memory_requirements(self, text: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract memory requirements from text"""
        vram = None
        ram = None
        
        # Look for VRAM mentions
        vram_match = re.search(self.memory_patterns['vram'], text, re.IGNORECASE)
        if vram_match:
            vram = f"{vram_match.group(1)}GB"
        
        # Look for RAM mentions
        ram_match = re.search(self.memory_patterns['ram'], text, re.IGNORECASE)
        if ram_match:
            ram = f"{ram_match.group(1)}GB"
        
        return vram, ram
    
    def extract_python_version(self, text: str) -> Optional[str]:
        """Extract Python version requirements from text"""
        python_match = re.search(self.python_patterns['version'], text, re.IGNORECASE)
        if python_match:
            return python_match.group(1) + "+"
        return None
    
    def save_requirements(self, requirements: SystemRequirements, output_file: str = "ace_step_requirements.json"):
        """Save requirements analysis to JSON file"""
        data = {
            "tool_name": requirements.tool_name,
            "github_url": requirements.github_url,
            "gpu_requirements": {
                "min_gpu": requirements.min_gpu,
                "min_vram": requirements.min_vram,
                "recommended_gpu": requirements.recommended_gpu,
                "recommended_vram": requirements.recommended_vram,
                "gpu_required": requirements.gpu_required
            },
            "system_requirements": {
                "python_version": requirements.python_version,
                "cuda_version": requirements.cuda_version,
                "min_ram": requirements.min_ram,
                "recommended_ram": requirements.recommended_ram,
                "storage_space": requirements.storage_space
            },
            "dependencies": {
                "key_dependencies": requirements.key_dependencies,
                "docker_support": requirements.docker_support
            },
            "performance_data": requirements.performance_data,
            "installation_notes": requirements.installation_notes
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Requirements analysis saved to {output_file}")

def main():
    """Main function to run requirements analysis"""
    analyzer = RequirementsAnalyzer()
    
    print("🔍 Analyzing ACE-Step requirements...")
    ace_step_reqs = analyzer.analyze_ace_step()
    
    print("\n📊 ACE-Step Requirements Analysis:")
    print(f"Tool: {ace_step_reqs.tool_name}")
    print(f"Min GPU: {ace_step_reqs.min_gpu}")
    print(f"Min VRAM: {ace_step_reqs.min_vram}")
    print(f"Python: {ace_step_reqs.python_version}")
    print(f"CUDA: {ace_step_reqs.cuda_version}")
    print(f"Docker Support: {ace_step_reqs.docker_support}")
    
    # Save to file
    analyzer.save_requirements(ace_step_reqs)
    
    return ace_step_reqs

if __name__ == "__main__":
    main()
