<!DOCTYPE html><html lang="en">
<!-- Mirrored from docs.vast.ai/templates by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 12 Jul 2025 12:00:58 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head><meta charSet="utf-8" data-next-head=""/><title data-next-head="">Templates - Guides</title><meta name="description" content="Guides" data-next-head=""/><meta name="image" content="" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:title" content="Templates - Guides" data-next-head=""/><meta name="twitter:description" content="Guides" data-next-head=""/><meta name="twitter:image" content="" data-next-head=""/><meta property="og:title" content="Templates - Guides" data-next-head=""/><meta property="og:type" content="product" data-next-head=""/><meta property="og:image" content="" data-next-head=""/><meta property="og:description" content="Guides" data-next-head=""/><meta name="language" content="en" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=6.0, user-scalable=1" data-next-head=""/><link rel="shortcut icon" href="../images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/K8kL9zPy3Yuuym96Ony5l_vast-social-profile-photo.png" data-next-head=""/><script type="text/javascript" data-next-head="">
window.$crisp=[];
window.CRISP_WEBSITE_ID="734d7b1a-86fc-470d-b60a-f6d4840573ae";

// Set up the ready trigger before loading Crisp
window.CRISP_READY_TRIGGER = function() {
    // Set current page URL as session data
    $crisp.push(["set", "session:data", [[
        "current_page", window.location.href
    ]]]);
};

(function(){
    d=document;
    s=d.createElement("script");
    s.src="../client.crisp.chat/l.js";
    s.async=1;
    d.getElementsByTagName("head")[0].appendChild(s);
})();

// Also track URL changes if you have a single-page application
window.addEventListener('popstate', function() {
    if (window.$crisp.is("website:available")) {
        $crisp.push(["set", "session:data", [[
            "current_page", window.location.href
        ]]]);
    }
});
</script><script async="" src="../external.html?link=https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG" data-next-head=""></script><script data-next-head="">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-DG15WC8WXG');
</script><style data-next-head="">
  /* Adjust chat button position */
  .crisp-client {
    --crisp-position-reverse: 1 !important; /* Left side positioning */
    --crisp-button-color: #4B5563 !important; /* Custom button color */
  }
  
  
  
  /* Optional: Hide chat widget on mobile */
  @media (max-width: 768px) {
    .crisp-client {
      display: none !important;
    }
  }
</style><link rel="preconnect" href="../external.html?link=https://cdn.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://cdnjs.cloudflare.com/"/><link rel="preconnect" href="../external.html?link=https://fonts.googleapis.com/"/><link rel="preconnect" href="../external.html?link=https://fonts.gstatic.com/" crossorigin=""/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="apple-mobile-web-app-capable" content="yes"/><link href="../external.html?link=https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/><link type="text/css" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/public/normalize.css"/><style data-emotion-css="1uiwhkr 1ro3qzv 1i6d6vk rbmaoo ydpz48 1dgdik1 v5vzns 1fl8vw4 u4v8xq roftna 1t9hfmb 1b94hea 9whsf3 w36zip 1ydoibe 1b4fjyx ypjd7c 1724ov3 whnwm4 64jqxe xw1pqi 130k67h 1lljsge j88me8 1srr18y 1iamho1 5knetf x4c2bn vxmz0c rw7xsu p1cn1o 1ys6oi3 49mvvd ghq01l 1tv6ror cjepy5 wufc4w cn6axj hkc7xg 8he6ch bmwl98 t7putw 5mgflz blbbuw jgdyep hlwo6c 1p004sg veby95 1321q7x 1bz6gk4 13qucz6 fzs3dc 8vo1gy 1ms681s gn5mcr 1qet1p 1gbzpq5 1149rud 18h9xkc dild6v 1ob8ogp s33lbd 1gygdlm pnt15a 1wsux5p ju1r6u 1whh9et 4zrcm0 na5ygo rn2g6o 13g7z6x gie19p jjsjcg 1tx6k1l 8k8wai w25jvf wwft1u qizsd mcv8nk ay17xk fe43hj 1of59pv u0dj5y 1izd2ee 71vex2 1fmx8db euksn3 87h1z1 f7ifu9 1rl7zx8 1c21mnf zfdg47 1xi6o7y hqfggw 3fu96b tfaolw kx3ph9 1b3v1k4 1nqd0y1 1h9zkw0 mhu4l3 7sluno 12f3pwh tplma3 sn6axx 1u5t5yx 6q3mca 7lplk8 1he04i1 1306ufs 1k5vv28 tkabts 1os98ik 4779de 5z0b09 v9rovc 13u1zyh 143nwtb 4dpz50 1pjpc1 jknmwy nfhqnj uglexu l75tm1 1i7f6np 1yku8fa q5fwlo 1pe94ip 7mn3wt 1wbwnsm 1paia7l ggw9mt va58lw 4wxci8 vyvlbq 1uye9f0 17q9201 mh68s5 wkw5oq bkpjdf 1ezqz7u 15dh43c 1bgl7dd 1qwrbaq 1cjgca1 11qudoy 1nwilma 1ieqxtj 1u12gcq 1p6ltz1 od8u8e 1ggsefg 9pg19u 2h5r2b 1epjdjo 1myj5jx 6zvay4 esdacs 1nm73v8 t78dr6 18pr5mp 16290om tomm9q 1vcs6o7 1ea3n21 s9z885 1i6ajij 1iu3i8p 1y1dhpv x7qujw 1l091ft 1ekr0xj rxerzz 10e4hpw 19zrndo 19zi4nq 4owzq1 18yrr1h ys0mck 16kn019 63d9l0 11263bm 77goi7 c2lo6u 1r7wx12 vkd4 1cjyxhb jvtmgo 1nsqb76 hrn9nv 1kaono8 ao6spu am5scv 1b3nikh qf3nol w6crv0 eqvarw frjp74 z6hwls pm8ktx gttege 1mb92vv 1ecznfq 1v9g95b 6485it gwo6kx 1mp63us 135mz3m 14jremo 1beh7e2 15slsu4 qxkkhs za4e7k 1wsgm2l 1k7mldi yyj7by 1e08sco 1xofzud e9x7n0 q8renl ipn83r fd4syp 11tke5i rq7vug re0pe 1u0h1r2 ae5q20 15y0cxp 1xcrlpv s2lg7y 95er94 4hl7c0 19a7cd6 18wgnvz nzt13c 1xwlzvm 1ipukr5 63ihqe wxglv8 1d5czsu ttmdd3 1f8ucol 13dzzt yfh3wc 1sirhxu blrl92 x39to4 yi4an8 6va3et 14elb1x 12n8vte 1uprvsl fpivd 1u0lf8v 1o2veex 1q4xixa jk60lu esg2bf 1gradx1 1jzmuxk 1uedzoz 1hjn58v ub2tfp 1qy29vz 11hpfbx o7bkb1 1702ptm 1ngj57b 18wotnf a6hlge 3kfn3n wi7rvl sr2yzv 1ink54o s5mzj7 14mq9xd g8bmer euad1m 17z7aj8 h3mayo 1lwg67g 1lsvizy kkia1w w46nsu 6sjddz 14sirbb 2ooi2b v7ueow 2so5qp 1kqr105 3802nw w8qile ffvq9h 13ub60e uiowgc 1d8xnr6 1a0ztic j2z86 1xp4b63 a2aim s6qf4w q5fyyv xeh8dm 1xt6kub 16bdtp4 gy1ky8 o1kdlj l81hax xbugmj 1kg35zq ppe5mx jgj4ho 1fy1f3j jl0nxc jopwba v990xg">:root{--smokey-grey:#d1d5db;--blue:#2166ae;--dark-blue:#0C121D;--red:#E95C5C;--grey:#0C121D;--light-grey:#9ca3af;--very-light-grey:#d1d5db;--smokey-grey-background:#f3f4f6;--line-grey:#d1d5db;}.graphiql-container .topBar{height:auto!important;}.doc-explorer-back{overflow:hidden;}.doc-explorer-title{overflow:hidden;}@-webkit-keyframes animation-1i6d6vk{0%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0.7);}70%{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);transform:scale(1);box-shadow:0 0 0 10px rgba(255, 82, 82, 0);}100%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0);}}@keyframes animation-1i6d6vk{0%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0.7);}70%{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);transform:scale(1);box-shadow:0 0 0 10px rgba(255, 82, 82, 0);}100%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0);}}:root{--ab-accent-color:#2166ae;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}.css-ydpz48{letter-spacing:-0.1rem;}.css-1dgdik1{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);}.css-v5vzns:hover{stroke:#2166ae;}.css-1fl8vw4{color:#ffffff;background-color:#2166ae!important;}.css-1fl8vw4:hover{background-color:#194d83!important;}.css-u4v8xq{height:inherit;max-width:inherit;}.css-roftna{border-color:transparent;}.css-1t9hfmb{border-color:#2166ae;}.css-1t9hfmb:hover{background-color:#eff6fd;}.css-1b94hea{margin-top:14px;margin-bottom:14px;font-weight:600;}.css-9whsf3{max-width:100%;}.css-w36zip{margin-top:28px;margin-bottom:14px;font-weight:600;}.css-1ydoibe{max-width:calc(100% - 18px);}.css-1b4fjyx{color:#2166ae!important;}.css-ypjd7c{-webkit-transition:border 200ms ease-out;transition:border 200ms ease-out;}.css-ypjd7c:hover{color:#2166ae;}.css-ypjd7c:hover .navigation-arrow{stroke:#2166ae;}:root{--ab-accent-color:#6921ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}.css-whnwm4{margin-top:14px;margin-bottom:14px;font-weight:400;}:root{--ab-accent-color:#e88004;--ab-bg-color:#fbe7d2;--ab-hover-bg-color:#fdf4eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b98ce;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7950F2;--ab-bg-color:#F3F0FF;--ab-hover-bg-color:#f7f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6262F5;--ab-bg-color:#e9e9fd;--ab-hover-bg-color:#f3f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#EA1D25;--ab-bg-color:#fce5e5;--ab-hover-bg-color:#fef4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5c98ce;--ab-bg-color:#e4ecf4;--ab-hover-bg-color:#f0f4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0051a4;--ab-bg-color:#dbedff;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#35b234;--ab-bg-color:#ddf1dd;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1f2fa3;--ab-bg-color:#e7eafc;--ab-hover-bg-color:#f5f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b36f5;--ab-bg-color:#ebeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#13b0fc;--ab-bg-color:#d1f1fc;--ab-hover-bg-color:#e5f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c8252c;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#F2F7FD;--ab-hover-bg-color:#eef4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de23fc;--ab-bg-color:#fce6fc;--ab-hover-bg-color:#fdf0fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6550ff;--ab-bg-color:#edebff;--ab-hover-bg-color:#f6f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006699;--ab-bg-color:#cbf0ff;--ab-hover-bg-color:#e4f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#27ae60;--ab-bg-color:#c6f5de;--ab-hover-bg-color:#defbed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#62c7c8;--ab-bg-color:#daefef;--ab-hover-bg-color:#eaf6f6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#009e74;--ab-bg-color:#8fffde;--ab-hover-bg-color:#cbfff0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#1f9cff;--ab-hover-bg-color:#ecf6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#328af6;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1b6aee;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#171717;--ab-bg-color:#ececec;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f47c6a;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#93c741;--ab-bg-color:#e4efd9;--ab-hover-bg-color:#eff6e9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1463ff;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4a61e0;--ab-bg-color:#eaedfd;--ab-hover-bg-color:#f4f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4750EF;--ab-bg-color:#C9D8FD;--ab-hover-bg-color:#f1f4fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff361d;--ab-bg-color:#ff361d85;--ab-hover-bg-color:rgba(255,241,239,0.52);--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#6a4ee1;--ab-bg-color:#e8e8f9;--ab-hover-bg-color:#f4f4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#664FEE;--ab-bg-color:#664fee1e;--ab-hover-bg-color:rgba(244,244,254,0.12);--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#ff0092;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0e4683;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#01d093;--ab-bg-color:#91fede;--ab-hover-bg-color:#cdfef0;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#180459;--ab-bg-color:#d5d0ef;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4F2ba7;--ab-bg-color:#311c6b;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#55a51c;--ab-bg-color:#d0f6b1;--ab-hover-bg-color:#e8fbd9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4D87E2;--ab-bg-color:#e6ecfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5056ea;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#116530;--ab-bg-color:#c0f7d5;--ab-hover-bg-color:#defbe7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1c48d9;--ab-bg-color:#e6ebfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f84b0b;--ab-bg-color:#ffe7cd;--ab-hover-bg-color:#fff3e6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff6f61;--ab-bg-color:#ffe6e3;--ab-hover-bg-color:#fff4f2;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8b6dff;--ab-bg-color:#efeaff;--ab-hover-bg-color:#f7f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9649b6;--ab-bg-color:#efe9f6;--ab-hover-bg-color:#f8f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ca1289;--ab-bg-color:#fce4f2;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ae8221;--ab-bg-color:#f9eac7;--ab-hover-bg-color:#fcf4e0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1463ff;--ab-bg-color:#e6eeff;--ab-hover-bg-color:#f0f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e51743;--ab-bg-color:#fce6e9;--ab-hover-bg-color:#fef5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#223f99;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6f00ff;--ab-bg-color:#f3e6ff;--ab-hover-bg-color:#faf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9ec500;--ab-bg-color:#cafc00;--ab-hover-bg-color:#e7ff84;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2ed167;--ab-bg-color:#EBFFF4;--ab-hover-bg-color:#d2ffe5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7e5efd;--ab-bg-color:#eeeafd;--ab-hover-bg-color:#f6f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4ab423;--ab-bg-color:#c9f7ba;--ab-hover-bg-color:#e5fbdd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#007235;--ab-bg-color:#9affca;--ab-hover-bg-color:#ccffe6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5145bf;--ab-bg-color:#e9e9f6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B9132F;--ab-bg-color:#fde9e9;--ab-hover-bg-color:#fef3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#160a70;--ab-bg-color:#ededfd;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4c4c4c;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f84b0b;--ab-bg-color:#fce8e2;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0C121D;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb609e;--ab-bg-color:#fefbfd;--ab-hover-bg-color:#fcf3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#191a1a;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7d40cd;--ab-bg-color:#d2bee4;--ab-hover-bg-color:#f8f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4360a4;--ab-bg-color:#e9e9f6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070740;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#F4EAF6;--ab-hover-bg-color:#f8f2f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#402E96;--ab-bg-color:#F5F5FA;--ab-hover-bg-color:#F5F5FA;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#005ac8;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6e01fa;--ab-bg-color:#f3e8fe;--ab-hover-bg-color:#f8f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e55775;--ab-bg-color:#fce5eb;--ab-hover-bg-color:#fef4f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#96c534;--ab-bg-color:#e3efc8;--ab-hover-bg-color:#f1f6e4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3694fc;--ab-bg-color:#94c2fa;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#016a4b;--ab-bg-color:#94fedf;--ab-hover-bg-color:#d0fef1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#194896;--ab-bg-color:#e5ebfc;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f2aa3;--ab-bg-color:#eeecf9;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6a8eb9;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FA530B;--ab-bg-color:#D9D2C3;--ab-hover-bg-color:#f7f4f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#847aff;--ab-bg-color:#F1F0FF;--ab-hover-bg-color:#f6f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#192d89;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#244481;--ab-hover-bg-color:#f2f5f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#4692c0;--ab-bg-color:#e6edf4;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3bb76b;--ab-bg-color:#dbf0e6;--ab-hover-bg-color:#ebf7f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#45ada0;--ab-bg-color:#d9efef;--ab-hover-bg-color:#edf7f7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb6334;--ab-bg-color:#fbe6de;--ab-hover-bg-color:#fef5f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#004b87;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#543af4;--ab-bg-color:#e9e9fd;--ab-hover-bg-color:#f3f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f47715;--ab-bg-color:#fae7d3;--ab-hover-bg-color:#fdf4ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#97c634;--ab-bg-color:#e4efc9;--ab-hover-bg-color:#f2f6e5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00c0ff;--ab-bg-color:#cdf0ff;--ab-hover-bg-color:#e6f8ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2da295;--ab-bg-color:#d4f0ee;--ab-hover-bg-color:#ecf7f7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#f7e8fc;--ab-hover-bg-color:#fbf2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0050a0;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e6007e;--ab-bg-color:#ffe6f3;--ab-hover-bg-color:#fff0f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3483DC;--ab-bg-color:#FFFFFF;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#191847;--ab-bg-color:#ececf7;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fd0084;--ab-bg-color:#ffe4f2;--ab-hover-bg-color:#fff3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#911211;--ab-bg-color:#c3a0a2;--ab-hover-bg-color:#fbf5f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#003655;--ab-bg-color:#d2f0ff;--ab-hover-bg-color:#e6f8ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c79817;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0772ec;--ab-bg-color:#72ffff;--ab-hover-bg-color:#c2ffff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de4a00;--ab-bg-color:#ffe5d9;--ab-hover-bg-color:#fff3ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc3229;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1E8AAE;--ab-bg-color:#EDF5F7;--ab-hover-bg-color:#f1f7f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#33285d;--ab-bg-color:#eaeaf6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#004d47;--ab-bg-color:#ade0bc;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FA3803;--ab-bg-color:#fce6dd;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c91589;--ab-bg-color:#fce4f0;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0069ff;--ab-bg-color:#dcedff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00aeef;--ab-bg-color:#ccf0ff;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#155d84;--ab-bg-color:#ddedfb;--ab-hover-bg-color:#f1f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f55cc;--ab-bg-color:#ebecf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#212121;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8566ab;--ab-bg-color:#f1ebf7;--ab-hover-bg-color:#f6f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6e69f3;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6239EB;--ab-bg-color:#F1EAFE;--ab-hover-bg-color:#f7f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#16a1e1;--ab-bg-color:#d1f0fa;--ab-hover-bg-color:#e5f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#339af0;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000116;--ab-bg-color:#e8e9ff;--ab-hover-bg-color:#f2f3ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff6600;--ab-bg-color:#ffe7d7;--ab-hover-bg-color:#fff3eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0c84d6;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc4c02;--ab-bg-color:#fde6dd;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00162b;--ab-bg-color:#daedff;--ab-hover-bg-color:#eef7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1568E4;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2bccbd;--ab-bg-color:#baf6e8;--ab-hover-bg-color:#ddfbf4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1d4354;--ab-bg-color:#e4edf4;--ab-hover-bg-color:#f0f4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070b46;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#45ada0;--ab-bg-color:#ececf7;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6A47FF;--ab-bg-color:#F6F3FF;--ab-hover-bg-color:#F6F3FF;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#cc0100;--ab-bg-color:#ffe6e5;--ab-hover-bg-color:#fff5f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0068ac;--ab-bg-color:#d4efff;--ab-hover-bg-color:#e8f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a04a21;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B9132F;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070A0D;--ab-bg-color:#F6F8F9;--ab-hover-bg-color:#f3f6f7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#086454;--ab-bg-color:#b5f7ec;--ab-hover-bg-color:#d8fbf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ed3831;--ab-bg-color:#fce5e5;--ab-hover-bg-color:#fef4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00255f;--ab-bg-color:#e1ecff;--ab-hover-bg-color:#f0f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FF6E00;--ab-bg-color:#E6E6E6;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#007b86;--ab-bg-color:#eeeeee;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4f90d3;--ab-bg-color:#e3eef8;--ab-hover-bg-color:#eff5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1563ff;--ab-bg-color:#e2ebff;--ab-hover-bg-color:#f1f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#001267;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000116;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3074d9;--ab-bg-color:#e1ecfc;--ab-hover-bg-color:#f0f5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#009c94;--ab-bg-color:#79fff7;--ab-hover-bg-color:#c4fff9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1888af;--ab-bg-color:#d2f0fa;--ab-hover-bg-color:#e6f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2611e7;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#096dd9;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0c0c0c;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#333366;--ab-bg-color:#eaeaf6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#263B2C;--ab-bg-color:#e1ede7;--ab-hover-bg-color:#f0f7f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#574feb;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#38645a;--ab-bg-color:#F4EFE9;--ab-hover-bg-color:#f8f4ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#85b1d5;--ab-bg-color:#e5ecf4;--ab-hover-bg-color:#f1f5f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e7aa00;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2aa3dc;--ab-bg-color:#d9effb;--ab-hover-bg-color:#edf7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#272958;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1f9387;--ab-bg-color:#c1f5ea;--ab-hover-bg-color:#dbfbf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1e94e6;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#29cf92;--ab-bg-color:#FF0079;--ab-hover-bg-color:#fff0f8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#21ae62;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f06292;--ab-bg-color:#fde9ee;--ab-hover-bg-color:#fef3f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0bb599;--ab-bg-color:#b7f7ee;--ab-hover-bg-color:#dafbf5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a23aff;--ab-bg-color:#f4e9ff;--ab-hover-bg-color:#f9f3ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#089488;--ab-bg-color:#ddd;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#87ca29;--ab-bg-color:#d4f5b2;--ab-hover-bg-color:#e8fad5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0079c1;--ab-bg-color:#d0f1ff;--ab-hover-bg-color:#e4f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4bc2fa;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2B8E94;--ab-bg-color:#f4f8fb;--ab-hover-bg-color:#f0f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#04c9b5;--ab-bg-color:#2c2c2c;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0b382b;--ab-bg-color:#c6f4e7;--ab-hover-bg-color:#defaf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0096DC;--ab-bg-color:#E5EFF7;--ab-hover-bg-color:#f1f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6228C0;--ab-bg-color:#eeebf7;--ab-hover-bg-color:#f4f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ae217e;--ab-bg-color:#fce5f2;--ab-hover-bg-color:#fef4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#1100A7;--ab-hover-bg-color:#f8f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#1a73e8;--ab-bg-color:#e2ebfc;--ab-hover-bg-color:#f1f4fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1D7EA9;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2366ad;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#dd0631;--ab-bg-color:#fbc1cd;--ab-hover-bg-color:#fef3f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#225d3a;--ab-bg-color:#f5f3ef;--ab-hover-bg-color:#f7f5f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4da0ff;--ab-bg-color:#deefff;--ab-hover-bg-color:#edf6ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FF5000;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f49600;--ab-bg-color:#ffe8bd;--ab-hover-bg-color:#fff4db;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7516D8;--ab-bg-color:#f1e6fc;--ab-hover-bg-color:#f9f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#911211;--ab-bg-color:#cd1b18;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#000000;--ab-bg-color:#002BFF;--ab-hover-bg-color:#f5f6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#ea0e89;--ab-bg-color:#fddaeb;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#03111f;--ab-bg-color:#deedfc;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1ecbb6;--ab-bg-color:#eaf5f5;--ab-hover-bg-color:#edf7f7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00B373;--ab-bg-color:#F9FAE4;--ab-hover-bg-color:#f6f7d8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2b7fd7;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#625DEC;--ab-bg-color:#EFEFFD;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2b2b2b;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f66334;--ab-bg-color:#f8fafc;--ab-hover-bg-color:#f0f4f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6432ff;--ab-bg-color:#efeaff;--ab-hover-bg-color:#f7f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0f50;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2e8ef0;--ab-bg-color:#ddecfb;--ab-hover-bg-color:#f1f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#616194;--ab-bg-color:#ececf4;--ab-hover-bg-color:#f5f5fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae37;--ab-bg-color:#bdf8cc;--ab-hover-bg-color:#e0fce7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2A9D8F;--ab-bg-color:#efefef;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3E95D1;--ab-bg-color:#deecf9;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#4a4a4a;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#f98858;--ab-bg-color:#fbe6df;--ab-hover-bg-color:#fdf2ee;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0083b3;--ab-bg-color:#ccf0ff;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#58cd8a;--ab-bg-color:#dcf0e7;--ab-hover-bg-color:#ecf7f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e1676a;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2365a9;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6eb3f3;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#365742;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8FC640;--ab-bg-color:#cccccc;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#489be5;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#272a3b;--ab-bg-color:#e9ecf0;--ab-hover-bg-color:#f2f4f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#68aaef;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#305079;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#14467a;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#56296f;--ab-bg-color:#5a96f0;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1100A7;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5d1996;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ffa200;--ab-bg-color:#ffe9b4;--ab-hover-bg-color:#fff3d7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5a96f0;--ab-bg-color:#143644;--ab-hover-bg-color:#f0f7f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#14467a;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#402e96;--ab-bg-color:#ebebf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b5b5b5;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#be2026;--ab-bg-color:#cfcfce;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9e1e51;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#d45e30;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#657220;--ab-bg-color:#D9F400;--ab-hover-bg-color:#eeff4f;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f28e00;--ab-bg-color:#ffe7ca;--ab-hover-bg-color:#fff4e8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#10c180;--ab-bg-color:#b9f7df;--ab-hover-bg-color:#dcfbed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8353f9;--ab-bg-color:#eee9fd;--ab-hover-bg-color:#f5f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#158C7B;--ab-bg-color:#eaf0f7;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5fa846;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae6a;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1E3D59;--ab-bg-color:#BBF245;--ab-hover-bg-color:#eef9cc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3a4958;--ab-bg-color:#e7ecf0;--ab-hover-bg-color:#f3f6f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#199aab;--ab-bg-color:#d4f1fa;--ab-hover-bg-color:#e8f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5646C0;--ab-bg-color:#ddf1dd;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6c26db;--ab-bg-color:#ede8fc;--ab-hover-bg-color:#f5f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#74b01a;--ab-bg-color:#d1f6ac;--ab-hover-bg-color:#e8fad4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#38616f;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#008179;--ab-bg-color:#77fff7;--ab-hover-bg-color:#c7fffa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fe5d27;--ab-bg-color:#fee6db;--ab-hover-bg-color:#fef3ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#18181b;--ab-bg-color:#abdeed;--ab-hover-bg-color:#e7f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff4451;--ab-bg-color:#ffe4e7;--ab-hover-bg-color:#fff3f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#050523;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#245dc1;--ab-bg-color:#e8ebff;--ab-hover-bg-color:#f2f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8b5cf7;--ab-bg-color:#ede8fc;--ab-hover-bg-color:#f5f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae72;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21aea1;--ab-bg-color:#e3fdf8;--ab-hover-bg-color:#d9fdf6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b64ee;--ab-bg-color:#ececfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0e71cc;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#24cca7;--ab-bg-color:#b6f7e7;--ab-hover-bg-color:#d9fbf2;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5bbb7b;--ab-bg-color:#dbf0db;--ab-hover-bg-color:#ebf7eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8757f9;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2485cc;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#005eb8;--ab-bg-color:#dbedff;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00A2E9;--ab-bg-color:#DBF4FF;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3c9a35;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B91C1C;--ab-bg-color:#F3F4F6;--ab-hover-bg-color:#F3F4F6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4d5259;--ab-bg-color:#edf1f2;--ab-hover-bg-color:#f3f5f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0f2db3;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#278b9b;--ab-bg-color:#d6f0f5;--ab-hover-bg-color:#eaf7fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#226da7;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#30cabb;--ab-bg-color:#c9f4ec;--ab-hover-bg-color:#e1faf5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c490d1;--ab-bg-color:#f0e8f5;--ab-hover-bg-color:#f7f4fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e51ee5;--ab-bg-color:#fce6fc;--ab-hover-bg-color:#fdf0fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006EDB;--ab-bg-color:#DBEDFF;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4ab848;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff93c1;--ab-bg-color:#ffe3f1;--ab-hover-bg-color:#fff2f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7862A5;--ab-bg-color:#f1ebf7;--ab-hover-bg-color:#f6f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006d00;--ab-bg-color:#a9ffa9;--ab-hover-bg-color:#d6ffd6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00cc9f;--ab-bg-color:#8bffe4;--ab-hover-bg-color:#ccfff1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2D2836;--ab-bg-color:#FBF9FE;--ab-hover-bg-color:#f5f1fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#121313;--ab-bg-color:#e8ebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#268ff9;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff361d;--ab-bg-color:#ffe7c3;--ab-hover-bg-color:#fff3e1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#20A8D8;--ab-hover-bg-color:#e8f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2350af;--ab-bg-color:#e6ecfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#01a770;--ab-bg-color:#eff8f2;--ab-hover-bg-color:#ebf6ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#db372d;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#009017;--ab-bg-color:#a4ffb1;--ab-hover-bg-color:#d6ffdb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#142b3a;--ab-bg-color:#e6edf4;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6639b7;--ab-bg-color:#ede8f5;--ab-hover-bg-color:#f6f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2179e2;--ab-bg-color:#e4eefc;--ab-hover-bg-color:#f3f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f6922e;--ab-bg-color:#fae7d3;--ab-hover-bg-color:#fdf4ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3b82f6;--ab-bg-color:#e5ebfc;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#96c534;--ab-bg-color:#394915;--ab-hover-bg-color:#f1f6e4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#7921ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0000;--ab-bg-color:#ffe6e6;--ab-hover-bg-color:#fff5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b4a172;--ab-bg-color:#3e4462;--ab-hover-bg-color:#f1f5f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#fda22f;--ab-bg-color:#02012D;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#9146ED;--ab-bg-color:#e7e513;--ab-hover-bg-color:#f8f8bd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e7aa00;--ab-bg-color:#ffe9b5;--ab-hover-bg-color:#fff4d8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ee9cc9;--ab-bg-color:#10092a;--ab-hover-bg-color:#f5f3fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}</style><link rel="stylesheet" href="../cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"/><link rel="preload" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/css/b69acd3085e4480a.css" as="style"/><link rel="stylesheet" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/css/b69acd3085e4480a.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/polyfills-42372ed130431b0a.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5810.e6a0789a46dba285.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1cc2734a-a2bd46b48dcfb414.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1976-3a1e16ed39f257ff.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4747-66e20966a06d24d8.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7764-f3b14f364a0d1b52.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/416-97c1497291412984.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4225-22f657dc0f739d91.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1539-00ecead0d9ee55cc.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/6652-fc6900588edd6271.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5176-afc4cc13abd1d35c.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9370-c81561713f82c466.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/111-6561208ec0d2be20.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7020-baaec7260f5e6fc1.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9292-f2fe7a82877d47bf.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5089.32630609a654ddd8.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5159-41323e3b79d2d278.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/509-bc3789384db0db85.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4638-8cca8ce68235216a.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5118-075b126111014c7d.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4024-970f73672e22cd2c.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7968-7d9b753edf6c1f99.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7187-496bdd6418e0be57.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3395.9eef5e529d29ff6f.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/8992-017f87979f45ecc7.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2541.bb0abb704527903d.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3156.a91f1bbd6e134c38.js"></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/webpack-91f79a938b1b1dfb.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/framework-5648639edb99e1bd.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/main-2255b1a2dca1d2dd.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/pages/_app-73995db85270ee67.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/48e0f7fa-2695100ece8812c4.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2edb282b-e1fef83e163cf249.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9a20ca01-4cbfe13ddba0b528.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/91bbf309-07ffab0a9657d31d.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/8bd53eb9-3850191f2ece3222.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3514-c52c7aad05a7e48f.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7422-4c22c8698c393ea5.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3424-375f0b69ace252f6.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9442-be30be087aa68cc6.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/562-dc8635dfded4d697.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5913-96d7a578979c260a.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/6342-6d0fbb2ac6aa8697.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2893-f98caa64b2e95191.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9849-f5651e5e76c2f026.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/pages/public/%5b%5b...slug%5d%5d-26ef0790e35d5b2c.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/dBe88brPNkUEoyQSYuIle/_buildManifest.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/dBe88brPNkUEoyQSYuIle/_ssgManifest.js" defer=""></script><meta name="sentry-trace" content="b5c20cb4cfb648de26cd68b9adebfc89-2433e2862444c5aa-1"/><meta name="baggage" content="sentry-environment=live,sentry-release=96c7914b49966e400b4f53ac904fa80945d640a2,sentry-public_key=ad82dc9f244e490fbe33eeb465274185,sentry-trace_id=b5c20cb4cfb648de26cd68b9adebfc89,sentry-sample_rate=1,sentry-transaction=GET%20%2Ftemplates,sentry-sampled=true"/><style id="__jsx-2251086973">#nprogress{pointer-events:none}#nprogress .bar{background:#2166ae;background:--ab-accent-color;position:fixed;z-index:1031;top:0;left:0;width:100%;height:2px}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;-webkit-box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;-moz-box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;opacity:1;-webkit-transform:rotate(3deg)translate(0px,-4px);-ms-transform:rotate(3deg)translate(0px,-4px);-moz-transform:rotate(3deg)translate(0px,-4px);-o-transform:rotate(3deg)translate(0px,-4px);transform:rotate(3deg)translate(0px,-4px)}</style></head><body style="display:block"><div id="__next"><div class="h-full w-full"><link rel="dns-prefetch" href="../external.html?link=https://app.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://app.archbee.com/"/><link rel="dns-prefetch" href="../external.html?link=https://cdn.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://cdn.archbee.com/"/><div class="h-full w-full" style="visibility:hidden"><div data-overlayscrollbars-initialize="" id="docs-scroll-container" class="ab-space ab-collection min-h-full min-w-full h-full dark:text-white fixed top-0 left-0 right-0 bottom-0 print:overflow-visible print:!static print:!h-auto"><div data-overlayscrollbars-contents=""><div class="ab-space-container ab-collection-container flex flex-col w-full justify-center bg-white dark:bg-gray-900"><nav role="navigation" class="ab-top-navbar flex flex-col z-20 sticky top-0 items-center bg-white/70 dark:bg-gray-900/80 border-b border-gray-100 dark:border-gray-800 css-1dgdik1" id="ab-public-nav-header"><div class="w-full mx-auto px-7"><div class="flex items-center justify-between w-full py-4"><a class="ab-public-logo ab-tab-focus flex items-center max-w-[160px] h-[48px] relative justify-start" tabindex="0" href="../external.html?link=https://vast.ai/" aria-label="website logo"><img src="../external.html?link=https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/hY3Z66NYu_wT-evx8EFi5_logo-symbol-dark.svg?format=webp&amp;width=400" class="w-full block mx-auto py-1 object-contain css-u4v8xq" alt="Website logo"/></a><div class="flex items-center print:hidden"><div class="flex items-center text-gray-400 cursor-pointer mx-4"><div class="flex items-center xl:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 cursor-pointer css-v5vzns"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 cursor-pointer stroke-current"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg></div><div class="flex items-center gap-2"><div class="flex items-center justify-center p-2.5 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-400 dark:text-gray-100" type="button" id="radix-:R1kijamm:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg></div></div></div></div><div class="hidden xl:flex flex-1 items-center justify-end gap-2"><a href="../external.html?link=https://cloud.vast.ai/" tabindex="0" class="ab-external-link-btn py-3 px-7 font-semibold rounded-lg text-lg btn-blue relative inline-flex items-center leading-6 css-1fl8vw4">Console</a><a href="../external.html?link=https://discord.gg/hSuEbSQ4X8" tabindex="0" class="ab-external-link-btn py-3 px-7 font-semibold rounded-lg text-lg btn ab-tab-focus text-gray-700 dark:text-gray-100 hover:text-gray-800 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 undefined&#x27;,">Discord</a></div></div><div class="no-print hidden xl:flex w-full pt-1 overflow-x-auto ab-scrollbars"><div class="ab-public-space-links-wrap flex items-center flex-1 -left-4"><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg css-1t9hfmb" role="link" tabindex="0"><div class="w-auto max-w-250 truncate font-semibold text-gray-900 dark:text-white" data-state="closed">Guides</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">Instances</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">Serverless</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">API</div></div></div></div></div></nav><style data-emotion="css 14yoxd">.css-14yoxd{z-index:1200;}</style><div class="ab-space-content ab-collection-content w-full mx-auto relative xl:flex xl:flex-row justify-between px-7 xl:px-0"><div class="ab-tree-navigation no-print sticky flex-col border-r border-gray-100 dark:border-gray-800 w-[360px] hidden xl:flex xl:flex-shrink-0 transition-width transition-slowest ease" style="top:100px;height:calc(100vh - 100px);max-height:calc(100vh - 100px)"><div class="ab-left-nav-public-header flex flex-col w-full px-6 xl:px-7 xl:pt-7 pb-6 !px-0"><div class="flex w-full xl:hidden ab-space-navigation mb-4"><div class="flex flex-col w-full p-6 bg-gray-200 border-b border-gray-300 dark:bg-gray-700 dark:border-gray-600 ab-space-navigation"><div class="flex justify-between font-semibold items-center text-gray-700 dark:text-gray-200"><span>Navigate through spaces</span><div class="text-gray-400 dark:text-gray-500 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="strike-current"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></svg></div></div></div></div><div role="search" class="ab-public-search flex justify-center px-6 xl:px-7"><div class="w-full flex h-11 items-center border pl-4 pr-2 rounded-lg hover:shadow-sm border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-xs"><div class="w-6 h-6 flex items-center justify-center mr-0 lg:mr-2 shrink-0 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="css-v5vzns"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg></div><input class="ab-search-input border-none bg-transparent placeholder-gray-400::placeholder w-full leading-none" placeholder="Search or ask..." value=""/><kbd class="inline-flex justify-center items-center p-2 rounded border border-gray-300 dark:border-gray-700 align-middle leading-3 cursor-default text-gray-600 dark:text-gray-400 border-none bg-gray-50 dark:bg-gray-800 font-medium mx-0 ml-1 shadow-none" style="height:22px;min-width:22px">⌘</kbd><kbd class="inline-flex justify-center items-center p-2 rounded border border-gray-300 dark:border-gray-700 align-middle cursor-default text-gray-600 dark:text-gray-400 text-xs border-none bg-gray-50 dark:bg-gray-800 font-medium mx-0 ml-1 shadow-none" style="height:22px;min-width:22px">K</kbd></div></div></div><div data-overlayscrollbars-initialize="" id="ab-left-nav-public-wrap" class="ab-left-nav-public scroll-smooth flex flex-1 flex-col items-center max-h-full xl:px-7 xl:pb-5"><div data-overlayscrollbars-contents=""><div role="navigation" class="w-full relative overflow-x-hidden px-6 xl:px-0"><div title="Overview"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-1b94hea" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Overview"><span class="truncate">Overview</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron transform rotate-90 text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a><div class="flex-1 max-w-full pl-7 ml-4"><div title="Introduction"><div href="index.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="Introduction"><span class="truncate">Introduction</span></div></a></div></div><div title="QuickStart"><div href="quickstart.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="QuickStart"><span class="truncate">QuickStart</span></div></a></div></div><div title="FAQ"><div href="faq.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="FAQ"><span class="truncate">FAQ</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div></div></div></div><div title="Use Cases"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Use Cases"><span class="truncate">Use Cases</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Teams"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Teams"><span class="truncate">Teams</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Hosting"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Hosting"><span class="truncate">Hosting</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Distributed Computing"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Distributed Computing"><span class="truncate">Distributed Computing</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Console"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Console"><span class="truncate">Console</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Specific GPUs"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Specific GPUs"><span class="truncate">Specific GPUs</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron transform rotate-90 text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a><div class="flex-1 max-w-full pl-7 ml-4"><div title="RTX 5 Series"><div href="jDpX-XUZdy-zKWbAZQPNK.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="RTX 5 Series"><span class="truncate">RTX 5 Series</span></div></a></div></div></div></div></div></div></div><div class="px-[24px] flex my-4"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-full"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div><div role="main" class="ab-center-column md:flex justify-center grow xl:px-7 xl:overflow-x-hidden"><div class="w-full max-w-[768px]" id="main-column"><div><div class="flex flex-col justify-center pt-4 xl:pt-7"><div class="flex flex-1 w-full justify-center pb-0"><div class="flex flex-initial flex-col w-full"><div class="flex items-center pl-0 pr-2 mb-5 xl:-mt-1.5"><div class="flex"><div><div class="flex text-gray-400 flex-wrap"><div class="flex"><div class="flex pr-2 text-gray-500 dark:text-gray-400 items-center font-semibold css-1b4fjyx">Console</div></div></div></div></div></div><h1 class="ab-doc-name h1 font-bold text-5xl break-words w-full max-w-full mt-0 pb-0 xl:-mt-1.5 mb-0 css-ydpz48">Templates</h1><div class="flex gap-6 text-sm mt-5 mb-6 text-gray-400 dark:text-gray-500"><div class="flex items-center gap-1.5 flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="1rem" height="1rem" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="stroke-current"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg><span>28 min</span></div></div><div><div class="max-h-dvh invisible"> what is a template? a template is how vast helps you launch an instance, setting up your rented machine with whatever software and formatting you need templates are generally used for launching instances through the web interface, but they can also be used in the cli or through the api in this document, we will focus on the web interface, but we will link to other relevant documentation thoughout in the simplest technical terms, you can consider a template to be a wrapper around docker run the template contains all of the information you want to pass to our systems to configure the environment you can browse the template section of the web interface at cloud vast ai/templates recommended templates we provide several recommended templates to help you get started these are pre configured environments that you can use as is, or you can tweak them to your own requirements it&#x27;s a great idea to look at how these templates have been configured to guide you in creating your own find out more about our recommended templates in the vast templates https //docs vast ai/templates#oh7ms section later in the guide quick start to jump right in and run a template, follow these steps visit the templates section of the console where you will find all of our recommended templates recommended templates page browse through the templates until you find one that meets your requirements in this guide we will use nvidia cuda, which is the first on the list it&#x27;s a great starter template as it just includes the cuda development environment, plus a few extras to improve the user experience nvidia cuda template whos play button in bottom left corner now, click the &#x27;play&#x27; button this will load the template and take you to the available offers offers page with gpu filter active there are filters available at the top of the page to help you target a particular gpu you will also find many additional filters on the left of the page for more fine grained control over the instances you find when you have found a suitable offer, simply click the &#x27;rent&#x27; button to create your new instance you can now visit cloud vast ai/instances https //cloud vast ai/instances/ where you will find your running instance it may take a few minutes to be ready as everything is being set up instance view with blue open button when it is ready you will see the blue open button this indicates that the instance is ready to connect the action of the open button depends on the template you have chosen in this example you will be transferred to the instance portal later in this guide we will discuss how this can be configured creating your own template you can create a new template from scratch, or you can edit an existing template for this gude we will edit the nvidia cuda template we used in the quick start section from the templates page, click the pencil icon on the template card to open up the template editor you&#x27;ll see two tabs config and readme we&#x27;ll start with the config tab identification the first section helps you to keep your templates organized identification section of the template editor template name this will be displayed in bold on the template card choose something that helps you identify the template amongst your other templates for this guide, we will change the name to &#x27;nvidia cuda demo&#x27; template description this field helps describe the function and purpose of the template completely optional for your own purposes, but very helpful if you intend to make this template public or share it with others docker repository and environment this is where you define the docker image you want to run, along with any options we want to pass into the container docker section of the template editor image path\ tag here is where you can define the docker image to run this field must be in the format repository/image name\ tag many of our templates pull from dockerhub but you can use any container registry just remember to add the full path if you&#x27;re using an alternative registry eg nvcr io/nvidia/pytorch 25 04 py3 version tag for many registries we are able to pull the available list of tags so this field allows you to quickly select another version there is also a special \[automatic] tag you can use with this selected, the machine you choose for your instance will pull the most recent docker image that is compatible with that machine&#x27;s own cuda version this will only work if the image tag contains the cuda version string for example my image cuda 12 8 would be loaded on a machine supporting cuda 12 8, but a machine with only cuda 12 6 would pull my image cuda 12 6 docker options this field is a textual representation of the ports and environment variables declared in the sections beneath it you can edit it directly or you can use the page widgets this field will only accept ports and environment variables other docker run options will be ignored ports to access your instance via the external ip address, you will need to add some ports to the template you can add both tcp and udp ports when your instance is created, a port will be randomly assigned to the external interface which will map into the instance port you selected environment variables here you can add any environment variables that your docker image requires do not save any sensitive information here if you are planning to make the template public place any variables with sensitive values into the environment variables section of your account settings page they will then be made available in any instance you create, regardless of the template used you can find out more about port mapping and special environment variables in our docker execution environment guide select launch mode templates offer three launch modes you can select from our recommened templates will usually launch in jupyter mode for easiest access, but you are free to choose whichever suits your needs launch mode selection options jupyter python notebook + ssh when you run the template in this mode, we will install jupyter and ssh at runtime jupyter will be available on mapped port 8080 and ssh will be available on mapped port 22 interactive shell server, ssh as above, but ssh only with no jupyter installation in both jupyter and ssh mode, the docker entrypoint for your image will not be run it will be replaced with our instance setup script so you should use the on start section (documented below) to start any services docker entrypoint in this mode, your docker image will run precisely as it is we will not include any additional software or access methods if your docker image does not offer ssh or another appropriate interface, please select one of the alternative modes if you need to interact with the running instance an additional field will be showin when using this launch mode to allow passing arguments to the image entrypoint field allowing for argument passing on start script here you can enter a short bash script which will be run during instance startup it is only available when using the jupyter or ssh launch modes, and is most useful for starting any services that your docker image would have launched if run if the entrypoint had been executed in our example &#x27;nvidia cuda&#x27; template we are simply calling the entrypoint sh script which contains the logic for preparing the instance extra filters use this area to place restrictions on the machines that should show up in the search page when the template is selected extra filters showing this template is configured for both amd64 and arm64 cpus docker repository authentication if you are using a private docker image then you will need to add authentication credentials so the machine running the instance can download it disk space by setting the disk space in the template, you can ensure that new instances created from the template with use this amount as a minimum template visibility any template marked as public will be available in the template search system, while private images will not private templates can still be used by others if you have shared the template url never save a template as public if it contains sensitive information or secrets use the account level environmnet variables as an alternative cli command templates can be translated directly into cli launch commands this read only area shows what you would need to type or copy to the cli if you wanted to programatically launch an instance this way launch a template via the cli to learn more about starting instance from the cli, check out our quickstart guide save the template finally, you can save the template if you are creating a new template or editing one which is not associated with your account such as one of our recommended templates the buttons you see will be labelled &#x27;create&#x27; for your own templates, you will see them labelled &#x27;save&#x27; buttons for saving the &#x27;create&#x27; button will create a copy of the templaye in the &#x27;my templates&#x27; section of the templates page for you to use later the &#x27;create &amp; use&#x27; button will save the template, load it and then open up the offers page updating a template if you want to make changes to a template you previously saved, simply navigate back to the templates page and select &#x27;my templates&#x27; here you&#x27;ll be able to make your changes by clicking the pencil icon my templates showing the nvidia cuda demo template sharing a template it&#x27;s really easy to share your template with other users we have two special links you can use and both include your referral code so you can earn if new users sign up find more about that here to share, click the three dots icon in the bottom right of the template card menu shows sharing options copy referral link this will copy a link that contains your referral id, creator id and the template name it will always point to the most recent template you created with this name really useful if you want people clicking the link to always get the most recent version copy template link this will copy a link containing your referral id and the template hash id it points to this specific template at this point in time templates all have a unique hash after every save this is useful as it allows you to find a previous version if you have tracked the hash id, but for sharing you probably want the referral link above remember to add a comprehensive readme to your template if you&#x27;re going to share it this will help users to get started easily vast templates templates that we have created will often be found in the recommended section as they go through a qa process to ensure they function correctly and provide a positive user experience they contain extras not found in all templates so we will address the differences here instance portal you will notice that after clicking the open button on template running one of the vast ai templates a screen like this will appear instance portal landing page this is our instance portal and it provides easy to access links to services running in your instance it places an authentication layer in front of these services to prevent access by anyone who does not have the correct authentication token you are also able to create tunnels to your services without exposing ports full documentation for the instance portal is available here provisioning script vast ai templates support running a remote script on start to help configure the instance and download models and extensions that may not already be available in the docker image to use this feature, simply add environment variable provisioning script and set the value to a plain text shell script url the file will be downloaded and run on first start of the instance use the provisioning script to add a twist to an existing recommended template by downloading custom models and extensions base image you can find the source code for the docker images we used to create the vast templates on github these are large docker images that contain cuda development libraries, node + npm, opencl and other useful libraries despite their large size you&#x27;ll find they generally start quickly because they have been cached on many of the host machines this makes them ideal for using as a base for your own images virtual machine templates currently we offer two vm templates; a cli and a desktop gui these differ from a standard template because they launch a full virtual machine environment rather than a docker container you can edit these templates as described above, but you should not change the docker image field only the images we distribue from docker io/vastai/kvm will work, but feel free to add extra environment variables and ports use the vm templates when you need to run applications that require namespace support run more than one docker container in an instance load kernel modules or run profiling jobs mount remote drives with rclone or similar advanced configuration for advanced template configuration options and variables, please see the docker execution environment guide </div></div></div></div><div class="flex flex-col"><div class="no-print flex flex-col justify-end sm:flex-col w-full max-w-full pt-16"><div class="ab-doc-template-footer-container flex flex-col md:flex-row md:pt-7 md:justify-between sm:items-center 2xl:pb-7 border-t border-gray-100 dark:border-gray-800"><div class="flex flex-1 flex-col 2xl:mb-0 p-6 md:p-0 items-center md:items-start"><div class="flex items-center text-gray-500 dark:text-gray-400 text-base p-1"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1.5 stroke-current"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>Updated<!-- --> <!-- -->13 Jun 2025</div></div><div><div class="flex flex-col items-center p-6 pr-0 w-full border-t border-gray-100 dark:border-gray-800 2xl:items-center 2xl:justify-end 2xl:p-0 md:rounded md:items-end md:border-none md:w-fit md:p-4 md:pr-0 bg-white dark:bg-transparent text-gray-600 dark:text-gray-300"><div class="flex w-full justify-center md:justify-start text-base mb-4 md:mb-2 text-gray-500 dark:text-gray-300">Did this page help you?</div><div class="flex flex-wrap md:flex-nowrap justify-center md:justify-end"></div></div></div></div><div class="border-t border-gray-100 dark:border-gray-800 my-7 2xl:mt-0"></div><div class="flex flex-col lg:flex-row justify-between w-full dark:border-gray-800"><a title="CLI" tabindex="0" class="ab-nav-left ab-tab-focus flex flex-1 items-center lg:max-w-[calc(50%-0.5rem)] justify-start text-left mb-2 rounded-2xl cursor-pointer py-3 px-4 bg-gray-50 dark:bg-gray-850 hover:bg-gray-100 dark:hover:bg-gray-800 css-ypjd7c" href="cli.html"><div class="mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="navigation-arrow stroke-current text-gray-300 dark:text-gray-500 transition-all"><circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line></svg></div><div class="max-w-[90%]"><div class="text-gray-500 dark:text-gray-400 text-xs font-bold mt-0.5 text-left">PREVIOUS</div><div class="ab-nav-left-text text-left font-bold text-lg truncate">CLI</div></div></a><a title="Instance Portal" tabindex="0" class="ab-nav-right ab-tab-focus flex flex-1 items-center lg:max-w-[calc(50%-0.5rem)] justify-end text-right mb-2 rounded-2xl cursor-pointer py-3 px-4 bg-gray-50 dark:bg-gray-850 hover:bg-gray-100 dark:hover:bg-gray-800 css-ypjd7c" href="instance-portal.html"><div class="max-w-[90%]"><div class="text-gray-500 dark:text-gray-400 text-xs font-bold mt-0.5">NEXT</div><div class="ab-nav-right-text text-right max-w-full font-bold text-lg truncate">Instance Portal</div></div><div class="ml-3"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="navigation-arrow stroke-current text-gray-300 dark:text-gray-500 transition-all"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></svg></div></a></div><div class="flex justify-center mt-12"><div class="my-2 flex mb-8 xl:hidden"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-[248px]"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div></div></div></div></div></div></div><div class="hidden"><div class="w-0 h-0 fixed right-0 bottom-0 pr-2 pt-7 xl:h-screen xl:w-1/3 xl:min-w-[33.33%] 2xl:min-w-[auto] 2xl:max-w-[450px] bg-white dark:bg-gray-900" style="top:100px"><div class="overflow-hidden h-full"><div id="ab-code-drawer" class="h-full"></div></div></div></div><div class="hidden xl:block w-[256px] 2xl:w-[360px] xl:flex-shrink-0"><div class="ab-right-column"><div class="ab-toc-container"><div id="ab-toc-portal"></div></div></div><div class="my-2 hidden"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-full"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div></div></div></div></div></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"hostname":"docs.vast.ai","pdfExport":false,"showToC":true,"shareableToken":"","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","docId":"/templates","_doc":{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","name":"Templates","icon":"","title":"Templates","previewImageURL":"","summary":null,"urlKey":"templates","description":"","urlAlias":"","data":{"nodes":[{"id":"CpgO8sqy_Q-ayJU_DTIEd","type":"h2","children":[{"text":"What is a Template?","id":"o8XfUHePSNsYM5-5wH90f"}]},{"id":"V6pPwja_CtiiCLZYNI-0x","type":"paragraph","children":[{"text":"A template is how Vast helps you launch an instance, setting up your rented machine with whatever software and formatting you need. Templates are generally used for launching instances through the web interface, but they can also be used in the CLI or through the API.  In this document, we will focus on the web interface, but we will link to other relevant documentation thoughout.","id":"7wihzvBlnYBuT_0BaUyyJ"}]},{"id":"POZHA74GsWck_gg0uOf85","type":"paragraph","children":[{"text":"In the simplest technical terms, you can consider a template to be a wrapper around ","id":"wcLXnHn_RELAh4E-Tqsk_"},{"text":"docker run","highlight":true,"id":"QDAvgO47bDGtx9ZGgksji"},{"text":". The template contains all of the information you want to pass to our systems to configure the environment.","id":"laMktbnJgemg1VXBB5wzt"}]},{"id":"voAr4ObBAFj8g5zyuOMe7","type":"paragraph","children":[{"text":"You can browse the template section of the web interface at ","id":"64xBbq3L3sAbUwdnsdxsb"},{"id":"5QvmsWMPidVdtItz3QYcL","type":"link","data":{"href":"https://cloud.vast.ai/templates/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"cloud.vast.ai/templates","id":"W149oEwqdIzgsqUiDERwM"}]},{"text":"","id":"qmVH9LgB8zc8eKDYdjDoh"}]},{"id":"ZrLsOvjaSiIb_Cc43-NTi","type":"h2","children":[{"text":"Recommended Templates","id":"P_Nwc_Jhl1Ad5x2CmaMTf"}]},{"id":"BJKoQvjVPKuFSU4z6Gkfo","type":"paragraph","children":[{"text":"We provide several recommended templates to help you get started.  These are pre-configured environments that you can use as-is, or you can tweak them to your own requirements.  ","id":"LEfO1R1ujcItEc-kLR5yR"}]},{"id":"7927KFLcg8ZDx03uv6O0p","type":"callout-v2","data":{"type":"info"},"children":[{"id":"bXyjihnP6-LH0jnakK9zS","type":"paragraph","children":[{"text":"It's a great idea to look at how these templates have been configured to guide you in creating your own.","id":"TdETJu_YVf3FBrHkG8bH8"}]}]},{"id":"twSx9CwF5eP3yZ9tsm4Gc","type":"paragraph","children":[{"text":"Find out more about our recommended templates in the ","id":"B_Z5VxrxtH8UsltM9foo4"},{"id":"V5xrR3g7VZ_tIhl3p8gw0","type":"link","data":{"href":"https://docs.vast.ai/templates#oh7MS","newTab":false,"hasDisabledNofollow":false},"children":[{"id":"B_Z5VxrxtH8UsltM9foo4","text":"Vast Templates"}]},{"id":"B_Z5VxrxtH8UsltM9foo4","text":" section later in the guide."}]},{"id":"HSXU4MKrrmwYj95JyFuuZ","type":"h2","children":[{"text":"Quick Start","id":"x9XL8-IaPLyn1CnJmgzIc"}]},{"id":"p755T8YqNFLJPmoZICcOx","type":"paragraph","children":[{"text":"To Jump right in and run a template, follow these steps","id":"uJuUdxZBHCxHkAgvnIT8y"}]},{"id":"YfRscWmrKmkEsT63K30qs","type":"paragraph","children":[{"text":"Visit the templates section of the console where you will find all of our recommended templates.","id":"xFIPUvmpy3jni2Y6wjy_n"}]},{"id":"Fof9Ugr4rOg_R71IX2BEM","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/iHVunKrkpyXVJdTsz6NgP_image.png","signedSrc":"","size":100,"width":1707,"height":777,"position":"center","caption":"Recommended Templates","alt":"Recommended Templates page"},"children":[{"text":"","id":"UFRK_e28vnJGIJ52c3JOq"}]},{"id":"g8cyoZvroTbh_IDGHsSyI","type":"paragraph","children":[{"text":"Browse through the templates until you find one that meets your requirements.  In this guide we will use NVIDIA CUDA, which is the first on the list. It's a great starter template as it just includes the CUDA development environment, plus a few extras to improve the user experience.","id":"XLBpj4UvpS6SL1PV6l3pR"}]},{"id":"mAb3gBotPKD8hOOXl07Ct","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/dlaQQ5RGmCbntmi23IHEI_image.png","signedSrc":"","size":100,"width":417,"height":274,"position":"center","caption":"NVIDIA CUDA template","alt":"NVIDIA CUDA Template whos play button in bottom left corner"},"children":[{"text":"","id":"sakYzP1KZaSpvYidTGqbg"}]},{"id":"Kb34JqtsIqDjJSO9HVT4j","type":"paragraph","children":[{"text":"Now, click the 'play' button.  This will load the template and take you to the available offers","id":"b_NBG3ryDUJupQDvHPeMI"}]},{"id":"ZTbsUhMbsbR64Kd-wI2-u","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/Rs9SQ5RokRrAcYSHBGX3L_image.png","signedSrc":"","size":100,"width":1201,"height":837,"position":"center","caption":"Offers Page","alt":"Offers page with GPU filter active"},"children":[{"text":"","id":"np3aMPRm12er-SiX5zLyr"}]},{"id":"_3fvWKsd0o0zL_eLtyJXl","type":"paragraph","children":[{"text":"There are filters available at the top of the page to help you target a particular GPU.  You will also find many additional filters on the left of the page for more fine-grained control over the instances you find.","id":"OBQdmTK4sPhkHYZ-VhOBs"}]},{"id":"CJ-ZBSxzdeLtOijXx0B7s","type":"paragraph","children":[{"text":"When you have found a suitable offer, simply click the 'RENT' button to create your new instance.","id":"4kI79k5NKe9FXtow7i8QZ"}]},{"id":"B5GwN0C5qD2lC38L1cE_e","type":"paragraph","children":[{"text":"You can now visit ","id":"4sAUnlchnxJlVzemhOTGP"},{"id":"D0U20COKer3wpInEczzXK","type":"link","data":{"href":"https://cloud.vast.ai/instances/"},"children":[{"text":"cloud.vast.ai/instances","id":"3sSP2S7G302G3g-95-7Q2"}]},{"text":" where you will find your running instance.  It may take a few minutes to be ready as everything is being set up.","id":"9_n3kZq4MS4q9rTU8olSC"}]},{"id":"w7m_tmxb6lAxD5PE4W4C3","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/sy34F-FInPC77OnukpW6o_image.png","signedSrc":"","size":100,"width":906,"height":718,"position":"center","caption":"Instance is Ready","alt":"Instance view with blue open button"},"children":[{"text":"","id":"-wgDSyAPD3feNM12Old4q"}]},{"id":"YbDcQAtB4Qtaru0Of4T6l","type":"paragraph","children":[{"text":"When it is ready you will see the blue open button.  This indicates that the instance is ready to connect.","id":"ArstBULv4zKlIP7Vhtet-"}]},{"id":"Qv12hbNa1TrCP6nfqxAln","type":"callout-v2","data":{"type":"info"},"children":[{"id":"VvDTXY_zP3z6RJp7urwVf","type":"paragraph","children":[{"text":"The action of the open button depends on the template you have chosen - In this example you will be transferred to the ","id":"V-T-vHt_dTY0IlDBq0N4S"},{"id":"56LnTpf9GXlr23DAHcKxP","type":"link","data":{"href":"https://docs.vast.ai/instance-portal","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Instance Portal","id":"ecpissPkjCmOjEVp68U-f"}]},{"text":".  Later in this guide we will discuss how this can be configured.","id":"iyeXQV5spsEpMrC4vySuC"}]}]},{"id":"LrOMESy20X_NYizAh1Rfm","type":"h2","children":[{"text":"Creating Your Own Template","id":"U8ZnBRTL846UG7P76grnT"}]},{"id":"n660Dp-I2FlvXbviFe5Bc","type":"paragraph","children":[{"text":"You can create a new template from scratch, or you can edit an existing template.  For this gude we will edit the NVIDIA CUDA template we used in the Quick Start section. ","id":"etCH2F56XVHp3JFYoU2Wx"}]},{"id":"qSImBmst_ADqdXJ3sDZKE","type":"paragraph","children":[{"text":"From the templates page, click the pencil icon on the template card to open up the template editor.  You'll see two tabs ","id":"jwKWt6mBjb83Ck3lEw_MV"},{"text":"Config","highlight":true,"id":"t8mFwl0GT3Hx718tHKIMF"},{"text":" and ","id":"rJKa3WKPr0cNsg_R50Eu1"},{"text":"ReadMe","highlight":true,"id":"c0jcfRPS0PFZMKUaUkYzJ"},{"text":".  We'll start with the config tab:","id":"hLoNQah_H-eWV1tLRzKBC"}]},{"id":"VPKyq6sy57PpHTBrUBmOS","type":"h3","children":[{"text":"Identification","id":"2VUIsBYh3Eh6oo7hPAgbU"}]},{"id":"09NHLpVkZPi4uF4LQ51yB","type":"paragraph","children":[{"text":"The first section helps you to keep your templates organized.","id":"Z44iGZG-OG_LGLgJOxmlk"}]},{"id":"vdY6FA2BNflMClqTLl1nm","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/RbISQHd_ERbF8HMIqams3_image.png","signedSrc":"","size":100,"width":936,"height":294,"position":"center","caption":"Template editor: Identification","alt":"Identification section of the template editor"},"children":[{"text":"","id":"mTm9hJWmTXno7tUTzgJ40"}]},{"id":"jJDybsR7HrwiUuEC-7gqU","type":"paragraph","children":[{"text":"Template Name","bold":true,"id":"qenG44E15wB4BX06dopnG"}]},{"id":"eXPudaI6sBFDqXOqAEshZ","type":"paragraph","children":[{"text":"This will be displayed in bold on the template card.  Choose something that helps you identify the template amongst your other templates.  For this guide, we will change the name to 'NVIDIA CUDA - Demo'","id":"-J2Z500mVtZxDNbgP8Oxf"}]},{"id":"YLp7gbSGPUOqL2k0tlGb0","type":"paragraph","children":[{"text":"Template Description","bold":true,"id":"khA9WW7y5WTHYGOJh8yUR"}]},{"id":"rDQ-G82YNvJx1IwxRtulG","type":"paragraph","children":[{"text":"This field helps describe the function and purpose of the template. Completely optional for your own purposes, but very helpful if you intend to make this template public or share it with others.","id":"mzWXZIM3GqKQLp-sQFDIZ"}]},{"id":"twT2AbtFAzFqpJ_BtcNNr","type":"h3","children":[{"text":"Docker Repository And Environment","id":"LwHqCBCBMrXrAe00eSZIf"}]},{"id":"MAwEaaeEuYGec9WjbZ9gg","type":"paragraph","children":[{"text":"This is where you define the Docker image you want to run, along with any options we want to pass into the container.","id":"5WILIVPTb84nhKMziXXHg"}]},{"id":"nc4RoAsqNwzMEU7zsCVDP","type":"paragraph","children":[{"text":"","id":"7PvgOX1fZ9JQp1M8dsaWA"}]},{"id":"Lj8xhkEDYLTZluL4LcpMZ","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/49uAhXy-7NFBtLtPwNhWh_image.png","signedSrc":"","size":100,"width":844,"height":775,"position":"center","caption":"Template editor: Docker","alt":"Docker section of the template editor"},"children":[{"text":"","id":"631-wK5OkwWYzigwjYTOm"}]},{"id":"ekyqVR_OURVyMEwE-M4bf","type":"paragraph","children":[{"text":"Image Path:Tag","bold":true,"id":"iUb2qWPdEnroOxJOQE_Yr"}]},{"id":"NFn5VJy2EAMk3cYHO_GPR","type":"paragraph","children":[{"text":"Here is where you can define the docker image to run.  This field must be in the format ","id":"a5URn4Z5K91CRDhKdAB-q"},{"text":"repository/image_name:tag","highlight":true,"id":"HScubrcYwmw0jauf7q29k"},{"text":".","id":"Er8kJPTTobwXpItLTskY5"}]},{"id":"P9KGqfbBlYsF6gJhM_xo6","type":"paragraph","children":[{"text":"Many of our templates pull from DockerHub but you can use any container registry - Just remember to add the full path if you're using an alternative registry. Eg. ","id":"avyGG3OmEVrc6bXXtXsll"},{"text":"nvcr.io/nvidia/pytorch:25.04-py3","highlight":true,"id":"MyeYdgIseHvshhZ6MiCan"}]},{"id":"ge3MQwNTffCly04Ckdf3A","type":"paragraph","children":[{"text":"Version Tag","bold":true,"id":"Fbc2Kc2uG0ctohjW4c4ov"}]},{"id":"86L0wRbwN3vUJDJIZzNqc","type":"paragraph","children":[{"text":"For many registries we are able to pull the available list of tags so this field allows you to quickly select another version.","id":"bzXaJ64bmWEzFq7hfyFdN"}]},{"id":"Erv6FM7CDdw0-gIByqSQY","type":"paragraph","children":[{"text":"There is also a special ","id":"C5AzM2nTmLwacGTY7JUFr"},{"text":"[Automatic]","highlight":true,"id":"a4dUoHAjhZCJZv6GrT9TM"},{"text":" tag you can use.  With this selected, the machine you choose for your instance will pull the most recent docker image that is compatible with that machine's own CUDA version. ","id":"txsQdP-QtsdEtMBp4xt08"}]},{"id":"VrhJUxV9uUKGpDMMFz0wS","type":"paragraph","children":[{"text":"This will only work if the image tag contains the CUDA version string. For example: ","id":"wrpU3VjV375j_Y7BoSS64"},{"text":"my-image-cuda-12.8","highlight":true,"id":"on4SWqrxGI-FnRtO35h_6"},{"text":" would be loaded on a machine supporting CUDA 12.8, but a machine with only CUDA 12.6 would pull ","id":"Kfy8PcSvdrBd_52IxBDXT"},{"text":"my-image-cuda-12.6","highlight":true,"id":"M12QbJ4dczKmE19XnFGjU"}]},{"id":"8Y9b2Qkjun3njoErnQ2AZ","type":"paragraph","children":[{"text":"Docker Options","bold":true,"id":"FvVaUZv4gDTJzUuwx-C1i"}]},{"id":"-KjxqrkYSj-ulMKqnjDf3","type":"paragraph","children":[{"text":"This field is a textual representation of the ports and environment variables declared in the sections beneath it.  You can edit it directly or you can use the page widgets.","id":"-SIy-7yZREl8H4gQewuf4"}]},{"id":"52edynKNaXc3iU6SbzfzT","type":"callout-v2","data":{"type":"info"},"children":[{"id":"6DA8QzAkugZw2TEJA8lRn","type":"paragraph","children":[{"text":"This field will only accept ports and environment variables.  Other docker run options will be ignored.","id":"l1DNr3IfkjrljJ_6iPTGy"}]}]},{"id":"FiIJYKCbj5tD7L_x75AR5","type":"paragraph","children":[{"text":"Ports","bold":true,"id":"VrVxL2D5sg7PkXyCXcF82"}]},{"id":"lEZ2bvO6LPTIREQl0d0Qr","type":"paragraph","children":[{"text":"To access your instance via the external IP address, you will need to add some ports to the template.  You can add both TCP and UDP ports.","id":"8ZVQe_WiHoGkowhvzZQaq"}]},{"id":"SmTaPb9vrkg3nYw_zv8pI","type":"paragraph","children":[{"text":"When your instance is created, a port will be randomly assigned to the external interface which will map into the instance port you selected.","id":"bwbu0EwuYVjiMMo0LIv21"}]},{"id":"SUHghp5jfXfXHY69ewpo-","type":"paragraph","children":[{"text":"Environment Variables","bold":true,"id":"HYBrwUEHQh0M5TvtCKiAE"}]},{"id":"Up7jjZkRHgBuB0EPFvr3P","type":"paragraph","children":[{"text":"Here you can add any environment variables that your docker image requires.  Do not save any sensitive information here if you are planning to make the template public.","id":"YFAMGFtJnL280AkPq8-vH"}]},{"id":"mwpi7TywIvQ4uP9yR0Q4t","type":"paragraph","children":[{"text":"Place any variables with sensitive values into the Environment Variables section of your ","id":"N7iW8Pb0MsG3dXmfxFp9S"},{"id":"TijQaWifPuMIRe4TxykH6","type":"link","data":{"href":"https://cloud.vast.ai/account/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"account settings page.","id":"t_-bhlpTB1xtMPegr_VSq"}]},{"text":"  They will then be made available in any instance you create, regardless of the template used.","id":"2sE3mhGiiarceGCDj-Vt1"}]},{"id":"cK2BTgFfpwGFNiL4odpl8","type":"paragraph","children":[{"text":"You can find out more about port mapping and special environment variables in our ","id":"3mrXoIvMwna__MpJZz3lk"},{"id":"31ir_NOd9XYo3ZTTajFBX","type":"link","data":{"href":"https://docs.vast.ai/instances/docker-execution-environment","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Docker Execution Environment","id":"Xi9YAKrXG5nl6cRTA7r4a"}]},{"text":" guide.","id":"LdTWUaDmP4NhAhfYqmTIj"}]},{"id":"K5vNzpdpAeNQh0P-kiyVp","type":"h3","children":[{"text":"Select Launch Mode","bold":true,"id":"VP9flNToysN1IglejzHad"}]},{"id":"r6iIlUmzm02Sevys_6xpr","type":"paragraph","children":[{"text":"Templates offer three launch modes you can select from.  Our recommened templates will usually launch in Jupyter mode for easiest access, but you are free to choose whichever suits your needs.","id":"et_2X62JlAs-7BaA5r0Cy"}]},{"id":"oWrp4ZM5aVIga1iQycJAD","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/E8Jc-IjeiPLPwftGRAej3_image.png","signedSrc":"","size":100,"width":936,"height":191,"position":"center","caption":"Launch mode options","alt":"Launch mode selection options"},"children":[{"text":"","id":"0m5LbxYFlSogvUjxLB2Fd"}]},{"id":"xRz45rKL4CnywMkr0GzwJ","type":"paragraph","children":[{"text":"Jupyter-python notebook + SSH","bold":true,"id":"BBuZmqyFTrAvTW2nBeIZN"}]},{"id":"DF8UMgQE4m-lQJFGXbcIz","type":"paragraph","children":[{"text":"When you run the template in this mode, we will install Jupyter and SSH at runtime.  Jupyter will be available on mapped port ","id":"XBUI9vLDs8Kwaw9CQxoFc"},{"text":"8080","highlight":true,"id":"SjW8DOtPMlY3mwuqM5JRx"},{"text":" and SSH will be available on mapped port ","id":"aMWSo6oqChY27SiB_fGeF"},{"text":"22","highlight":true,"id":"6vz5Rn-vHsRl2tktZ-Ij_"},{"text":".","id":"GQSz6ytxk9HihXegYct07"}]},{"id":"bwrPapd4mBN9kHIg94-pR","type":"paragraph","children":[{"text":"Interactive shell server, SSH","bold":true,"id":"RhJLX4Kp5GQx0GVXPmKGm"}]},{"id":"A2zx1Yqmfgdwzks8Ocgs5","type":"paragraph","children":[{"text":"As above, but SSH only with no Jupyter installation.","id":"Zi0UC7n4_4qihf-Nm5_UO"}]},{"id":"pR-tBpqDLEfv_LxRyc7Ls","type":"callout-v2","data":{"type":"warning"},"children":[{"id":"F5amr17dBwUf-0UM_Lymp","type":"paragraph","children":[{"text":"In both Jupyter and SSH mode, the docker entrypoint for your image will not be run.  It will be replaced with our instance setup script so you should use the on start section (documented below) to start any services.","id":"E2sMc1iZgyuZELtbGYrt8"}]}]},{"id":"OnXEVvVs4KK-6X3no_fP_","type":"paragraph","children":[{"text":"docker ENTRYPOINT","bold":true,"id":"Rqete39egS_z4BYVx-gfX"}]},{"id":"c7nKjfZhqNPcAsO_HoZ4P","type":"paragraph","children":[{"text":"In this mode, your Docker image will run precisely as it is. We will not include any additional software or access methods. If your Docker image does not offer SSH or another appropriate interface, please select one of the alternative modes if you need to interact with the running instance.\nAn additional field will be showin when using this launch mode to allow passing arguments to the image entrypoint.","id":"rL4k6Dwf5DJBvMy8inFj7"}]},{"id":"nH5Cmte7KnoUiKkpy_nTR","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/6MSmdumOj0wHY-pr2z2YN_image.png","signedSrc":"","size":100,"width":936,"height":63,"position":"center","caption":"Entrypoint arguments","alt":"Field allowing for argument passing"},"children":[{"text":"","id":"70Y0RFrMW57z9MKHEuYnu"}]},{"id":"4A154gsbNDQi-HQhBhMS_","type":"h3","children":[{"text":"On-start Script","id":"LBXyNrWP2b5f4ZFHDpitw"}]},{"id":"Ia-0x5O-MnGCy7_MRmgBG","type":"paragraph","children":[{"text":"Here you can enter a short Bash script which will be run during instance startup.  It is only available when using the Jupyter or SSH launch modes, and is most useful for starting any services that your docker image would have launched if run if the entrypoint had been executed.","id":"kwGnD3Iexhybdg0Aue7FA"}]},{"id":"KMln09XVtt6vXu79CH-OS","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vXS-1QECGWMiUqreqp5JB_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vXS-1QECGWMiUqreqp5JB_image.png","size":100,"caption":"","isUploading":false,"width":936,"height":177},"children":[{"text":"","id":"alhBtXNbP7I66yYttPZ-c"}]},{"id":"uYvTlajW7e64SGXUSMzew","type":"paragraph","children":[{"text":"In our example 'NVIDIA CUDA' template we are simply calling the ","id":"fgqKtFkQDgaKxJVw03JX5"},{"text":"entrypoint.sh","highlight":true,"id":"xvqHukBerr9LPFJO6F_7q"},{"text":" script which contains the logic for preparing the instance.","id":"BC2KoIv-W1WStlJwyz1yW"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vXS-1QECGWMiUqreqp5JB_image.png","signedSrc":"","size":100,"width":936,"height":177,"position":"center","caption":"","alt":"On start field with entrypoint.sh "}},{"id":"pi25h_BHLDt67esXuDH_S","type":"h3","children":[{"text":"Extra Filters","id":"nI2SvJvucHnbwUkG8EfDN"}]},{"id":"LWnvV7BO_e4yBfnFAlirx","type":"paragraph","children":[{"text":"Use this area to place restrictions on the machines that should show up in the search page when the template is selected.","id":"8Wwyyxz57ggN55nuQsoOI"}]},{"id":"HBEO_EvON9nkIKGj2wpjz","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/nuuwsNgDalF8Xo3XKompc_image.png","signedSrc":"","size":100,"width":936,"height":142,"position":"center","caption":"Extra Filters","alt":"Extra filters showing this template is configured for both AMD64 and ARM64 CPUs"},"children":[{"text":"","id":"lf-cUp1_kDJHcF69L9sAn"}]},{"id":"smLU9-p50FTwX9f-rLHmt","type":"h3","children":[{"text":"Docker Repository Authentication","bold":true,"id":"EU9q7y9odw0Md1Fwan1jx"}]},{"id":"XvwYR2HtuJw9AxOeuYamC","type":"paragraph","children":[{"text":"If you are using a private Docker image then you will need to add authentication credentials so the machine running the instance can download it.","id":"f0aEB0d5qsahGZUV_YvbB"}]},{"id":"b4SvfoOQ1raJs5Ywd7GN8","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/fc7faHt8TaQaDc0LSF6fr_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/fc7faHt8TaQaDc0LSF6fr_image.png","size":100,"caption":"","isUploading":false,"width":945,"height":152},"children":[{"text":"","id":"n5IFJgaoQC-Ri86vYVAWd"}]},{"id":"ppN-FSxJcd0g8vPBG6kFf","type":"h3","children":[{"text":"Disk Space","id":"GodzhjHfAYkcf8Vhiu0a9"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/fc7faHt8TaQaDc0LSF6fr_image.png","signedSrc":"","size":100,"width":945,"height":152,"position":"center","caption":"","alt":"Shows where to enter server, username and token"}},{"id":"a2hjtjnk-NCX1Ww2K6KBq","type":"paragraph","children":[{"text":"By setting the disk space in the template, you can ensure that new instances created from the template with use this amount as a minimum. ","id":"98aNG08RRR50N9Yv2p5eO"}]},{"id":"Wm_I0uo41pwqzSoZBQjGW","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/xAnaHLXIXdm9TmLcKhPej_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/xAnaHLXIXdm9TmLcKhPej_image.png","size":100,"caption":"","isUploading":false,"width":945,"height":129},"children":[{"text":"","id":"w9tZfne9-El3_vSswNRkR"}]},{"id":"CLYMPVNBjO9R9NgqEZ-Jr","type":"h3","children":[{"text":"Template Visibility","id":"em8eMqINk6cgk-wf4Kr5p"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/xAnaHLXIXdm9TmLcKhPej_image.png","signedSrc":"","size":100,"width":945,"height":129,"position":"center","caption":"","alt":"Default disk space"}},{"id":"Z_V03iBGgQuoqw7z7JYC-","type":"paragraph","children":[{"text":"Any template marked as public will be available in the template search system, while private images will not.","id":"laojAzlM61Hs5MOWZrl7I"}]},{"id":"g8qmQDzjac0jDufLXYe4l","type":"paragraph","children":[{"text":"Private templates can still be used by others if you have shared the template URL.","id":"GlVFv2tWrl65sZFujB7wO"}]},{"id":"BKvIhXFkIQBeY9u5HhgDM","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/rRnwi3JuUxjPynXIrGwJm_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/rRnwi3JuUxjPynXIrGwJm_image.png","size":100,"caption":"","isUploading":false,"width":945,"height":65},"children":[{"text":"","id":"UTcj8g1jm17HAykK4MSbP"}]},{"id":"1cAH2s46hpST_lAOK7nhl","type":"callout-v2","data":{"type":"danger"},"children":[{"id":"u3l-3peOTZCsGkAKhwSFb","type":"paragraph","children":[{"text":"Never save a template as public if it contains sensitive information or secrets.  Use the account level environmnet variables as an alternative.","id":"Rw86tua-ZQ5sifr3q9C-K"}]}]},{"id":"6hveS2XKGt_kD0owwtf6u","type":"h3","children":[{"text":"CLI Command","id":"3KRwvS8oStdEvora2xBeB"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/rRnwi3JuUxjPynXIrGwJm_image.png","signedSrc":"","size":100,"width":945,"height":65,"position":"center","caption":"","alt":"Template visibility selector"}},{"id":"zbM2wnqoSX-8FGHNp2TQi","type":"paragraph","children":[{"text":"Templates can be translated directly into CLI launch commands.  This read-only area shows what you would need to type or copy to the CLI if you wanted to programatically launch an instance this way.","id":"qDgrb7nyP04gFlDS7S-Fn"}]},{"id":"GylGWB5b0j7AcnnwY3qMs","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/TQPTz9I5em8-jfnw_DSi0_image.png","signedSrc":"","size":100,"width":945,"height":183,"position":"center","caption":"CLI Command","alt":"Launch a template via the CLI"},"children":[{"text":"","id":"tIdfu_jbBBastjUatUh8s"}]},{"id":"AiU2cHfHX62Hi0BQGu_6D","type":"paragraph","children":[{"text":"To learn more about starting instance from the CLI, check out our ","id":"alxq5im95SN5CQkMqjWoi"},{"id":"1hz3xGcYkYSyuv_rXTPTP","type":"link","data":{"href":"https://docs.vast.ai/api/overview-and-quickstart","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"quickstart guide","id":"evFEXhWRBmStfWBroaLqW"}]},{"text":".","id":"ZtgcovaWPD4H1BjHzWURL"}]},{"id":"7F512JA-m-uUivslpvKqy","type":"h3","children":[{"text":"Save the Template","id":"SzR-M1k2LW-Dq078TmWBU"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/TQPTz9I5em8-jfnw_DSi0_image.png","signedSrc":"","size":100,"width":945,"height":183,"position":"center","caption":"","alt":"CLI Command section showing how to launch the NVIDIA CUDA template"}},{"id":"kQWjLBtt-rYsc3kDV_TwX","type":"paragraph","children":[{"text":"Finally, you can save the template.  If you are creating a new template or editing one which is not associated with your account - Such as one of our recommended templates - The buttons you see will be labelled 'Create'.  For your own templates, you will see them labelled 'Save'","id":"g1sYu3upqyA5NS1FPyNYI"}]},{"id":"HHXpE6MYMZqPaJ0YuBi0v","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/pheY6imcsh5__O_-Thmyq_image.png","signedSrc":"","size":100,"width":323,"height":56,"position":"center","caption":"Save Buttons","alt":"Buttons for saving"},"children":[{"text":"","id":"DmZHj2Pc-Sdkx23Qda7rm"}]},{"id":"SkvDWQv7m3ql2qga6Ed4X","type":"paragraph","children":[{"text":"The 'Create' button will create a copy of the templaye in the 'My Templates' section of the ","id":"4qcUsAX-X8zUcZ6v-TfF_"},{"id":"2xTg3VahcVaQYkNNNw4q8","type":"link","data":{"href":"https://cloud.vast.ai/templates/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"templates page","id":"TJNPaN0yfel8FO3fqieFF"}]},{"text":" for you to use later  The 'Create \u0026 Use' button will save the template, load it and then open up the ","id":"Exol1RGcnud5ojthQSqf-"},{"id":"F69_KRzYS4E1RU27tdYzf","type":"link","data":{"href":"https://cloud.vast.ai/create/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"offers page.","id":"zDBHw_g0i1Dd2598ynZx5"}]},{"text":"","id":"_XC0F9OXC2bxPdzZ24B7R"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/pheY6imcsh5__O_-Thmyq_image.png","signedSrc":"","size":40,"width":323,"height":56,"position":"center","caption":"","alt":"Buttons for saving the template"}},{"id":"SPKENORDJs9vLjGvpCJ_o","type":"h2","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/pheY6imcsh5__O_-Thmyq_image.png","signedSrc":"","size":40,"width":323,"height":56,"position":"center","caption":"","alt":"Buttons for saving the template"},"children":[{"text":"Updating a Template","id":"7vxA9Qxe7ga06ihTegPgj"}]},{"id":"k5X_s6b6Ci6COft1t5vNI","type":"paragraph","children":[{"text":"If you want to make changes to a template you previously saved, simply navigate back to the templates page and select 'My Templates'.  Here you'll be able to make your changes by clicking the pencil icon.","id":"dxz-uTlzvB_g5gNjvNnZp"}]},{"id":"GdVbbTAXJbsw40QaGeJuj","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/JL86zURfZuCur4mvOPXQG_image.png","signedSrc":"","size":100,"width":596,"height":491,"position":"center","caption":"My Templates","alt":"My templates showing the NVIDIA CUDA - Demo template"},"children":[{"text":"","id":"CPiLsAYjisJAzMQ6qxkTC"}]},{"id":"5H6akaJ-JgkLevlZrAJx3","type":"h2","children":[{"text":"Sharing a Template","id":"unkfyR8_8iXrLTKxDF-PR"}]},{"id":"D3p7Z0aJOrwL5k1ZLPUxL","type":"paragraph","children":[{"text":"It's really easy to share your template with other users.  We have two special links you can use and both include your referral code so you can earn if new users sign up - Find more about that ","id":"lm1zs-BhQkkNsIL7EPvRR"},{"id":"IxJNS3194sZqsumUCHK2c","type":"link","data":{"href":"https://docs.vast.ai/referral-program","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"here.","id":"jB7iLgxAO1IR8ppm26jPc"}]},{"text":" ","id":"paAEy3THyPyznG_sINU5i"}]},{"id":"aRHP6ejWtJEd-JNdMZron","type":"paragraph","children":[{"text":"To share, click the three dots icon in the bottom right of the template card.","id":"zT2ivHgG0jqjZGSDVlcqQ"}]},{"id":"RitH9Np9j59dCNcrb9l_H","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vr0DPCqmQbaIhNfEspUwx_image.png","signedSrc":"","size":100,"width":462,"height":197,"position":"center","caption":"Template Sharing Menu","alt":"Menu shows sharing options"},"children":[{"text":"","id":"64vdAD8sLSXT7nYRp-tAI"}]},{"id":"hXqUcWxakbII1masdPe6A","type":"h3","children":[{"text":"Copy referral link","id":"E-wSCi9uji7ucXfmmH4Wv"}]},{"id":"uSTPQrhHSCmhPuhMuxjsK","type":"paragraph","children":[{"text":"This will copy a link that contains your referral ID, creator ID and the template name.  It will always point to the most recent template you created with this name - Really useful if you want people clicking the link to always get the most recent version.","id":"BGo5DOqJ45AitrWO4c7TB"}]},{"id":"xO7L8AixVbF7aHvWSlYQF","type":"h3","children":[{"text":"Copy template link ","id":"HeLerQh9BxByzfDHDEOB1"}]},{"id":"YtqSjnBHrSx-Y7F71Y0e0","type":"paragraph","children":[{"text":"This will copy a link containing your referral ID and the template hash ID.  It points to this specific template at this point in time.  ","id":"HR9uydJryIjKniNZeMPgH"}]},{"id":"VpBaPDlC1Qs6mBqpYFVaj","type":"paragraph","children":[{"text":"Templates all have a unique hash after every save.  This is useful as it allows you to find a previous version if you have tracked the hash ID, but for sharing you probably want the referral link above.","id":"Ne3tYbXpeXCDEuD6oYlHl"}]},{"id":"IrvxhTyFkZMmdJc0v2vn5","type":"callout-v2","data":{"type":"info"},"children":[{"id":"G83vt170KwoCSLmRiBwnj","type":"paragraph","children":[{"text":"Remember to add a comprehensive Readme to your template if you're going to share it.  This will help users to get started easily.","id":"YVMGAXX7SMHt12FkUAtn8"}]}]},{"id":"oh7MSjyZvKSf30zQqhv2w","type":"h2","children":[{"text":"Vast Templates","id":"7qq4sW-X1hCWxQTOmajOn"}]},{"id":"jUAjzPCKxNbvK3zR4U5Gh","type":"paragraph","children":[{"text":"Templates that we have created will often be found in the ","id":"XnICzICIDnCUUYXdlQv6v"},{"text":"Recommended ","bold":true,"id":"85rVpy9k7WOHh9WWhCtUG"},{"text":"section as they go through a QA process to ensure they function correctly and provide a positive user experience.  They contain extras not found in all templates so we will address the differences here.","id":"1rQcHP_Ww3UCO4h7A0QBF"}]},{"id":"xAv9O81a2bSll2uqcK5pH","type":"h3","children":[{"text":"Instance Portal","id":"4gRd4uqQd2Cxi4QvLxArx"}]},{"id":"SwiZJXFAwC_V6XI4nXW0u","type":"paragraph","children":[{"text":"You will notice that after clicking the Open button on template running one of the Vast.ai templates a screen like this will appear.","id":"zKyIh-GM2QtPFreOSeo7F"}]},{"id":"X8FC0xge265jb4QfSyuMk","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/YCTX5r91x9CQYhtg34O_S_image.png","signedSrc":"","size":100,"width":1896,"height":754,"position":"center","caption":"Instance Portal","alt":"Instance portal landing page "},"children":[{"text":"","id":"aZevOkuQ5Xa-wXZE6XQDi"}]},{"id":"KyprJkmfX4Mlcj3B2FqaM","type":"paragraph","children":[{"text":"This is our ","id":"nCvVQ9wEaop0ewwJqCOk7"},{"text":"Instance Portal","bold":true,"id":"eOfcUSMKlPZCTm7p6ZdqA"},{"text":" and it provides easy to access links to services running in your instance.  It places an authentication layer in front of these services to prevent access by anyone who does not have the correct authentication token.  You are also able to create tunnels to your services without exposing ports.","id":"5avR12fxJ31D8VuysOVuZ"}]},{"id":"GaL3sypUpeLUBoTUZy_qD","type":"paragraph","children":[{"text":"Full documentation for the ","id":"5t1mAQ5d9mPATLE-nGHjg"},{"text":"Instance Portal","bold":true,"id":"iwbnQI33jr8SU39iuRe07"},{"text":" is available ","id":"z5G0X1GBYVbgg-lxoSCbt"},{"id":"zOVDbnimOtQKyE2dGdkq5","type":"link","data":{"href":"https://docs.vast.ai/instance-portal","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"here","id":"nVGypaTL5YCf-P4pzZs_1"}]},{"text":".","id":"6JRgkBD6dkdiYD7IenpkV"}]},{"id":"aaspgvIqXn9OaMJ1W9Agn","type":"h3","children":[{"text":"Provisioning Script","id":"ZZLCK7O8OMRUe7JYcuX8W"}]},{"id":"lGn8NFfUdOY4n7j__Pktt","type":"paragraph","children":[{"text":"Vast.ai templates support running a remote script on start to help configure the instance and download models and extensions that may not already be available in the Docker image.","id":"QOgaWmxYZMVY3OahsqOqU"}]},{"id":"q0NvGaxqI33R5TJzSAunq","type":"paragraph","children":[{"text":"To use this feature, simply add environment variable ","id":"qi6A94y6FY19l1HMSSvKR"},{"text":"PROVISIONING_SCRIPT","highlight":true,"id":"XChVhdhz1ajrkMWqnxWRP"},{"text":" and set the value to a plain text shell script URL.  The file will be downloaded and run on first start of the instance.","id":"vH5ecCPXPfN2oOuMTy0vL"}]},{"id":"1pK3_qiNx45aSHCnN-Uzw","type":"callout-v2","data":{"type":"info"},"children":[{"id":"86AGA-xg81FfvioqO5wH5","type":"paragraph","children":[{"text":"Use the Provisioning Script to add a twist to an existing recommended template by downloading custom models and extensions.","id":"TkIhniMg2-EdVMXatVmlZ"}]}]},{"id":"YuKTIA8cJDHlnt5Nt9DaE","type":"h3","children":[{"text":"Base Image","id":"Jjo3JgLKrf-LJYJW9JVSy"}]},{"id":"4OG1Y6hs38jLOI0LnRCrO","type":"paragraph","children":[{"text":"You can find the source code for the Docker images we used to create the Vast templates on ","id":"StziAd4uD6BH1pL8ELF80"},{"id":"NkJNRzbVE9t5Jxfs_83vL","type":"link","data":{"href":"https://github.com/vast-ai/base-image/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"GitHub","highlight":true,"id":"hfLCPErZXCpsqHlsY12GW"}]},{"text":". ","id":"isB7m0G_ASXrcOXevSXEr"}]},{"id":"_ToN6B0xMddlm25VuDu2W","type":"paragraph","children":[{"text":"These are large Docker images that contain CUDA development libraries, node + npm, OpenCL and other useful libraries.  Despite their large size you'll find they generally start quickly because they have been cached on many of the host machines.  This makes them ideal for using as a base for your own images.","id":"NWHJEx0Qs5GwOyQ0QOTEw"}]},{"id":"4p3XMb8da7rgDvemCAekq","type":"h3","children":[{"text":"Virtual Machine Templates","id":"aEsXTzXh08zDy0m3X5ran"}]},{"id":"xQVz8XWoSU1S_s5_YHQXb","type":"paragraph","children":[{"text":"Currently we offer two VM templates; A CLI and a desktop GUI.  These differ from a standard template because they launch a full virtual machine environment rather than a docker container.","id":"RbbW-cqQBZHfQqSY-wwci"}]},{"id":"eoKb3-42jpqGpxUlDEnSJ","type":"paragraph","children":[{"text":"You can edit these templates as described above, but you should not change the docker image field.  Only the images we distribue from ","id":"dTRf_TuGcV0yFYI1tfN2c"},{"text":"docker.io/vastai/kvm","highlight":true,"id":"7i8ypMHXrkO-nroOhWSvT"},{"text":" will work, but feel free to add extra environment variables and ports.","id":"CLeb2mqEsARKxjepoll6e"}]},{"id":"LLxWKHG5sm1jXNc5waDCf","type":"paragraph","children":[{"text":"Use the VM templates when you need to:","id":"aUEqBFRTe66jXwBZeYNy3"}]},{"id":"Vzo0PSSayhyIFtcZqNX24","type":"bulleted-list","children":[{"id":"jd6BogoEfDeh_CbO-Nf7u","type":"list-item","children":[{"id":"-cNrAaLfttadTXnI3AyN_","type":"list-item-child","children":[{"text":"Run applications that require namespace support","id":"CILFEwHjt-MGjsBGaTQMj"}]}]},{"id":"VsmaPIm6iI13Uut7B03xH","type":"list-item","children":[{"id":"Kb-B6XHoyX9sqgwyDg5Td","type":"list-item-child","data":null,"children":[{"text":"Run more than one Docker container in an instance","id":"7XLtgNFxT9A4-nWdySQqq"}]}]},{"id":"28ldLVs2bJG-35KKA_IPI","type":"list-item","children":[{"id":"BcyC33UkS16lhgvRB0QkN","type":"list-item-child","data":null,"children":[{"text":"Load kernel modules or run profiling jobs","id":"o9gku5tQaATDuG9MBkoJD"}]}]},{"id":"svckYZbCTWk8EZyCQqYBH","type":"list-item","children":[{"id":"WoF1Us69QEwc3JBaXvSzy","type":"list-item-child","data":null,"children":[{"text":"Mount remote drives with rclone or similar","id":"CdQFQ9tGbZnw1uUQUB-d2"}]}]}]},{"id":"yIKR4tucZ1Ms-IgvebOI8","type":"h2","children":[{"text":"Advanced Configuration","id":"UA-zzDYCu0mL6M8Qex1Eh"}]},{"id":"YItCvsv8pnl8eSu4ywxF2","type":"paragraph","children":[{"text":"For advanced template configuration options and variables, please see the ","id":"FrcXTPOFHWa5qXO_kS2-t"},{"id":"UkivnttvBrZ7VjDGvLKUy","type":"link","data":{"href":"https://docs.vast.ai/instances/docker-execution-environment","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Docker Execution Environment ","id":"431RIC2k1VIt-2m4CJZVX"}]},{"text":"guide.","id":"7vKzl9kkNYJi_AUgR8HVE"}]},{"id":"VQNNBId61YO-p4ehZdmnK","type":"paragraph","children":[{"text":"","id":"NZaJFVuqsJHYZ0rSoM79T"}]}],"metadata":{"type":"doc","version":"v2","convertedDate":"2025-05-12T11:10:10.570Z"}},"version":2409,"privacy":"shared with team","shareableToken":"x4ixozdP9OvGkTJ8byZv3","tags":[],"docTags":[],"children":[],"hasDraft":false,"createdByUserId":"zS_1kv8ITuDzgH4-KTdjJ","createdBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"lastModifiedByUserId":"WL2az7o_8HmPbkHvSjU-Q","lastModifiedBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"contributorsDetails":[],"watchers":[],"isArchbeeBrandVisible":false,"customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","updatedAt":"2025-06-13T17:13:44.783Z","createdAt":"2025-05-12T11:10:10.578Z","deletedAt":null,"editable":false,"expanded":true,"reusableContentVariables":[{"contentVariableId":"q3exSma0JEJZI5e1dH_V6","name":"Worker_Groups","content":"Worker Groups","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"UP_Tl2jcgO5gOIMbrO-GS","name":"Endpoints","content":"Endpoints","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"5aHM10OFigKWTwHuB3fMP","name":"PyWorker","content":"PyWorker","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The Vast PyWorker is a Python web server designed to run alongside a machine learning model instance, providing autoscaler compatibility."},{"contentVariableId":"4t0syUbNAbAxpMhfF1SS9","name":"Worker_Group","content":"Worker Group","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"07Mp-kz9OjvJv1gj7w4H2","name":"Endpoint","content":"Endpoint","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"sP383XCx12brqPeq_8qVA","name":"min_cold_workers","content":"min_cold_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The minimum number of workers you want to keep \"cold\" (meaning stopped and fully loaded) when your group has no load."},{"contentVariableId":"ud8V8Q4s-JoB5vW8wVEJS","name":"max_workers","content":"max_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The maximum number of workers your router group can have."}],"reusableContentDocRefs":{},"externalSync":"","contentFromExternalLink":"","leftDoc":{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","isCategory":false,"categoryName":"4-cli","children":[],"expanded":false,"name":"CLI","urlKey":"cli","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},"rightDoc":{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}},"_docSpace":{"id":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","name":"Guides","organizationName":"Vast.ai 2","icon":"","type":"team","publicDocsTree":[{"id":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","isCategory":true,"categoryName":"Overview","gitHubPath":"docs/overview","children":[{"id":"PUBLISHED-704s_lXkTRgEFkRLGeF1p","isCategory":false,"categoryName":"1-introduction","children":[],"expanded":false,"name":"Introduction","urlKey":"","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-aKvlCTuCrf4gl2NR7hO2W","isCategory":false,"categoryName":"2-quickstart","children":[],"expanded":false,"name":"QuickStart","urlKey":"quickstart","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","children":[{"id":"PUBLISHED-_biJv6YaRTqcfsFCsFS50","children":[],"expanded":false,"name":"Instances Help","urlKey":"instances-help","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-PlnZzvcNBmO9p7BEBwDFS","children":[],"expanded":false,"name":"Billing Help","urlKey":"billing-help","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-sVUb5JPCzlvMnKDfU-2Op","children":[],"expanded":false,"name":"Networking","urlKey":"networking","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_0-0A7rQNefJSou1KR6CK","children":[],"expanded":false,"name":"Troubleshooting","urlKey":"troubleshooting","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-VfosYMdo2IumeiqSJdd2g","children":[],"expanded":false,"name":"Data Movement","urlKey":"data-movement","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]}],"name":"FAQ","expanded":false,"urlKey":"faq","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]}],"name":"overview","expanded":true,"urlKey":"N2so-overview","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-oocGb1edF0t5aErXyJR2f","isCategory":true,"categoryName":"Use Cases","gitHubPath":"docs/use-cases","children":[{"id":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","children":[{"id":"PUBLISHED-aNV7igw-IqgsWPPhf73et","children":[],"expanded":false,"name":"Creating Templates for GROBID","urlKey":"creating-templates-for-grobid","icon":"","parentDocId":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","conditionalRuleId":"","docTags":[]}],"name":"Creating a Custom Template","expanded":false,"urlKey":"creating-a-custom-template","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-8OgArwW8g6Y_17wGT-SNf","children":[{"id":"PUBLISHED-k-Bha16hWnYinwkb4lD2O","children":[],"expanded":false,"name":"PyTorch","urlKey":"pytorch","icon":"","parentDocId":"PUBLISHED-8OgArwW8g6Y_17wGT-SNf","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI/ML Frameworks","name":"AI/ML Frameworks","expanded":false,"urlKey":"aiml-frameworks","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-FGY05fV4_DdhtOukqQRu7","children":[{"id":"PUBLISHED-jTdh63niYUhgnZqlLJN9y","children":[],"expanded":false,"name":"CUDA","urlKey":"cuda","icon":"","parentDocId":"PUBLISHED-FGY05fV4_DdhtOukqQRu7","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"GPU Programming","name":"GPU Programming","expanded":false,"urlKey":"gpu-programming","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-4TMoTzzxDABXAuieagxQu","children":[{"id":"PUBLISHED-ODY3UoJAhLsqR71AARmJJ","children":[],"expanded":false,"name":"Linux Virtual Desktop","urlKey":"linux-virtual-desktop","icon":"","parentDocId":"PUBLISHED-4TMoTzzxDABXAuieagxQu","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-qklVPbUTSdi2iDW1S8HLN","children":[],"expanded":false,"name":"Linux Virtual Machines","urlKey":"linux-virtual-machines","icon":"","parentDocId":"PUBLISHED-4TMoTzzxDABXAuieagxQu","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Virtual Computing","name":"Virtual Computing","expanded":false,"urlKey":"virtual-computing","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-20FJ-CC_3dkM_s4PSdZ2F","children":[{"id":"PUBLISHED-WPQDtP5RBJyVW-5J1Q2M8","children":[],"expanded":false,"name":"TTS with Nari Labs Dia","urlKey":"tts-with-nari-labs-dia","icon":"","parentDocId":"PUBLISHED-20FJ-CC_3dkM_s4PSdZ2F","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Audio Generation","expanded":false,"name":"AI Audio Generation","urlKey":"ai-audio-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-SHmVLRqlmubft4sblMWsR","children":[{"id":"PUBLISHED-Pe3Za4T6xtQeIXLMP5Qqb","children":[],"expanded":false,"name":"Ollama + Webui","urlKey":"ollama-webui","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-787gRc7SEF6IXK4TzZYoZ","isCategory":false,"categoryName":"1-oobabooga","children":[],"expanded":false,"name":"Oobabooga (LLM webui)","urlKey":"oobabooga-llm-webui","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-p379GpTWJQL7SU2H0AqZk","isCategory":false,"categoryName":"8-tgi-llama3","children":[],"expanded":false,"name":"Huggingface TGI with LLama3","urlKey":"huggingface-tgi-with-llama3","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-jDgLap_KE2zPowqF-tpeu","children":[],"expanded":false,"name":"Quantized GGUF models (cloned)","urlKey":"quantized-gguf-models-cloned","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-6rtLs7cFxc61_uHKG8Ez9","children":[],"expanded":false,"name":"vLLM (LLM inference and serving)","urlKey":"vllm-llm-inference-and-serving","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Text Generation","name":"AI Text Generation","expanded":false,"urlKey":"ai-text-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cit6SDDxsqPFy7W79duf2","children":[{"id":"PUBLISHED-GHt2haaEuiL6GPBQyTBxF","children":[],"expanded":false,"name":"Image Generation","urlKey":"image-generation","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-k7iTLYdjB0Frdv3rQW_ca","children":[],"expanded":false,"name":"Stable Diffusion","urlKey":"stable-diffusion","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-zq3-YW2UEXhITlDCCTg6r","isCategory":false,"categoryName":"3-disco-diffusion","children":[],"expanded":false,"name":"Disco Diffusion","urlKey":"disco-diffusion","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Image Generation","name":"AI Image Generation","expanded":false,"urlKey":"ai-image-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-pB-IyK90u76M-ersF3WVv","children":[{"id":"PUBLISHED-oIHRc63tcSWGEACRRhHkI","children":[],"expanded":false,"name":"Video Generation","urlKey":"video-generation","icon":"","parentDocId":"PUBLISHED-pB-IyK90u76M-ersF3WVv","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Video Generation","expanded":false,"name":"AI Video Generation","urlKey":"ai-video-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--OQJF1kNwwKNJ08fC6Yp-","children":[{"id":"PUBLISHED-ii8e4fy1Tlup9M977dXpb","isCategory":false,"categoryName":"10-serving-infinity","children":[],"expanded":false,"name":"Infinity Embeddings","urlKey":"infinity-embeddings","icon":"","parentDocId":"PUBLISHED--OQJF1kNwwKNJ08fC6Yp-","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Text Embeddings","name":"Text Embeddings","expanded":false,"urlKey":"text-embeddings","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","children":[{"id":"PUBLISHED-mMqv1J1TQtpgiXH-eVm14","children":[],"expanded":false,"name":"Blender in the Cloud","urlKey":"blender-in-the-cloud","icon":"","parentDocId":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-AIPiFyiKn52NGHCH5nq1x","children":[],"expanded":false,"name":"Blender Batch Rendering","urlKey":"blender-batch-rendering","icon":"","parentDocId":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"3D Rendering","name":"3D Rendering","expanded":false,"urlKey":"3d-rendering","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-o-tDtwQLB3sjdU5HXlAH4","children":[{"id":"PUBLISHED-1FvJXdy3RgsmFzXR-4Wo2","isCategory":false,"categoryName":"7-mining-on-bittensor","children":[],"expanded":false,"name":"Mining on Bittensor","urlKey":"mining-on-bittensor","icon":"","parentDocId":"PUBLISHED-o-tDtwQLB3sjdU5HXlAH4","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Cryptocurrency","name":"Cryptocurrency","expanded":false,"urlKey":"cryptocurrency","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Yb3VYsvpheOhYYR_1WcII","children":[{"id":"PUBLISHED--X-CVuYaTu3w0xlokb91n","children":[],"expanded":false,"name":"Google Colab","urlKey":"google-colab","icon":"","parentDocId":"PUBLISHED-Yb3VYsvpheOhYYR_1WcII","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Development Tools","name":"Development Tools","expanded":false,"urlKey":"development-tools","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-0E8Al-YdTkoXx5j9keaaQ","children":[{"id":"PUBLISHED-dHQJOGIJkb6T0ZbxrZXSl","children":[],"expanded":false,"name":"Whisper ASR Guide","urlKey":"whisper-asr-guide","icon":"","parentDocId":"PUBLISHED-0E8Al-YdTkoXx5j9keaaQ","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Audio-to-Text","name":"Audio-to-Text","expanded":false,"urlKey":"audio-to-text","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]}],"name":"use-cases","isFoldedByDefault":true,"expanded":false,"urlKey":"use-cases","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-bbpobgkuArCgDczPxCtwT","isCategory":true,"categoryName":"Teams","gitHubPath":"docs/team","children":[{"id":"PUBLISHED-xv11ltkqGShfl-mT7XNLu","children":[],"expanded":false,"name":"Teams Overview","urlKey":"teams-overview","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Ynj96qfNyErZfqAwa2LFD","children":[{"id":"PUBLISHED-BXJXoCydqAATNaD288O0B","children":[],"expanded":false,"name":"Edit team","urlKey":"edit-team","icon":"","parentDocId":"PUBLISHED-Ynj96qfNyErZfqAwa2LFD","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Teams Quickstart","urlKey":"teams-quickstart","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-ACyO06JuEKgjfmMjBzaU9","children":[],"expanded":false,"name":"Team Creation","urlKey":"team-creation","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Hlv-kWiMRQoJ9t01lmNbW","children":[],"expanded":false,"name":"Teams Invitations","urlKey":"teams-invitations","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--XLpXkdTANYzK8ApWif5B","children":[],"expanded":false,"name":"Teams Roles","urlKey":"teams-roles","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-qlS14p3D6OOTphgBEQHOV","children":[],"expanded":false,"name":"Transfer Team Ownership","urlKey":"transfer-team-ownership","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]}],"name":"team","isFoldedByDefault":true,"expanded":false,"urlKey":"team","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","isCategory":true,"categoryName":"Hosting","gitHubPath":"docs/hosting","children":[{"id":"PUBLISHED-m8TTE0HPiTQoEwvtuGXkz","children":[],"expanded":false,"name":"Overview","urlKey":"overview","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cqNh9syMhS3kCV0cBIVEZ","isCategory":false,"categoryName":"2-taxes","children":[],"expanded":false,"name":"Guide to Taxes","urlKey":"guide-to-taxes","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-xal9KdbH6xQ8XSLsN-33N","isCategory":false,"categoryName":"3-datacenter","children":[],"expanded":false,"name":"Datacenter Status","urlKey":"datacenter-status","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-jUSbVYLeXgnYRIW7-27Ol","isCategory":false,"categoryName":"3-payment","children":[],"expanded":false,"name":"Payment","urlKey":"payment","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-VxN0-4JlmoIUdou8WmY1y","isCategory":false,"categoryName":"4-verification-stages","children":[],"expanded":false,"name":"Verification Stages","urlKey":"verification-stages","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-BMs7hJRHIZKzTgQVsYSVB","isCategory":false,"categoryName":"5-vms","children":[],"expanded":false,"name":"VMs","urlKey":"vms","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-1fI7lKmyAWNjt6O0SBcEt","children":[],"expanded":false,"name":"Clusters","urlKey":"clusters","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]}],"name":"hosting","isFoldedByDefault":true,"expanded":false,"urlKey":"hosting","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-3tqe-Mndyz9u9qhOMafF7","isCategory":true,"categoryName":"Distributed Computing","gitHubPath":"docs/distributed-computing","children":[{"id":"PUBLISHED-fAVEqWxj7VHiS6pEHzul0","children":[],"name":"Multi-Node training using Torch + NCCL","urlKey":"multi-node-training-using-torch-nccl","icon":"","parentDocId":"PUBLISHED-3tqe-Mndyz9u9qhOMafF7","conditionalRuleId":"","expanded":false,"docTags":[]}],"name":"distributed-computing","isFoldedByDefault":true,"expanded":false,"urlKey":"distributed-computing","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","isCategory":true,"categoryName":"Console","gitHubPath":"docs/console","children":[{"id":"PUBLISHED-QiRgigF76ZPU_lTGkGoLS","isCategory":false,"categoryName":"1-introduction","children":[],"expanded":false,"name":"Introduction","urlKey":"introduction","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-6s1uSe7_teJrmXnaQD36N","isCategory":false,"categoryName":"2-account","children":[],"expanded":false,"name":"Settings","urlKey":"settings","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-3Yf2niu0UjnpDreE9E94e","children":[],"expanded":false,"name":"Keys","urlKey":"keys","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","isCategory":false,"categoryName":"4-cli","children":[],"expanded":false,"name":"CLI","urlKey":"cli","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","children":[{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Templates","urlKey":"templates","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-se59-5_jE_1KHjoVaYm2k","children":[],"expanded":false,"name":"Volumes","urlKey":"volumes","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-BWpjFVMPqBj_DXfYnPOpG","children":[],"expanded":false,"name":"Billing","urlKey":"billing","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-8gVeMK-hQqPNOq_hHuKlK","children":[],"expanded":false,"name":"Earning","urlKey":"earning","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-v5ZbddMWMtwvGHat6WNu-","children":[],"expanded":false,"name":"Instances Guide","urlKey":"instances-guide","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-za2xagq8dMhXLJVRkj6Mh","children":[],"expanded":false,"name":"Search","urlKey":"search","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-s7iBVewEBNHjyaKXGI018","isCategory":false,"categoryName":"referrals","children":[],"expanded":false,"name":"Referral Program","urlKey":"referral-program","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--7dVUasT1ajlfn9VvagMv","children":[],"expanded":false,"name":"Members","urlKey":"members","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]}],"name":"Console","isFoldedByDefault":true,"expanded":false,"urlKey":"console","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_wACicNun9GwX_re6EJaE","children":[{"id":"PUBLISHED-UPthhpssnt-ANcq6-Hxwa","children":[],"expanded":false,"name":"RTX 5 Series","urlKey":"jDpX-XUZdy-zKWbAZQPNK","icon":"","parentDocId":"PUBLISHED-_wACicNun9GwX_re6EJaE","conditionalRuleId":"","docTags":[]}],"name":"Specific GPUs","isFoldedByDefault":false,"isCategory":true,"categoryName":"Specific GPUs","expanded":true,"urlKey":"specific-gpus","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]}],"hostingTitle":"","logoRedirectURL":"","hasPrimaryColorLinks":true,"hostingColor":"#2166ae","darkHostingColor":"#2166ae","secondaryColor":"#e0eefc","darkSecondaryColor":"#14467a","isIndexable":true,"template":"stripe","contentLayout":"two-column","hostname":"docs.vast.ai","hostnamePath":"","proxyDomain":"","publicLogoURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/hY3Z66NYu_wT-evx8EFi5_logo-symbol-dark.svg?format=webp","darkPublicLogoURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/BBMribM7Vqri9n_fLelvV_logo-symbol-light.svg?format=webp","publicTheme":"dark","faviconURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/K8kL9zPy3Yuuym96Ony5l_vast-social-profile-photo.png","spaceLinks":[{"label":"Guides","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","hostnamePath":"","versionLinks":[],"icon":"","hostname":"docs.vast.ai"},{"label":"Instances","docSpaceId":"PUBLISHED-3IG3Byg89UtcvaXwjp66z","icon":"","hostnamePath":"instances","hostname":"docs.vast.ai","versionLinks":[]},{"label":"Serverless","docSpaceId":"PUBLISHED-5WC30cNrS-qdU0I5VeweU","icon":"","hostnamePath":"serverless","hostname":"docs.vast.ai","versionLinks":[]},{"label":"API","docSpaceId":"PUBLISHED-yC9YDSGxNOpyhS0i8A-6f","icon":"","hostnamePath":"api","hostname":"docs.vast.ai","versionLinks":[]}],"versionLinks":[],"externalLinks":[{"label":"Console","url":"https://cloud.vast.ai/","isPrimary":true},{"label":"Discord","url":"https://discord.gg/hSuEbSQ4X8","isPrimary":false}],"landingPageType":"first-doc","landingTemplate":"","landingPageHeaderText":"","landingPageSubheaderText":"","landingHeroBgLightURL":"","landingHeroBgDarkURL":"","footerTemplate":"","headerIncludes":"\u003cscript type=\"text/javascript\"\u003e\nwindow.$crisp=[];\nwindow.CRISP_WEBSITE_ID=\"734d7b1a-86fc-470d-b60a-f6d4840573ae\";\n\n// Set up the ready trigger before loading Crisp\nwindow.CRISP_READY_TRIGGER = function() {\n    // Set current page URL as session data\n    $crisp.push([\"set\", \"session:data\", [[\n        \"current_page\", window.location.href\n    ]]]);\n};\n\n(function(){\n    d=document;\n    s=d.createElement(\"script\");\n    s.src=\"https://client.crisp.chat/l.js\";\n    s.async=1;\n    d.getElementsByTagName(\"head\")[0].appendChild(s);\n})();\n\n// Also track URL changes if you have a single-page application\nwindow.addEventListener('popstate', function() {\n    if (window.$crisp.is(\"website:available\")) {\n        $crisp.push([\"set\", \"session:data\", [[\n            \"current_page\", window.location.href\n        ]]]);\n    }\n});\n\u003c/script\u003e\n\n\u003c!-- Google tag (gtag.js) --\u003e\n\u003cscript async src=\"https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG\"\u003e\u003c/script\u003e\n\u003cscript\u003e\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-DG15WC8WXG');\n\u003c/script\u003e","jwtRedirectURL":"","googleAnalyticsId":"","intercomId":"","isArchbeeBrandVisible":false,"archbeeBrandPosition":"doc-tree","i18nLanguage":"en","showReadTime":true,"showLastUpdate":true,"showThemeSwitcher":true,"showContributors":false,"showDocFeedback":true,"showEditInGitApp":true,"showDocNavigationButtons":true,"revisions":[],"customJS":"","customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","customFont":null,"custom404":"","createdAt":"2025-01-04T02:25:03.718Z","showPdfBookLink":false,"pdfBookLink":"","llmsTxtLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/llms-txt/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-llms.txt","llmsFullTxtLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/llms-txt/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-llms-full.txt","sitemapXmlLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/sitemap-xml/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-sitemap.xml","protectionType":"None","isAuthenticated":true,"tagsCache":[],"isQAndAEnabled":false,"isQAndAPublicEnabled":false,"isQAndAAIEnabled":false,"isQAndAApprovalEnabled":false,"isLlmEnabled":true,"isLlmInternal":true,"isLlmExternal":true,"authPageLayout":"one-column","authPagePosition":"left","authBgImageURL":"","authDarkBgImageURL":"","gitAppRepoId":"","gitAppRepo":null,"isGithubEnabled":false,"isBitbucketEnabled":false,"doc":{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","name":"Templates","icon":"","title":"Templates","previewImageURL":"","summary":null,"urlKey":"templates","description":"","urlAlias":"","data":{"nodes":[{"id":"CpgO8sqy_Q-ayJU_DTIEd","type":"h2","children":[{"text":"What is a Template?","id":"o8XfUHePSNsYM5-5wH90f"}]},{"id":"V6pPwja_CtiiCLZYNI-0x","type":"paragraph","children":[{"text":"A template is how Vast helps you launch an instance, setting up your rented machine with whatever software and formatting you need. Templates are generally used for launching instances through the web interface, but they can also be used in the CLI or through the API.  In this document, we will focus on the web interface, but we will link to other relevant documentation thoughout.","id":"7wihzvBlnYBuT_0BaUyyJ"}]},{"id":"POZHA74GsWck_gg0uOf85","type":"paragraph","children":[{"text":"In the simplest technical terms, you can consider a template to be a wrapper around ","id":"wcLXnHn_RELAh4E-Tqsk_"},{"text":"docker run","highlight":true,"id":"QDAvgO47bDGtx9ZGgksji"},{"text":". The template contains all of the information you want to pass to our systems to configure the environment.","id":"laMktbnJgemg1VXBB5wzt"}]},{"id":"voAr4ObBAFj8g5zyuOMe7","type":"paragraph","children":[{"text":"You can browse the template section of the web interface at ","id":"64xBbq3L3sAbUwdnsdxsb"},{"id":"5QvmsWMPidVdtItz3QYcL","type":"link","data":{"href":"https://cloud.vast.ai/templates/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"cloud.vast.ai/templates","id":"W149oEwqdIzgsqUiDERwM"}]},{"text":"","id":"qmVH9LgB8zc8eKDYdjDoh"}]},{"id":"ZrLsOvjaSiIb_Cc43-NTi","type":"h2","children":[{"text":"Recommended Templates","id":"P_Nwc_Jhl1Ad5x2CmaMTf"}]},{"id":"BJKoQvjVPKuFSU4z6Gkfo","type":"paragraph","children":[{"text":"We provide several recommended templates to help you get started.  These are pre-configured environments that you can use as-is, or you can tweak them to your own requirements.  ","id":"LEfO1R1ujcItEc-kLR5yR"}]},{"id":"7927KFLcg8ZDx03uv6O0p","type":"callout-v2","data":{"type":"info"},"children":[{"id":"bXyjihnP6-LH0jnakK9zS","type":"paragraph","children":[{"text":"It's a great idea to look at how these templates have been configured to guide you in creating your own.","id":"TdETJu_YVf3FBrHkG8bH8"}]}]},{"id":"twSx9CwF5eP3yZ9tsm4Gc","type":"paragraph","children":[{"text":"Find out more about our recommended templates in the ","id":"B_Z5VxrxtH8UsltM9foo4"},{"id":"V5xrR3g7VZ_tIhl3p8gw0","type":"link","data":{"href":"https://docs.vast.ai/templates#oh7MS","newTab":false,"hasDisabledNofollow":false},"children":[{"id":"B_Z5VxrxtH8UsltM9foo4","text":"Vast Templates"}]},{"id":"B_Z5VxrxtH8UsltM9foo4","text":" section later in the guide."}]},{"id":"HSXU4MKrrmwYj95JyFuuZ","type":"h2","children":[{"text":"Quick Start","id":"x9XL8-IaPLyn1CnJmgzIc"}]},{"id":"p755T8YqNFLJPmoZICcOx","type":"paragraph","children":[{"text":"To Jump right in and run a template, follow these steps","id":"uJuUdxZBHCxHkAgvnIT8y"}]},{"id":"YfRscWmrKmkEsT63K30qs","type":"paragraph","children":[{"text":"Visit the templates section of the console where you will find all of our recommended templates.","id":"xFIPUvmpy3jni2Y6wjy_n"}]},{"id":"Fof9Ugr4rOg_R71IX2BEM","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/iHVunKrkpyXVJdTsz6NgP_image.png","signedSrc":"","size":100,"width":1707,"height":777,"position":"center","caption":"Recommended Templates","alt":"Recommended Templates page"},"children":[{"text":"","id":"UFRK_e28vnJGIJ52c3JOq"}]},{"id":"g8cyoZvroTbh_IDGHsSyI","type":"paragraph","children":[{"text":"Browse through the templates until you find one that meets your requirements.  In this guide we will use NVIDIA CUDA, which is the first on the list. It's a great starter template as it just includes the CUDA development environment, plus a few extras to improve the user experience.","id":"XLBpj4UvpS6SL1PV6l3pR"}]},{"id":"mAb3gBotPKD8hOOXl07Ct","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/dlaQQ5RGmCbntmi23IHEI_image.png","signedSrc":"","size":100,"width":417,"height":274,"position":"center","caption":"NVIDIA CUDA template","alt":"NVIDIA CUDA Template whos play button in bottom left corner"},"children":[{"text":"","id":"sakYzP1KZaSpvYidTGqbg"}]},{"id":"Kb34JqtsIqDjJSO9HVT4j","type":"paragraph","children":[{"text":"Now, click the 'play' button.  This will load the template and take you to the available offers","id":"b_NBG3ryDUJupQDvHPeMI"}]},{"id":"ZTbsUhMbsbR64Kd-wI2-u","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/Rs9SQ5RokRrAcYSHBGX3L_image.png","signedSrc":"","size":100,"width":1201,"height":837,"position":"center","caption":"Offers Page","alt":"Offers page with GPU filter active"},"children":[{"text":"","id":"np3aMPRm12er-SiX5zLyr"}]},{"id":"_3fvWKsd0o0zL_eLtyJXl","type":"paragraph","children":[{"text":"There are filters available at the top of the page to help you target a particular GPU.  You will also find many additional filters on the left of the page for more fine-grained control over the instances you find.","id":"OBQdmTK4sPhkHYZ-VhOBs"}]},{"id":"CJ-ZBSxzdeLtOijXx0B7s","type":"paragraph","children":[{"text":"When you have found a suitable offer, simply click the 'RENT' button to create your new instance.","id":"4kI79k5NKe9FXtow7i8QZ"}]},{"id":"B5GwN0C5qD2lC38L1cE_e","type":"paragraph","children":[{"text":"You can now visit ","id":"4sAUnlchnxJlVzemhOTGP"},{"id":"D0U20COKer3wpInEczzXK","type":"link","data":{"href":"https://cloud.vast.ai/instances/"},"children":[{"text":"cloud.vast.ai/instances","id":"3sSP2S7G302G3g-95-7Q2"}]},{"text":" where you will find your running instance.  It may take a few minutes to be ready as everything is being set up.","id":"9_n3kZq4MS4q9rTU8olSC"}]},{"id":"w7m_tmxb6lAxD5PE4W4C3","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/sy34F-FInPC77OnukpW6o_image.png","signedSrc":"","size":100,"width":906,"height":718,"position":"center","caption":"Instance is Ready","alt":"Instance view with blue open button"},"children":[{"text":"","id":"-wgDSyAPD3feNM12Old4q"}]},{"id":"YbDcQAtB4Qtaru0Of4T6l","type":"paragraph","children":[{"text":"When it is ready you will see the blue open button.  This indicates that the instance is ready to connect.","id":"ArstBULv4zKlIP7Vhtet-"}]},{"id":"Qv12hbNa1TrCP6nfqxAln","type":"callout-v2","data":{"type":"info"},"children":[{"id":"VvDTXY_zP3z6RJp7urwVf","type":"paragraph","children":[{"text":"The action of the open button depends on the template you have chosen - In this example you will be transferred to the ","id":"V-T-vHt_dTY0IlDBq0N4S"},{"id":"56LnTpf9GXlr23DAHcKxP","type":"link","data":{"href":"https://docs.vast.ai/instance-portal","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Instance Portal","id":"ecpissPkjCmOjEVp68U-f"}]},{"text":".  Later in this guide we will discuss how this can be configured.","id":"iyeXQV5spsEpMrC4vySuC"}]}]},{"id":"LrOMESy20X_NYizAh1Rfm","type":"h2","children":[{"text":"Creating Your Own Template","id":"U8ZnBRTL846UG7P76grnT"}]},{"id":"n660Dp-I2FlvXbviFe5Bc","type":"paragraph","children":[{"text":"You can create a new template from scratch, or you can edit an existing template.  For this gude we will edit the NVIDIA CUDA template we used in the Quick Start section. ","id":"etCH2F56XVHp3JFYoU2Wx"}]},{"id":"qSImBmst_ADqdXJ3sDZKE","type":"paragraph","children":[{"text":"From the templates page, click the pencil icon on the template card to open up the template editor.  You'll see two tabs ","id":"jwKWt6mBjb83Ck3lEw_MV"},{"text":"Config","highlight":true,"id":"t8mFwl0GT3Hx718tHKIMF"},{"text":" and ","id":"rJKa3WKPr0cNsg_R50Eu1"},{"text":"ReadMe","highlight":true,"id":"c0jcfRPS0PFZMKUaUkYzJ"},{"text":".  We'll start with the config tab:","id":"hLoNQah_H-eWV1tLRzKBC"}]},{"id":"VPKyq6sy57PpHTBrUBmOS","type":"h3","children":[{"text":"Identification","id":"2VUIsBYh3Eh6oo7hPAgbU"}]},{"id":"09NHLpVkZPi4uF4LQ51yB","type":"paragraph","children":[{"text":"The first section helps you to keep your templates organized.","id":"Z44iGZG-OG_LGLgJOxmlk"}]},{"id":"vdY6FA2BNflMClqTLl1nm","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/RbISQHd_ERbF8HMIqams3_image.png","signedSrc":"","size":100,"width":936,"height":294,"position":"center","caption":"Template editor: Identification","alt":"Identification section of the template editor"},"children":[{"text":"","id":"mTm9hJWmTXno7tUTzgJ40"}]},{"id":"jJDybsR7HrwiUuEC-7gqU","type":"paragraph","children":[{"text":"Template Name","bold":true,"id":"qenG44E15wB4BX06dopnG"}]},{"id":"eXPudaI6sBFDqXOqAEshZ","type":"paragraph","children":[{"text":"This will be displayed in bold on the template card.  Choose something that helps you identify the template amongst your other templates.  For this guide, we will change the name to 'NVIDIA CUDA - Demo'","id":"-J2Z500mVtZxDNbgP8Oxf"}]},{"id":"YLp7gbSGPUOqL2k0tlGb0","type":"paragraph","children":[{"text":"Template Description","bold":true,"id":"khA9WW7y5WTHYGOJh8yUR"}]},{"id":"rDQ-G82YNvJx1IwxRtulG","type":"paragraph","children":[{"text":"This field helps describe the function and purpose of the template. Completely optional for your own purposes, but very helpful if you intend to make this template public or share it with others.","id":"mzWXZIM3GqKQLp-sQFDIZ"}]},{"id":"twT2AbtFAzFqpJ_BtcNNr","type":"h3","children":[{"text":"Docker Repository And Environment","id":"LwHqCBCBMrXrAe00eSZIf"}]},{"id":"MAwEaaeEuYGec9WjbZ9gg","type":"paragraph","children":[{"text":"This is where you define the Docker image you want to run, along with any options we want to pass into the container.","id":"5WILIVPTb84nhKMziXXHg"}]},{"id":"nc4RoAsqNwzMEU7zsCVDP","type":"paragraph","children":[{"text":"","id":"7PvgOX1fZ9JQp1M8dsaWA"}]},{"id":"Lj8xhkEDYLTZluL4LcpMZ","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/49uAhXy-7NFBtLtPwNhWh_image.png","signedSrc":"","size":100,"width":844,"height":775,"position":"center","caption":"Template editor: Docker","alt":"Docker section of the template editor"},"children":[{"text":"","id":"631-wK5OkwWYzigwjYTOm"}]},{"id":"ekyqVR_OURVyMEwE-M4bf","type":"paragraph","children":[{"text":"Image Path:Tag","bold":true,"id":"iUb2qWPdEnroOxJOQE_Yr"}]},{"id":"NFn5VJy2EAMk3cYHO_GPR","type":"paragraph","children":[{"text":"Here is where you can define the docker image to run.  This field must be in the format ","id":"a5URn4Z5K91CRDhKdAB-q"},{"text":"repository/image_name:tag","highlight":true,"id":"HScubrcYwmw0jauf7q29k"},{"text":".","id":"Er8kJPTTobwXpItLTskY5"}]},{"id":"P9KGqfbBlYsF6gJhM_xo6","type":"paragraph","children":[{"text":"Many of our templates pull from DockerHub but you can use any container registry - Just remember to add the full path if you're using an alternative registry. Eg. ","id":"avyGG3OmEVrc6bXXtXsll"},{"text":"nvcr.io/nvidia/pytorch:25.04-py3","highlight":true,"id":"MyeYdgIseHvshhZ6MiCan"}]},{"id":"ge3MQwNTffCly04Ckdf3A","type":"paragraph","children":[{"text":"Version Tag","bold":true,"id":"Fbc2Kc2uG0ctohjW4c4ov"}]},{"id":"86L0wRbwN3vUJDJIZzNqc","type":"paragraph","children":[{"text":"For many registries we are able to pull the available list of tags so this field allows you to quickly select another version.","id":"bzXaJ64bmWEzFq7hfyFdN"}]},{"id":"Erv6FM7CDdw0-gIByqSQY","type":"paragraph","children":[{"text":"There is also a special ","id":"C5AzM2nTmLwacGTY7JUFr"},{"text":"[Automatic]","highlight":true,"id":"a4dUoHAjhZCJZv6GrT9TM"},{"text":" tag you can use.  With this selected, the machine you choose for your instance will pull the most recent docker image that is compatible with that machine's own CUDA version. ","id":"txsQdP-QtsdEtMBp4xt08"}]},{"id":"VrhJUxV9uUKGpDMMFz0wS","type":"paragraph","children":[{"text":"This will only work if the image tag contains the CUDA version string. For example: ","id":"wrpU3VjV375j_Y7BoSS64"},{"text":"my-image-cuda-12.8","highlight":true,"id":"on4SWqrxGI-FnRtO35h_6"},{"text":" would be loaded on a machine supporting CUDA 12.8, but a machine with only CUDA 12.6 would pull ","id":"Kfy8PcSvdrBd_52IxBDXT"},{"text":"my-image-cuda-12.6","highlight":true,"id":"M12QbJ4dczKmE19XnFGjU"}]},{"id":"8Y9b2Qkjun3njoErnQ2AZ","type":"paragraph","children":[{"text":"Docker Options","bold":true,"id":"FvVaUZv4gDTJzUuwx-C1i"}]},{"id":"-KjxqrkYSj-ulMKqnjDf3","type":"paragraph","children":[{"text":"This field is a textual representation of the ports and environment variables declared in the sections beneath it.  You can edit it directly or you can use the page widgets.","id":"-SIy-7yZREl8H4gQewuf4"}]},{"id":"52edynKNaXc3iU6SbzfzT","type":"callout-v2","data":{"type":"info"},"children":[{"id":"6DA8QzAkugZw2TEJA8lRn","type":"paragraph","children":[{"text":"This field will only accept ports and environment variables.  Other docker run options will be ignored.","id":"l1DNr3IfkjrljJ_6iPTGy"}]}]},{"id":"FiIJYKCbj5tD7L_x75AR5","type":"paragraph","children":[{"text":"Ports","bold":true,"id":"VrVxL2D5sg7PkXyCXcF82"}]},{"id":"lEZ2bvO6LPTIREQl0d0Qr","type":"paragraph","children":[{"text":"To access your instance via the external IP address, you will need to add some ports to the template.  You can add both TCP and UDP ports.","id":"8ZVQe_WiHoGkowhvzZQaq"}]},{"id":"SmTaPb9vrkg3nYw_zv8pI","type":"paragraph","children":[{"text":"When your instance is created, a port will be randomly assigned to the external interface which will map into the instance port you selected.","id":"bwbu0EwuYVjiMMo0LIv21"}]},{"id":"SUHghp5jfXfXHY69ewpo-","type":"paragraph","children":[{"text":"Environment Variables","bold":true,"id":"HYBrwUEHQh0M5TvtCKiAE"}]},{"id":"Up7jjZkRHgBuB0EPFvr3P","type":"paragraph","children":[{"text":"Here you can add any environment variables that your docker image requires.  Do not save any sensitive information here if you are planning to make the template public.","id":"YFAMGFtJnL280AkPq8-vH"}]},{"id":"mwpi7TywIvQ4uP9yR0Q4t","type":"paragraph","children":[{"text":"Place any variables with sensitive values into the Environment Variables section of your ","id":"N7iW8Pb0MsG3dXmfxFp9S"},{"id":"TijQaWifPuMIRe4TxykH6","type":"link","data":{"href":"https://cloud.vast.ai/account/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"account settings page.","id":"t_-bhlpTB1xtMPegr_VSq"}]},{"text":"  They will then be made available in any instance you create, regardless of the template used.","id":"2sE3mhGiiarceGCDj-Vt1"}]},{"id":"cK2BTgFfpwGFNiL4odpl8","type":"paragraph","children":[{"text":"You can find out more about port mapping and special environment variables in our ","id":"3mrXoIvMwna__MpJZz3lk"},{"id":"31ir_NOd9XYo3ZTTajFBX","type":"link","data":{"href":"https://docs.vast.ai/instances/docker-execution-environment","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Docker Execution Environment","id":"Xi9YAKrXG5nl6cRTA7r4a"}]},{"text":" guide.","id":"LdTWUaDmP4NhAhfYqmTIj"}]},{"id":"K5vNzpdpAeNQh0P-kiyVp","type":"h3","children":[{"text":"Select Launch Mode","bold":true,"id":"VP9flNToysN1IglejzHad"}]},{"id":"r6iIlUmzm02Sevys_6xpr","type":"paragraph","children":[{"text":"Templates offer three launch modes you can select from.  Our recommened templates will usually launch in Jupyter mode for easiest access, but you are free to choose whichever suits your needs.","id":"et_2X62JlAs-7BaA5r0Cy"}]},{"id":"oWrp4ZM5aVIga1iQycJAD","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/E8Jc-IjeiPLPwftGRAej3_image.png","signedSrc":"","size":100,"width":936,"height":191,"position":"center","caption":"Launch mode options","alt":"Launch mode selection options"},"children":[{"text":"","id":"0m5LbxYFlSogvUjxLB2Fd"}]},{"id":"xRz45rKL4CnywMkr0GzwJ","type":"paragraph","children":[{"text":"Jupyter-python notebook + SSH","bold":true,"id":"BBuZmqyFTrAvTW2nBeIZN"}]},{"id":"DF8UMgQE4m-lQJFGXbcIz","type":"paragraph","children":[{"text":"When you run the template in this mode, we will install Jupyter and SSH at runtime.  Jupyter will be available on mapped port ","id":"XBUI9vLDs8Kwaw9CQxoFc"},{"text":"8080","highlight":true,"id":"SjW8DOtPMlY3mwuqM5JRx"},{"text":" and SSH will be available on mapped port ","id":"aMWSo6oqChY27SiB_fGeF"},{"text":"22","highlight":true,"id":"6vz5Rn-vHsRl2tktZ-Ij_"},{"text":".","id":"GQSz6ytxk9HihXegYct07"}]},{"id":"bwrPapd4mBN9kHIg94-pR","type":"paragraph","children":[{"text":"Interactive shell server, SSH","bold":true,"id":"RhJLX4Kp5GQx0GVXPmKGm"}]},{"id":"A2zx1Yqmfgdwzks8Ocgs5","type":"paragraph","children":[{"text":"As above, but SSH only with no Jupyter installation.","id":"Zi0UC7n4_4qihf-Nm5_UO"}]},{"id":"pR-tBpqDLEfv_LxRyc7Ls","type":"callout-v2","data":{"type":"warning"},"children":[{"id":"F5amr17dBwUf-0UM_Lymp","type":"paragraph","children":[{"text":"In both Jupyter and SSH mode, the docker entrypoint for your image will not be run.  It will be replaced with our instance setup script so you should use the on start section (documented below) to start any services.","id":"E2sMc1iZgyuZELtbGYrt8"}]}]},{"id":"OnXEVvVs4KK-6X3no_fP_","type":"paragraph","children":[{"text":"docker ENTRYPOINT","bold":true,"id":"Rqete39egS_z4BYVx-gfX"}]},{"id":"c7nKjfZhqNPcAsO_HoZ4P","type":"paragraph","children":[{"text":"In this mode, your Docker image will run precisely as it is. We will not include any additional software or access methods. If your Docker image does not offer SSH or another appropriate interface, please select one of the alternative modes if you need to interact with the running instance.\nAn additional field will be showin when using this launch mode to allow passing arguments to the image entrypoint.","id":"rL4k6Dwf5DJBvMy8inFj7"}]},{"id":"nH5Cmte7KnoUiKkpy_nTR","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/6MSmdumOj0wHY-pr2z2YN_image.png","signedSrc":"","size":100,"width":936,"height":63,"position":"center","caption":"Entrypoint arguments","alt":"Field allowing for argument passing"},"children":[{"text":"","id":"70Y0RFrMW57z9MKHEuYnu"}]},{"id":"4A154gsbNDQi-HQhBhMS_","type":"h3","children":[{"text":"On-start Script","id":"LBXyNrWP2b5f4ZFHDpitw"}]},{"id":"Ia-0x5O-MnGCy7_MRmgBG","type":"paragraph","children":[{"text":"Here you can enter a short Bash script which will be run during instance startup.  It is only available when using the Jupyter or SSH launch modes, and is most useful for starting any services that your docker image would have launched if run if the entrypoint had been executed.","id":"kwGnD3Iexhybdg0Aue7FA"}]},{"id":"KMln09XVtt6vXu79CH-OS","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vXS-1QECGWMiUqreqp5JB_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vXS-1QECGWMiUqreqp5JB_image.png","size":100,"caption":"","isUploading":false,"width":936,"height":177},"children":[{"text":"","id":"alhBtXNbP7I66yYttPZ-c"}]},{"id":"uYvTlajW7e64SGXUSMzew","type":"paragraph","children":[{"text":"In our example 'NVIDIA CUDA' template we are simply calling the ","id":"fgqKtFkQDgaKxJVw03JX5"},{"text":"entrypoint.sh","highlight":true,"id":"xvqHukBerr9LPFJO6F_7q"},{"text":" script which contains the logic for preparing the instance.","id":"BC2KoIv-W1WStlJwyz1yW"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vXS-1QECGWMiUqreqp5JB_image.png","signedSrc":"","size":100,"width":936,"height":177,"position":"center","caption":"","alt":"On start field with entrypoint.sh "}},{"id":"pi25h_BHLDt67esXuDH_S","type":"h3","children":[{"text":"Extra Filters","id":"nI2SvJvucHnbwUkG8EfDN"}]},{"id":"LWnvV7BO_e4yBfnFAlirx","type":"paragraph","children":[{"text":"Use this area to place restrictions on the machines that should show up in the search page when the template is selected.","id":"8Wwyyxz57ggN55nuQsoOI"}]},{"id":"HBEO_EvON9nkIKGj2wpjz","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/nuuwsNgDalF8Xo3XKompc_image.png","signedSrc":"","size":100,"width":936,"height":142,"position":"center","caption":"Extra Filters","alt":"Extra filters showing this template is configured for both AMD64 and ARM64 CPUs"},"children":[{"text":"","id":"lf-cUp1_kDJHcF69L9sAn"}]},{"id":"smLU9-p50FTwX9f-rLHmt","type":"h3","children":[{"text":"Docker Repository Authentication","bold":true,"id":"EU9q7y9odw0Md1Fwan1jx"}]},{"id":"XvwYR2HtuJw9AxOeuYamC","type":"paragraph","children":[{"text":"If you are using a private Docker image then you will need to add authentication credentials so the machine running the instance can download it.","id":"f0aEB0d5qsahGZUV_YvbB"}]},{"id":"b4SvfoOQ1raJs5Ywd7GN8","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/fc7faHt8TaQaDc0LSF6fr_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/fc7faHt8TaQaDc0LSF6fr_image.png","size":100,"caption":"","isUploading":false,"width":945,"height":152},"children":[{"text":"","id":"n5IFJgaoQC-Ri86vYVAWd"}]},{"id":"ppN-FSxJcd0g8vPBG6kFf","type":"h3","children":[{"text":"Disk Space","id":"GodzhjHfAYkcf8Vhiu0a9"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/fc7faHt8TaQaDc0LSF6fr_image.png","signedSrc":"","size":100,"width":945,"height":152,"position":"center","caption":"","alt":"Shows where to enter server, username and token"}},{"id":"a2hjtjnk-NCX1Ww2K6KBq","type":"paragraph","children":[{"text":"By setting the disk space in the template, you can ensure that new instances created from the template with use this amount as a minimum. ","id":"98aNG08RRR50N9Yv2p5eO"}]},{"id":"Wm_I0uo41pwqzSoZBQjGW","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/xAnaHLXIXdm9TmLcKhPej_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/xAnaHLXIXdm9TmLcKhPej_image.png","size":100,"caption":"","isUploading":false,"width":945,"height":129},"children":[{"text":"","id":"w9tZfne9-El3_vSswNRkR"}]},{"id":"CLYMPVNBjO9R9NgqEZ-Jr","type":"h3","children":[{"text":"Template Visibility","id":"em8eMqINk6cgk-wf4Kr5p"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/xAnaHLXIXdm9TmLcKhPej_image.png","signedSrc":"","size":100,"width":945,"height":129,"position":"center","caption":"","alt":"Default disk space"}},{"id":"Z_V03iBGgQuoqw7z7JYC-","type":"paragraph","children":[{"text":"Any template marked as public will be available in the template search system, while private images will not.","id":"laojAzlM61Hs5MOWZrl7I"}]},{"id":"g8qmQDzjac0jDufLXYe4l","type":"paragraph","children":[{"text":"Private templates can still be used by others if you have shared the template URL.","id":"GlVFv2tWrl65sZFujB7wO"}]},{"id":"BKvIhXFkIQBeY9u5HhgDM","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/rRnwi3JuUxjPynXIrGwJm_image.png","signedSrc":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/rRnwi3JuUxjPynXIrGwJm_image.png","size":100,"caption":"","isUploading":false,"width":945,"height":65},"children":[{"text":"","id":"UTcj8g1jm17HAykK4MSbP"}]},{"id":"1cAH2s46hpST_lAOK7nhl","type":"callout-v2","data":{"type":"danger"},"children":[{"id":"u3l-3peOTZCsGkAKhwSFb","type":"paragraph","children":[{"text":"Never save a template as public if it contains sensitive information or secrets.  Use the account level environmnet variables as an alternative.","id":"Rw86tua-ZQ5sifr3q9C-K"}]}]},{"id":"6hveS2XKGt_kD0owwtf6u","type":"h3","children":[{"text":"CLI Command","id":"3KRwvS8oStdEvora2xBeB"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/rRnwi3JuUxjPynXIrGwJm_image.png","signedSrc":"","size":100,"width":945,"height":65,"position":"center","caption":"","alt":"Template visibility selector"}},{"id":"zbM2wnqoSX-8FGHNp2TQi","type":"paragraph","children":[{"text":"Templates can be translated directly into CLI launch commands.  This read-only area shows what you would need to type or copy to the CLI if you wanted to programatically launch an instance this way.","id":"qDgrb7nyP04gFlDS7S-Fn"}]},{"id":"GylGWB5b0j7AcnnwY3qMs","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/TQPTz9I5em8-jfnw_DSi0_image.png","signedSrc":"","size":100,"width":945,"height":183,"position":"center","caption":"CLI Command","alt":"Launch a template via the CLI"},"children":[{"text":"","id":"tIdfu_jbBBastjUatUh8s"}]},{"id":"AiU2cHfHX62Hi0BQGu_6D","type":"paragraph","children":[{"text":"To learn more about starting instance from the CLI, check out our ","id":"alxq5im95SN5CQkMqjWoi"},{"id":"1hz3xGcYkYSyuv_rXTPTP","type":"link","data":{"href":"https://docs.vast.ai/api/overview-and-quickstart","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"quickstart guide","id":"evFEXhWRBmStfWBroaLqW"}]},{"text":".","id":"ZtgcovaWPD4H1BjHzWURL"}]},{"id":"7F512JA-m-uUivslpvKqy","type":"h3","children":[{"text":"Save the Template","id":"SzR-M1k2LW-Dq078TmWBU"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/TQPTz9I5em8-jfnw_DSi0_image.png","signedSrc":"","size":100,"width":945,"height":183,"position":"center","caption":"","alt":"CLI Command section showing how to launch the NVIDIA CUDA template"}},{"id":"kQWjLBtt-rYsc3kDV_TwX","type":"paragraph","children":[{"text":"Finally, you can save the template.  If you are creating a new template or editing one which is not associated with your account - Such as one of our recommended templates - The buttons you see will be labelled 'Create'.  For your own templates, you will see them labelled 'Save'","id":"g1sYu3upqyA5NS1FPyNYI"}]},{"id":"HHXpE6MYMZqPaJ0YuBi0v","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/pheY6imcsh5__O_-Thmyq_image.png","signedSrc":"","size":100,"width":323,"height":56,"position":"center","caption":"Save Buttons","alt":"Buttons for saving"},"children":[{"text":"","id":"DmZHj2Pc-Sdkx23Qda7rm"}]},{"id":"SkvDWQv7m3ql2qga6Ed4X","type":"paragraph","children":[{"text":"The 'Create' button will create a copy of the templaye in the 'My Templates' section of the ","id":"4qcUsAX-X8zUcZ6v-TfF_"},{"id":"2xTg3VahcVaQYkNNNw4q8","type":"link","data":{"href":"https://cloud.vast.ai/templates/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"templates page","id":"TJNPaN0yfel8FO3fqieFF"}]},{"text":" for you to use later  The 'Create \u0026 Use' button will save the template, load it and then open up the ","id":"Exol1RGcnud5ojthQSqf-"},{"id":"F69_KRzYS4E1RU27tdYzf","type":"link","data":{"href":"https://cloud.vast.ai/create/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"offers page.","id":"zDBHw_g0i1Dd2598ynZx5"}]},{"text":"","id":"_XC0F9OXC2bxPdzZ24B7R"}],"data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/pheY6imcsh5__O_-Thmyq_image.png","signedSrc":"","size":40,"width":323,"height":56,"position":"center","caption":"","alt":"Buttons for saving the template"}},{"id":"SPKENORDJs9vLjGvpCJ_o","type":"h2","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/pheY6imcsh5__O_-Thmyq_image.png","signedSrc":"","size":40,"width":323,"height":56,"position":"center","caption":"","alt":"Buttons for saving the template"},"children":[{"text":"Updating a Template","id":"7vxA9Qxe7ga06ihTegPgj"}]},{"id":"k5X_s6b6Ci6COft1t5vNI","type":"paragraph","children":[{"text":"If you want to make changes to a template you previously saved, simply navigate back to the templates page and select 'My Templates'.  Here you'll be able to make your changes by clicking the pencil icon.","id":"dxz-uTlzvB_g5gNjvNnZp"}]},{"id":"GdVbbTAXJbsw40QaGeJuj","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/JL86zURfZuCur4mvOPXQG_image.png","signedSrc":"","size":100,"width":596,"height":491,"position":"center","caption":"My Templates","alt":"My templates showing the NVIDIA CUDA - Demo template"},"children":[{"text":"","id":"CPiLsAYjisJAzMQ6qxkTC"}]},{"id":"5H6akaJ-JgkLevlZrAJx3","type":"h2","children":[{"text":"Sharing a Template","id":"unkfyR8_8iXrLTKxDF-PR"}]},{"id":"D3p7Z0aJOrwL5k1ZLPUxL","type":"paragraph","children":[{"text":"It's really easy to share your template with other users.  We have two special links you can use and both include your referral code so you can earn if new users sign up - Find more about that ","id":"lm1zs-BhQkkNsIL7EPvRR"},{"id":"IxJNS3194sZqsumUCHK2c","type":"link","data":{"href":"https://docs.vast.ai/referral-program","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"here.","id":"jB7iLgxAO1IR8ppm26jPc"}]},{"text":" ","id":"paAEy3THyPyznG_sINU5i"}]},{"id":"aRHP6ejWtJEd-JNdMZron","type":"paragraph","children":[{"text":"To share, click the three dots icon in the bottom right of the template card.","id":"zT2ivHgG0jqjZGSDVlcqQ"}]},{"id":"RitH9Np9j59dCNcrb9l_H","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/vr0DPCqmQbaIhNfEspUwx_image.png","signedSrc":"","size":100,"width":462,"height":197,"position":"center","caption":"Template Sharing Menu","alt":"Menu shows sharing options"},"children":[{"text":"","id":"64vdAD8sLSXT7nYRp-tAI"}]},{"id":"hXqUcWxakbII1masdPe6A","type":"h3","children":[{"text":"Copy referral link","id":"E-wSCi9uji7ucXfmmH4Wv"}]},{"id":"uSTPQrhHSCmhPuhMuxjsK","type":"paragraph","children":[{"text":"This will copy a link that contains your referral ID, creator ID and the template name.  It will always point to the most recent template you created with this name - Really useful if you want people clicking the link to always get the most recent version.","id":"BGo5DOqJ45AitrWO4c7TB"}]},{"id":"xO7L8AixVbF7aHvWSlYQF","type":"h3","children":[{"text":"Copy template link ","id":"HeLerQh9BxByzfDHDEOB1"}]},{"id":"YtqSjnBHrSx-Y7F71Y0e0","type":"paragraph","children":[{"text":"This will copy a link containing your referral ID and the template hash ID.  It points to this specific template at this point in time.  ","id":"HR9uydJryIjKniNZeMPgH"}]},{"id":"VpBaPDlC1Qs6mBqpYFVaj","type":"paragraph","children":[{"text":"Templates all have a unique hash after every save.  This is useful as it allows you to find a previous version if you have tracked the hash ID, but for sharing you probably want the referral link above.","id":"Ne3tYbXpeXCDEuD6oYlHl"}]},{"id":"IrvxhTyFkZMmdJc0v2vn5","type":"callout-v2","data":{"type":"info"},"children":[{"id":"G83vt170KwoCSLmRiBwnj","type":"paragraph","children":[{"text":"Remember to add a comprehensive Readme to your template if you're going to share it.  This will help users to get started easily.","id":"YVMGAXX7SMHt12FkUAtn8"}]}]},{"id":"oh7MSjyZvKSf30zQqhv2w","type":"h2","children":[{"text":"Vast Templates","id":"7qq4sW-X1hCWxQTOmajOn"}]},{"id":"jUAjzPCKxNbvK3zR4U5Gh","type":"paragraph","children":[{"text":"Templates that we have created will often be found in the ","id":"XnICzICIDnCUUYXdlQv6v"},{"text":"Recommended ","bold":true,"id":"85rVpy9k7WOHh9WWhCtUG"},{"text":"section as they go through a QA process to ensure they function correctly and provide a positive user experience.  They contain extras not found in all templates so we will address the differences here.","id":"1rQcHP_Ww3UCO4h7A0QBF"}]},{"id":"xAv9O81a2bSll2uqcK5pH","type":"h3","children":[{"text":"Instance Portal","id":"4gRd4uqQd2Cxi4QvLxArx"}]},{"id":"SwiZJXFAwC_V6XI4nXW0u","type":"paragraph","children":[{"text":"You will notice that after clicking the Open button on template running one of the Vast.ai templates a screen like this will appear.","id":"zKyIh-GM2QtPFreOSeo7F"}]},{"id":"X8FC0xge265jb4QfSyuMk","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/9WtD9F5n1L2IYJxS0qXDd/YCTX5r91x9CQYhtg34O_S_image.png","signedSrc":"","size":100,"width":1896,"height":754,"position":"center","caption":"Instance Portal","alt":"Instance portal landing page "},"children":[{"text":"","id":"aZevOkuQ5Xa-wXZE6XQDi"}]},{"id":"KyprJkmfX4Mlcj3B2FqaM","type":"paragraph","children":[{"text":"This is our ","id":"nCvVQ9wEaop0ewwJqCOk7"},{"text":"Instance Portal","bold":true,"id":"eOfcUSMKlPZCTm7p6ZdqA"},{"text":" and it provides easy to access links to services running in your instance.  It places an authentication layer in front of these services to prevent access by anyone who does not have the correct authentication token.  You are also able to create tunnels to your services without exposing ports.","id":"5avR12fxJ31D8VuysOVuZ"}]},{"id":"GaL3sypUpeLUBoTUZy_qD","type":"paragraph","children":[{"text":"Full documentation for the ","id":"5t1mAQ5d9mPATLE-nGHjg"},{"text":"Instance Portal","bold":true,"id":"iwbnQI33jr8SU39iuRe07"},{"text":" is available ","id":"z5G0X1GBYVbgg-lxoSCbt"},{"id":"zOVDbnimOtQKyE2dGdkq5","type":"link","data":{"href":"https://docs.vast.ai/instance-portal","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"here","id":"nVGypaTL5YCf-P4pzZs_1"}]},{"text":".","id":"6JRgkBD6dkdiYD7IenpkV"}]},{"id":"aaspgvIqXn9OaMJ1W9Agn","type":"h3","children":[{"text":"Provisioning Script","id":"ZZLCK7O8OMRUe7JYcuX8W"}]},{"id":"lGn8NFfUdOY4n7j__Pktt","type":"paragraph","children":[{"text":"Vast.ai templates support running a remote script on start to help configure the instance and download models and extensions that may not already be available in the Docker image.","id":"QOgaWmxYZMVY3OahsqOqU"}]},{"id":"q0NvGaxqI33R5TJzSAunq","type":"paragraph","children":[{"text":"To use this feature, simply add environment variable ","id":"qi6A94y6FY19l1HMSSvKR"},{"text":"PROVISIONING_SCRIPT","highlight":true,"id":"XChVhdhz1ajrkMWqnxWRP"},{"text":" and set the value to a plain text shell script URL.  The file will be downloaded and run on first start of the instance.","id":"vH5ecCPXPfN2oOuMTy0vL"}]},{"id":"1pK3_qiNx45aSHCnN-Uzw","type":"callout-v2","data":{"type":"info"},"children":[{"id":"86AGA-xg81FfvioqO5wH5","type":"paragraph","children":[{"text":"Use the Provisioning Script to add a twist to an existing recommended template by downloading custom models and extensions.","id":"TkIhniMg2-EdVMXatVmlZ"}]}]},{"id":"YuKTIA8cJDHlnt5Nt9DaE","type":"h3","children":[{"text":"Base Image","id":"Jjo3JgLKrf-LJYJW9JVSy"}]},{"id":"4OG1Y6hs38jLOI0LnRCrO","type":"paragraph","children":[{"text":"You can find the source code for the Docker images we used to create the Vast templates on ","id":"StziAd4uD6BH1pL8ELF80"},{"id":"NkJNRzbVE9t5Jxfs_83vL","type":"link","data":{"href":"https://github.com/vast-ai/base-image/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"GitHub","highlight":true,"id":"hfLCPErZXCpsqHlsY12GW"}]},{"text":". ","id":"isB7m0G_ASXrcOXevSXEr"}]},{"id":"_ToN6B0xMddlm25VuDu2W","type":"paragraph","children":[{"text":"These are large Docker images that contain CUDA development libraries, node + npm, OpenCL and other useful libraries.  Despite their large size you'll find they generally start quickly because they have been cached on many of the host machines.  This makes them ideal for using as a base for your own images.","id":"NWHJEx0Qs5GwOyQ0QOTEw"}]},{"id":"4p3XMb8da7rgDvemCAekq","type":"h3","children":[{"text":"Virtual Machine Templates","id":"aEsXTzXh08zDy0m3X5ran"}]},{"id":"xQVz8XWoSU1S_s5_YHQXb","type":"paragraph","children":[{"text":"Currently we offer two VM templates; A CLI and a desktop GUI.  These differ from a standard template because they launch a full virtual machine environment rather than a docker container.","id":"RbbW-cqQBZHfQqSY-wwci"}]},{"id":"eoKb3-42jpqGpxUlDEnSJ","type":"paragraph","children":[{"text":"You can edit these templates as described above, but you should not change the docker image field.  Only the images we distribue from ","id":"dTRf_TuGcV0yFYI1tfN2c"},{"text":"docker.io/vastai/kvm","highlight":true,"id":"7i8ypMHXrkO-nroOhWSvT"},{"text":" will work, but feel free to add extra environment variables and ports.","id":"CLeb2mqEsARKxjepoll6e"}]},{"id":"LLxWKHG5sm1jXNc5waDCf","type":"paragraph","children":[{"text":"Use the VM templates when you need to:","id":"aUEqBFRTe66jXwBZeYNy3"}]},{"id":"Vzo0PSSayhyIFtcZqNX24","type":"bulleted-list","children":[{"id":"jd6BogoEfDeh_CbO-Nf7u","type":"list-item","children":[{"id":"-cNrAaLfttadTXnI3AyN_","type":"list-item-child","children":[{"text":"Run applications that require namespace support","id":"CILFEwHjt-MGjsBGaTQMj"}]}]},{"id":"VsmaPIm6iI13Uut7B03xH","type":"list-item","children":[{"id":"Kb-B6XHoyX9sqgwyDg5Td","type":"list-item-child","data":null,"children":[{"text":"Run more than one Docker container in an instance","id":"7XLtgNFxT9A4-nWdySQqq"}]}]},{"id":"28ldLVs2bJG-35KKA_IPI","type":"list-item","children":[{"id":"BcyC33UkS16lhgvRB0QkN","type":"list-item-child","data":null,"children":[{"text":"Load kernel modules or run profiling jobs","id":"o9gku5tQaATDuG9MBkoJD"}]}]},{"id":"svckYZbCTWk8EZyCQqYBH","type":"list-item","children":[{"id":"WoF1Us69QEwc3JBaXvSzy","type":"list-item-child","data":null,"children":[{"text":"Mount remote drives with rclone or similar","id":"CdQFQ9tGbZnw1uUQUB-d2"}]}]}]},{"id":"yIKR4tucZ1Ms-IgvebOI8","type":"h2","children":[{"text":"Advanced Configuration","id":"UA-zzDYCu0mL6M8Qex1Eh"}]},{"id":"YItCvsv8pnl8eSu4ywxF2","type":"paragraph","children":[{"text":"For advanced template configuration options and variables, please see the ","id":"FrcXTPOFHWa5qXO_kS2-t"},{"id":"UkivnttvBrZ7VjDGvLKUy","type":"link","data":{"href":"https://docs.vast.ai/instances/docker-execution-environment","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Docker Execution Environment ","id":"431RIC2k1VIt-2m4CJZVX"}]},{"text":"guide.","id":"7vKzl9kkNYJi_AUgR8HVE"}]},{"id":"VQNNBId61YO-p4ehZdmnK","type":"paragraph","children":[{"text":"","id":"NZaJFVuqsJHYZ0rSoM79T"}]}],"metadata":{"type":"doc","version":"v2","convertedDate":"2025-05-12T11:10:10.570Z"}},"version":2409,"privacy":"shared with team","shareableToken":"x4ixozdP9OvGkTJ8byZv3","tags":[],"docTags":[],"children":[],"hasDraft":false,"createdByUserId":"zS_1kv8ITuDzgH4-KTdjJ","createdBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"lastModifiedByUserId":"WL2az7o_8HmPbkHvSjU-Q","lastModifiedBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"contributorsDetails":[],"watchers":[],"isArchbeeBrandVisible":false,"customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","updatedAt":"2025-06-13T17:13:44.783Z","createdAt":"2025-05-12T11:10:10.578Z","deletedAt":null,"editable":false,"expanded":true,"reusableContentVariables":[{"contentVariableId":"q3exSma0JEJZI5e1dH_V6","name":"Worker_Groups","content":"Worker Groups","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"UP_Tl2jcgO5gOIMbrO-GS","name":"Endpoints","content":"Endpoints","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"5aHM10OFigKWTwHuB3fMP","name":"PyWorker","content":"PyWorker","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The Vast PyWorker is a Python web server designed to run alongside a machine learning model instance, providing autoscaler compatibility."},{"contentVariableId":"4t0syUbNAbAxpMhfF1SS9","name":"Worker_Group","content":"Worker Group","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"07Mp-kz9OjvJv1gj7w4H2","name":"Endpoint","content":"Endpoint","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"sP383XCx12brqPeq_8qVA","name":"min_cold_workers","content":"min_cold_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The minimum number of workers you want to keep \"cold\" (meaning stopped and fully loaded) when your group has no load."},{"contentVariableId":"ud8V8Q4s-JoB5vW8wVEJS","name":"max_workers","content":"max_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The maximum number of workers your router group can have."}],"reusableContentDocRefs":{},"externalSync":"","contentFromExternalLink":"","leftDoc":{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","isCategory":false,"categoryName":"4-cli","children":[],"expanded":false,"name":"CLI","urlKey":"cli","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},"rightDoc":{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}},"lastPublishedToPreview":"2025-07-08T00:23:45.674Z","lastPublishedToProduction":"2025-07-08T19:41:39.988Z","customQAndAModalDescription":"","customEndingSignature":"","customForgotPasswordMessage":"","customNewAccountMessage":"","customEmailSenderName":"","autoPublishInterval":null,"autoPublishLastRun":null,"autoPublishEnabled":false,"isPublicSubscribeEnabled":false,"isCategoryPageDataEnabled":false,"portalId":"lOjtkBF-PmAqJkTBobhAY","showFullTitleInLeftNavDocsTree":false},"isHosted":true,"isMobile":false,"headerIncludes":"\u003cscript type=\"text/javascript\"\u003e\nwindow.$crisp=[];\nwindow.CRISP_WEBSITE_ID=\"734d7b1a-86fc-470d-b60a-f6d4840573ae\";\n\n// Set up the ready trigger before loading Crisp\nwindow.CRISP_READY_TRIGGER = function() {\n    // Set current page URL as session data\n    $crisp.push([\"set\", \"session:data\", [[\n        \"current_page\", window.location.href\n    ]]]);\n};\n\n(function(){\n    d=document;\n    s=d.createElement(\"script\");\n    s.src=\"https://client.crisp.chat/l.js\";\n    s.async=1;\n    d.getElementsByTagName(\"head\")[0].appendChild(s);\n})();\n\n// Also track URL changes if you have a single-page application\nwindow.addEventListener('popstate', function() {\n    if (window.$crisp.is(\"website:available\")) {\n        $crisp.push([\"set\", \"session:data\", [[\n            \"current_page\", window.location.href\n        ]]]);\n    }\n});\n\u003c/script\u003e\n\n\u003c!-- Google tag (gtag.js) --\u003e\n\u003cscript async src=\"https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG\"\u003e\u003c/script\u003e\n\u003cscript\u003e\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-DG15WC8WXG');\n\u003c/script\u003e","passwordChangeToken":"","jwt":"","reloadAuth":false,"isBot":true,"isWidget":false,"template":"","docs":[],"search":""}},"page":"/public/[[...slug]]","query":{"slug":["","%2Ftemplates"],"hostname":"docs.vast.ai","isHosted":"true","pdfExport":"","paginationFromPdf":"","paginationToPdf":"","paginationLimitPdf":"","showToC":"","shareableToken":"","passwordChangeToken":"","search":"","template":"","jwt":"","isWidget":"false","tabNav":"","reload":"false"},"buildId":"dBe88brPNkUEoyQSYuIle","assetPrefix":"https://cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2","runtimeConfig":{"NEXT_PUBLIC_ENV_ID":"app.archbee.com","NEXT_PUBLIC_ENV":"live","NEXT_PUBLIC_DOMAIN":"app.archbee.com","NEXT_PUBLIC_ASSET_PREFIX":"https://cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2","NEXT_PUBLIC_VERSION":"96c7914b49966e400b4f53ac904fa80945d640a2","NEXT_PUBLIC_BUILD_DATE":"2025-07-11T11:21:54.699Z","NEXT_RTS_DOMAIN":"rts.archbee.com"},"isFallback":false,"isExperimentalCompile":false,"dynamicIds":[5810,79658,2541],"gip":true,"scriptLoader":[]}</script><script defer src="../external.html?link=https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"95e06220e52216ca","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"cd97f360f847483b94d308aff2d8fe85"}' crossorigin="anonymous"></script>
</body>
<!-- Mirrored from docs.vast.ai/templates by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 12 Jul 2025 12:01:16 GMT -->
</html>