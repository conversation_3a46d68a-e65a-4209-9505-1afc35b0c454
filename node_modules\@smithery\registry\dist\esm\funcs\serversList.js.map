{"version": 3, "file": "serversList.js", "sourceRoot": "", "sources": ["../../../src/funcs/serversList.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,GAAG,EAAE,MAAM,eAAe,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,KAAK,CAAC,MAAM,oBAAoB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAClD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,oBAAoB,CAAC;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAS3C,OAAO,KAAK,MAAM,MAAM,2BAA2B,CAAC;AAEpD,OAAO,KAAK,UAAU,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAW,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,EACL,kBAAkB,EAClB,YAAY,GAGb,MAAM,wBAAwB,CAAC;AAEhC;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CACzB,MAA4B,EAC5B,OAAsC,EACtC,OAAwB;IAkBxB,OAAO,IAAI,UAAU,CAAC,GAAG,CACvB,MAAM,EACN,OAAO,EACP,OAAO,CACR,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,GAAG,CAChB,MAA4B,EAC5B,OAAsC,EACtC,OAAwB;IAqBxB,MAAM,MAAM,GAAG,SAAS,CACtB,OAAO,EACP,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,iCAAiC,CAAC,KAAK,CAAC,KAAK,CAAC,EACpE,yBAAyB,CAC1B,CAAC;IACF,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QACf,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;IACvD,CAAC;IACD,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC;IAC7B,MAAM,IAAI,GAAG,IAAI,CAAC;IAElB,MAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;IAEtC,MAAM,KAAK,GAAG,eAAe,CAAC;QAC5B,MAAM,EAAE,OAAO,CAAC,IAAI;QACpB,UAAU,EAAE,OAAO,CAAC,QAAQ;QAC5B,GAAG,EAAE,OAAO,CAAC,CAAC;KACf,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC;QACrC,MAAM,EAAE,kBAAkB;KAC3B,CAAC,CAAC,CAAC;IAEJ,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACpE,MAAM,aAAa,GAAG,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;IACzE,MAAM,eAAe,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAE7D,MAAM,OAAO,GAAG;QACd,OAAO,EAAE,OAAO,EAAE,SAAS,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE;QACpD,WAAW,EAAE,aAAa;QAC1B,YAAY,EAAE,EAAE;QAEhB,gBAAgB,EAAE,eAAe;QAEjC,cAAc,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU;QAC1C,WAAW,EAAE,OAAO,EAAE,OAAO;eACxB,MAAM,CAAC,QAAQ,CAAC,WAAW;eAC3B;gBACD,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE;oBACP,eAAe,EAAE,GAAG;oBACpB,WAAW,EAAE,KAAK;oBAClB,QAAQ,EAAE,GAAG;oBACb,cAAc,EAAE,OAAO;iBACxB;gBACD,qBAAqB,EAAE,IAAI;aAC5B;eACE,EAAE,QAAQ,EAAE,MAAM,EAAE;QACzB,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,CAAC,KAAK,CAAC;KAC3C,CAAC;IAEF,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE;QAChD,QAAQ,EAAE,eAAe;QACzB,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,OAAO,EAAE,SAAS;QAC3B,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,KAAK;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,CAAC;KACjE,EAAE,OAAO,CAAC,CAAC;IACZ,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;QACnB,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC;IAE7B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;QACrC,OAAO;QACP,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACxC,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,UAAU,EAAE,OAAO,CAAC,UAAU;KAC/B,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7E,CAAC;IACD,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;IAEhC,MAAM,cAAc,GAAG;QACrB,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE;KAC/C,CAAC;IAEF,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAYjC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,iCAAiC,EAAE;QACxD,GAAG,EAAE,QAAQ;KACd,CAAC,EACF,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,+BAA+B,CAAC,EACtD,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,yBAAyB,CAAC,EAChD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EACb,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CACd,CAAC,QAAQ,EAAE,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC,CAAC;IAC7C,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QACf,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;gBAC5B,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,GAAG;gBACZ,QAAQ;aACT,CAAC,CAAC;IACL,CAAC;IAED,MAAM,QAAQ,GAAG,CACf,YAAqB,EAiBrB,EAAE;QACF,MAAM,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC;QAChC,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;QAC9B,CAAC;QACD,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC/C,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,OAAO,GAAG,GAAG,EAAE,CACnB,WAAW,CACT,MAAM,EACN;YACE,GAAG,OAAO;YACV,IAAI,EAAE,QAAQ;SACf,EACD,OAAO,CACR,CAAC;QAEJ,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;IACxD,CAAC,CAAC;IAEF,MAAM,IAAI,GAAG,EAAE,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;IAC7C,OAAO,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9D,MAAM,EAAE,UAAU;YAClB,OAAO,EAAE,GAAG;YACZ,QAAQ;SACT,CAAC,CAAC;AACL,CAAC"}