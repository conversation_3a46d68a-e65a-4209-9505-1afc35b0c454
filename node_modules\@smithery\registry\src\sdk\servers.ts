/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import { serversGet } from "../funcs/serversGet.js";
import { serversList } from "../funcs/serversList.js";
import { ClientSDK, RequestOptions } from "../lib/sdks.js";
import * as components from "../models/components/index.js";
import * as operations from "../models/operations/index.js";
import { unwrapAsync } from "../types/fp.js";
import { PageIterator, unwrapResultIterator } from "../types/operations.js";

export class Servers extends ClientSDK {
  /**
   * List Servers
   *
   * @remarks
   * Retrieves a paginated list of all available servers with optional filtering.
   */
  async list(
    request: operations.ListServersRequest,
    options?: RequestOptions,
  ): Promise<PageIterator<operations.ListServersResponse, { page: number }>> {
    return unwrapResultIterator(serversList(
      this,
      request,
      options,
    ));
  }

  /**
   * Get Server
   *
   * @remarks
   * Retrieves detailed information about a specific server by its qualified name.
   */
  async get(
    request: operations.GetServerRequest,
    options?: RequestOptions,
  ): Promise<components.ServerDetailResponse> {
    return unwrapAsync(serversGet(
      this,
      request,
      options,
    ));
  }
}
