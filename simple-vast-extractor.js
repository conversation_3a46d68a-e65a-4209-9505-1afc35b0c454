#!/usr/bin/env node

/**
 * Simple Vast.ai template extractor using Bright Data MCP
 */

console.log('🚀 Starting Vast.ai Template Extraction...');

// Set API token
process.env.API_TOKEN = "c2204591997072d2f0980981898439b1184b507295e49c085337bde43cf47805";

import { spawn } from 'child_process';
import fs from 'fs';

class SimpleVastExtractor {
    constructor() {
        this.results = [];
    }

    async runMCPCommand(toolName, args) {
        return new Promise((resolve, reject) => {
            console.log(`🔧 Running ${toolName} with args:`, args);
            
            const mcp = spawn('npx', ['@brightdata/mcp'], {
                env: process.env,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            const request = {
                jsonrpc: "2.0",
                id: 1,
                method: "tools/call",
                params: {
                    name: toolName,
                    arguments: args
                }
            };

            let output = '';
            let errorOutput = '';

            mcp.stdout.on('data', (data) => {
                output += data.toString();
            });

            mcp.stderr.on('data', (data) => {
                errorOutput += data.toString();
                console.log('📡 MCP:', data.toString());
            });

            mcp.on('close', (code) => {
                if (code === 0 && output) {
                    try {
                        const response = JSON.parse(output);
                        resolve(response);
                    } catch (e) {
                        resolve({ output, errorOutput });
                    }
                } else {
                    resolve({ output, errorOutput, code });
                }
            });

            mcp.stdin.write(JSON.stringify(request) + '\n');
            mcp.stdin.end();

            setTimeout(() => {
                mcp.kill();
                reject(new Error('Timeout'));
            }, 60000);
        });
    }

    async extractTemplates() {
        try {
            console.log('🌐 Step 1: Navigating to Vast.ai templates page...');
            
            // Navigate to the page
            const navResult = await this.runMCPCommand('scraping_browser_navigate', {
                url: 'https://cloud.vast.ai/templates/'
            });
            console.log('✅ Navigation result:', navResult);

            // Wait for page load
            console.log('⏳ Waiting for page to load...');
            await new Promise(resolve => setTimeout(resolve, 8000));

            console.log('📸 Step 2: Taking screenshot...');
            const screenshotResult = await this.runMCPCommand('scraping_browser_screenshot', {});
            console.log('✅ Screenshot taken');

            console.log('📝 Step 3: Getting page text...');
            const textResult = await this.runMCPCommand('scraping_browser_get_text', {});
            console.log('✅ Page text extracted');

            console.log('🔗 Step 4: Getting all links...');
            const linksResult = await this.runMCPCommand('scraping_browser_links', {});
            console.log('✅ Links extracted');

            console.log('📄 Step 5: Getting HTML content...');
            const htmlResult = await this.runMCPCommand('scraping_browser_get_html', {});
            console.log('✅ HTML extracted');

            // Try to find and click edit buttons
            console.log('✏️ Step 6: Looking for edit buttons...');
            
            const editSelectors = [
                'button[title="Edit"]',
                'button[aria-label="Edit"]',
                '.edit-btn',
                '.edit-button',
                '[data-testid="edit-button"]',
                'svg[data-icon="edit"]'
            ];

            for (let i = 0; i < editSelectors.length; i++) {
                try {
                    console.log(`🎯 Trying to click: ${editSelectors[i]}`);
                    const clickResult = await this.runMCPCommand('scraping_browser_click', {
                        selector: editSelectors[i]
                    });
                    
                    if (clickResult && !clickResult.error) {
                        console.log('✅ Successfully clicked edit button!');
                        
                        // Wait for modal to open
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        
                        // Get modal content
                        const modalText = await this.runMCPCommand('scraping_browser_get_text', {});
                        const modalHtml = await this.runMCPCommand('scraping_browser_get_html', {});
                        
                        this.results.push({
                            selector: editSelectors[i],
                            modalText: modalText,
                            modalHtml: modalHtml,
                            timestamp: new Date().toISOString()
                        });

                        // Try to close modal
                        try {
                            await this.runMCPCommand('scraping_browser_click', {
                                selector: 'button[aria-label="Close"]'
                            });
                        } catch (e) {
                            // Try escape key
                            console.log('Trying escape key to close modal...');
                        }
                        
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        break; // Found working selector
                    }
                } catch (error) {
                    console.log(`❌ Failed with selector ${editSelectors[i]}:`, error.message);
                }
            }

            // Save all results
            const finalResults = {
                extractedAt: new Date().toISOString(),
                url: 'https://cloud.vast.ai/templates/',
                navigation: navResult,
                pageText: textResult,
                pageLinks: linksResult,
                pageHtml: htmlResult,
                editButtonResults: this.results
            };

            const filename = `vast-templates-extraction-${Date.now()}.json`;
            fs.writeFileSync(filename, JSON.stringify(finalResults, null, 2));
            
            console.log(`💾 Results saved to: ${filename}`);
            console.log(`📊 Extraction completed with ${this.results.length} edit button interactions`);

            return filename;

        } catch (error) {
            console.error('❌ Error during extraction:', error.message);
            throw error;
        }
    }
}

// Run the extraction
const extractor = new SimpleVastExtractor();
extractor.extractTemplates().then((filename) => {
    console.log('✅ Extraction completed successfully!');
    console.log(`📁 Check the results in: ${filename}`);
    process.exit(0);
}).catch((error) => {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
});
