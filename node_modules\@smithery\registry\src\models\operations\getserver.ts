/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type GetServerRequest = {
  /**
   * Qualified name of the server (e.g., 'exa')
   */
  qualifiedName: string;
};

/** @internal */
export const GetServerRequest$inboundSchema: z.ZodType<
  GetServerRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  qualifiedName: z.string(),
});

/** @internal */
export type GetServerRequest$Outbound = {
  qualifiedName: string;
};

/** @internal */
export const GetServerRequest$outboundSchema: z.ZodType<
  GetServerRequest$Outbound,
  z.ZodTypeDef,
  GetServerRequest
> = z.object({
  qualifiedName: z.string(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace GetServerRequest$ {
  /** @deprecated use `GetServerRequest$inboundSchema` instead. */
  export const inboundSchema = GetServerRequest$inboundSchema;
  /** @deprecated use `GetServerRequest$outboundSchema` instead. */
  export const outboundSchema = GetServerRequest$outboundSchema;
  /** @deprecated use `GetServerRequest$Outbound` instead. */
  export type Outbound = GetServerRequest$Outbound;
}

export function getServerRequestToJSON(
  getServerRequest: GetServerRequest,
): string {
  return JSON.stringify(
    GetServerRequest$outboundSchema.parse(getServerRequest),
  );
}

export function getServerRequestFromJSON(
  jsonString: string,
): SafeParseResult<GetServerRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => GetServerRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'GetServerRequest' from JSON`,
  );
}
