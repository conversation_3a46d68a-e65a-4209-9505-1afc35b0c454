{"tool_name": "ACE-Step", "github_url": "https://github.com/ace-step/ACE-Step", "gpu_requirements": {"min_gpu": "NVIDIA RTX 3090", "min_vram": "8GB", "recommended_gpu": "NVIDIA RTX 4090", "recommended_vram": "16GB+", "gpu_required": true}, "system_requirements": {"python_version": "3.10+", "cuda_version": "CUDA 12.6", "min_ram": "16GB", "recommended_ram": "32GB+", "storage_space": "20GB+"}, "dependencies": {"key_dependencies": ["torch", "<PERSON><PERSON><PERSON>", "torchvision", "diffusers>=0.33.0", "transformers==4.50.0", "gradio", "librosa==0.11.0", "soundfile==0.13.1", "pytorch_lightning==2.5.1", "accelerate==1.6.0"], "docker_support": true}, "performance_data": {"RTX 4090": "34.48x RTF (1.74s for 1min audio)", "A100": "27.27x RTF (2.20s for 1min audio)", "RTX 3090": "12.76x RTF (4.70s for 1min audio)", "MacBook M2 Max": "2.27x RTF (26.43s for 1min audio)"}, "installation_notes": ["Requires NVIDIA GPU for optimal performance", "Windows users need triton-windows for torch.compile", "Memory optimization available with --cpu_offload flag", "Supports 19 languages with top 10 performing well", "Real-time factor (RTF) performance varies by hardware", "Docker and docker-compose support available", "ComfyUI integration available"]}