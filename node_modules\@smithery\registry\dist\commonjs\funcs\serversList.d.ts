import { SmitheryRegistryCore } from "../core.js";
import { RequestOptions } from "../lib/sdks.js";
import { APIError } from "../models/errors/apierror.js";
import { ConnectionError, InvalidRequestError, RequestAbortedError, RequestTimeoutError, UnexpectedClientError } from "../models/errors/httpclienterrors.js";
import * as errors from "../models/errors/index.js";
import { SDKValidationError } from "../models/errors/sdkvalidationerror.js";
import * as operations from "../models/operations/index.js";
import { APIPromise } from "../types/async.js";
import { Result } from "../types/fp.js";
import { PageIterator } from "../types/operations.js";
/**
 * List Servers
 *
 * @remarks
 * Retrieves a paginated list of all available servers with optional filtering.
 */
export declare function serversList(client: SmitheryRegistryCore, request: operations.ListServersRequest, options?: RequestOptions): APIPromise<PageIterator<Result<operations.ListServersResponse, errors.UnauthorizedError | errors.ServerError | APIError | SDKValidationError | UnexpectedClientError | InvalidRequestError | RequestAbortedError | RequestTimeoutError | ConnectionError>, {
    page: number;
}>>;
//# sourceMappingURL=serversList.d.ts.map