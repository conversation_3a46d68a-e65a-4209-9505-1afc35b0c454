#!/usr/bin/env node

/**
 * Test script for Bright Data MCP tools
 * This script demonstrates how to use the Bright Data MCP server programmatically
 */

import { spawn } from 'child_process';
import { config } from 'dotenv';

// Load environment variables from .env file
config();

console.log('🚀 Testing Bright Data MCP Server...');

// Check if API token is available
if (!process.env.API_TOKEN) {
    console.error('❌ API_TOKEN not found in environment variables');
    console.log('💡 Make sure to set API_TOKEN in your .env file or environment');
    process.exit(1);
}

console.log('✅ API_TOKEN found');
console.log('🔧 Starting Bright Data MCP server...');

// Start the MCP server
const mcpServer = spawn('npx', ['@brightdata/mcp'], {
    env: {
        ...process.env,
        API_TOKEN: process.env.API_TOKEN
    },
    stdio: ['pipe', 'pipe', 'pipe']
});

mcpServer.stdout.on('data', (data) => {
    console.log('📤 MCP Server:', data.toString());
});

mcpServer.stderr.on('data', (data) => {
    console.log('📥 MCP Server:', data.toString());
});

mcpServer.on('close', (code) => {
    console.log(`🔚 MCP server exited with code ${code}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down MCP server...');
    mcpServer.kill('SIGINT');
    process.exit(0);
});

console.log('✨ MCP server is running! Press Ctrl+C to stop.');
console.log('🔗 You can now connect MCP clients to this server.');
