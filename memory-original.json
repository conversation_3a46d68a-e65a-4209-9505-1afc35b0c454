{"entities": [{"name": "VastCLI", "type": "Tool", "description": "CLI tool for interacting with Vast.ai platform", "usage": "Used for managing instances, templates, and other resources"}, {"name": "VastTemplate", "type": "Configuration", "description": "Predefined configuration for launching Vast.ai instances", "fields": ["container_image", "run_script", "environment_variables", "name", "disk_space"]}, {"name": "VastAPI", "type": "Service", "endpoint": "https://console.vast.ai/api/v0/", "authentication": "Requires API key in Authorization header"}, {"name": "TemplateCreation", "type": "Process", "steps": ["Install Vast CLI", "Authenticate with API key", "Create template using API", "Verify template creation"]}, {"name": "VastCLIInstallation", "type": "Process", "commands": ["git clone https://github.com/vast-ai/vast-cli.git", "cd vast-cli", "pip install -r requirements.txt"]}, {"name": "VastAPIAuthentication", "type": "Process", "steps": ["Get API key from Vast.ai dashboard", "Store API key in ~/.vast_api_key", "CLI automatically uses this key for authentication"]}, {"name": "TemplateCreationCommand", "type": "API_Command", "method": "POST", "endpoint": "/template/", "headers": {"Authorization": "ApiKey YOUR_API_KEY", "Content-Type": "application/json"}, "body_example": {"name": "my-template", "image": "pytorch/pytorch:latest", "run_script": "python train.py", "env": {"MODEL_NAME": "resnet50", "BATCH_SIZE": "32"}}}, {"name": "TemplateUsage", "type": "Process", "command": "python3 vast.py create instance --template_id TEMPLATE_ID", "description": "Launch an instance using a previously created template"}], "relationships": [{"source": "VastCLI", "target": "VastTemplate", "type": "manages"}, {"source": "VastCLI", "target": "VastAPI", "type": "interacts_with"}, {"source": "VastCLI", "target": "VastCLIInstallation", "type": "requires"}, {"source": "VastCLI", "target": "VastAPIAuthentication", "type": "requires"}, {"source": "TemplateCreation", "target": "VastAPI", "type": "uses"}, {"source": "TemplateCreation", "target": "TemplateCreationCommand", "type": "involves"}, {"source": "VastTemplate", "target": "TemplateUsage", "type": "enables"}, {"source": "VastAPIAuthentication", "target": "TemplateCreation", "type": "precedes"}]}