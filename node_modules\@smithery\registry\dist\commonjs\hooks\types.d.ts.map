{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/hooks/types.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAEnD,MAAM,MAAM,WAAW,GAAG;IACxB,OAAO,EAAE,MAAM,GAAG,GAAG,CAAC;IACtB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAC9B,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,WAAW,EAAE,WAAW,CAAC;IACzB,gBAAgB,EAAE,aAAa,GAAG,IAAI,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAE1C,MAAM,MAAM,cAAc,GAAG;IAC3B,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC;IACpB,MAAM,EAAE,UAAU,CAAC;CACpB,CAAC;AAEF,MAAM,MAAM,0BAA0B,GAAG,WAAW,GAAG,EAAE,CAAC;AAC1D,MAAM,MAAM,oBAAoB,GAAG,WAAW,GAAG,EAAE,CAAC;AACpD,MAAM,MAAM,mBAAmB,GAAG,WAAW,GAAG,EAAE,CAAC;AACnD,MAAM,MAAM,iBAAiB,GAAG,WAAW,GAAG,EAAE,CAAC;AAEjD;;;GAGG;AACH,MAAM,WAAW,WAAW;IAC1B,OAAO,EAAE,CAAC,IAAI,EAAE,cAAc,KAAK,cAAc,CAAC;CACnD;AAED,MAAM,WAAW,uBAAuB;IACtC;;;;OAIG;IACH,mBAAmB,EAAE,CACnB,OAAO,EAAE,0BAA0B,EACnC,KAAK,EAAE,YAAY,KAChB,YAAY,CAAC;CACnB;AAED,MAAM,WAAW,iBAAiB;IAChC;;;;;OAKG;IACH,aAAa,EAAE,CACb,OAAO,EAAE,oBAAoB,EAC7B,OAAO,EAAE,OAAO,KACb,SAAS,CAAC,OAAO,CAAC,CAAC;CACzB;AAED,MAAM,WAAW,gBAAgB;IAC/B;;;;;OAKG;IACH,YAAY,EAAE,CACZ,OAAO,EAAE,mBAAmB,EAC5B,QAAQ,EAAE,QAAQ,KACf,SAAS,CAAC,QAAQ,CAAC,CAAC;CAC1B;AAED,MAAM,WAAW,cAAc;IAC7B;;;;OAIG;IACH,UAAU,EAAE,CACV,OAAO,EAAE,iBAAiB,EAC1B,QAAQ,EAAE,QAAQ,GAAG,IAAI,EACzB,KAAK,EAAE,OAAO,KACX,SAAS,CAAC;QACb,QAAQ,EAAE,QAAQ,GAAG,IAAI,CAAC;QAC1B,KAAK,EAAE,OAAO,CAAC;KAChB,CAAC,CAAC;CACJ;AAED,MAAM,WAAW,KAAK;IACpB,uEAAuE;IACvE,mBAAmB,CAAC,IAAI,EAAE,WAAW,GAAG,IAAI,CAAC;IAC7C,mFAAmF;IACnF,+BAA+B,CAAC,IAAI,EAAE,uBAAuB,GAAG,IAAI,CAAC;IACrE,2EAA2E;IAC3E,yBAAyB,CAAC,IAAI,EAAE,iBAAiB,GAAG,IAAI,CAAC;IACzD,0EAA0E;IAC1E,wBAAwB,CAAC,IAAI,EAAE,gBAAgB,GAAG,IAAI,CAAC;IACvD,wEAAwE;IACxE,sBAAsB,CAAC,IAAI,EAAE,cAAc,GAAG,IAAI,CAAC;CACpD;AAED,MAAM,MAAM,IAAI,GACZ,WAAW,GACX,uBAAuB,GACvB,iBAAiB,GACjB,gBAAgB,GAChB,cAAc,CAAC"}