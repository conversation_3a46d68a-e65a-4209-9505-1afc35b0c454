import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type ServerListItem = {
    /**
     * Unique identifier for the server
     */
    qualifiedName: string;
    /**
     * Human-readable name of the server
     */
    displayName: string;
    /**
     * Description of the server's functionality
     */
    description: string;
    /**
     * Link to Smithery server page
     */
    homepage: string;
    /**
     * Number of times the server has been used via tool calling
     */
    useCount: number;
    /**
     * Server creation timestamp
     */
    createdAt: Date;
};
/** @internal */
export declare const ServerListItem$inboundSchema: z.ZodType<ServerListItem, z.ZodTypeDef, unknown>;
/** @internal */
export type ServerListItem$Outbound = {
    qualifiedName: string;
    displayName: string;
    description: string;
    homepage: string;
    useCount: number;
    createdAt: string;
};
/** @internal */
export declare const ServerListItem$outboundSchema: z.ZodType<ServerListItem$Outbound, z.ZodTypeDef, ServerListItem>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ServerListItem$ {
    /** @deprecated use `ServerListItem$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ServerListItem, z.ZodTypeDef, unknown>;
    /** @deprecated use `ServerListItem$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ServerListItem$Outbound, z.ZodTypeDef, ServerListItem>;
    /** @deprecated use `ServerListItem$Outbound` instead. */
    type Outbound = ServerListItem$Outbound;
}
export declare function serverListItemToJSON(serverListItem: ServerListItem): string;
export declare function serverListItemFromJSON(jsonString: string): SafeParseResult<ServerListItem, SDKValidationError>;
//# sourceMappingURL=serverlistitem.d.ts.map