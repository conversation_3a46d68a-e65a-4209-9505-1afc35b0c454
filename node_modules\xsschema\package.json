{"name": "xsschema", "type": "module", "version": "0.3.0-beta.8", "description": "extra-small, Standard Schema-based alternative to typeschema.", "author": "Moeru AI", "license": "MIT", "homepage": "https://xsai.js.org", "repository": {"type": "git", "url": "git+https://github.com/moeru-ai/xsai.git", "directory": "packages-top/xsschema"}, "bugs": "https://github.com/moeru-ai/xsai/issues", "keywords": ["xsai", "standard-schema", "typeschema"], "sideEffects": ["src/to-json-schema/sync.ts"], "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist"], "peerDependencies": {"@valibot/to-json-schema": "^1.0.0", "arktype": "^2.1.16", "effect": "^3.14.5", "sury": "^10.0.0-rc", "zod": "^3.25.0", "zod-to-json-schema": "^3.24.5"}, "peerDependenciesMeta": {"@valibot/to-json-schema": {"optional": true}, "arktype": {"optional": true}, "effect": {"optional": true}, "sury": {"optional": true}, "zod": {"optional": true}, "zod-to-json-schema": {"optional": true}}, "devDependencies": {"@standard-schema/spec": "^1.0.0", "@types/json-schema": "^7.0.15", "@valibot/to-json-schema": "^1.0.0", "arktype": "^2.1.16", "effect": "^3.14.5", "sury": "^10.0.0-rc.4", "valibot": "^1.0.0", "zod": "^3.25.0", "zod-to-json-schema": "^3.24.5"}, "scripts": {"build": "pkgroll", "test": "vitest run", "test:watch": "vitest"}, "main": "./dist/index.js", "types": "./dist/index.d.ts"}