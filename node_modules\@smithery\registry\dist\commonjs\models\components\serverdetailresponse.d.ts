import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import { ConnectionInfo, ConnectionInfo$Outbound } from "./connectioninfo.js";
import { Tool, Tool$Outbound } from "./tool.js";
/**
 * Information about the server's security status
 */
export type ServerDetailResponseSecurity = {
    /**
     * Whether the server has passed security checks
     */
    scanPassed?: boolean | null | undefined;
};
export type ServerDetailResponse = {
    /**
     * Qualified name of the MCP server in the format `owner/repository`
     */
    qualifiedName: string;
    /**
     * Human-readable name of the MCP server
     */
    displayName: string;
    /**
     * URL to the server's icon image
     */
    iconUrl?: string | null | undefined;
    /**
     * Whether this server is a remote server
     */
    remote?: boolean | undefined;
    /**
     * Specifies how to connect to this server
     */
    connections: Array<ConnectionInfo>;
    /**
     * Information about the server's security status
     */
    security?: ServerDetailResponseSecurity | null | undefined;
    /**
     * List of tools that this server provides
     */
    tools?: Array<Tool> | null | undefined;
};
/** @internal */
export declare const ServerDetailResponseSecurity$inboundSchema: z.ZodType<ServerDetailResponseSecurity, z.ZodTypeDef, unknown>;
/** @internal */
export type ServerDetailResponseSecurity$Outbound = {
    scanPassed?: boolean | null | undefined;
};
/** @internal */
export declare const ServerDetailResponseSecurity$outboundSchema: z.ZodType<ServerDetailResponseSecurity$Outbound, z.ZodTypeDef, ServerDetailResponseSecurity>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ServerDetailResponseSecurity$ {
    /** @deprecated use `ServerDetailResponseSecurity$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ServerDetailResponseSecurity, z.ZodTypeDef, unknown>;
    /** @deprecated use `ServerDetailResponseSecurity$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ServerDetailResponseSecurity$Outbound, z.ZodTypeDef, ServerDetailResponseSecurity>;
    /** @deprecated use `ServerDetailResponseSecurity$Outbound` instead. */
    type Outbound = ServerDetailResponseSecurity$Outbound;
}
export declare function serverDetailResponseSecurityToJSON(serverDetailResponseSecurity: ServerDetailResponseSecurity): string;
export declare function serverDetailResponseSecurityFromJSON(jsonString: string): SafeParseResult<ServerDetailResponseSecurity, SDKValidationError>;
/** @internal */
export declare const ServerDetailResponse$inboundSchema: z.ZodType<ServerDetailResponse, z.ZodTypeDef, unknown>;
/** @internal */
export type ServerDetailResponse$Outbound = {
    qualifiedName: string;
    displayName: string;
    iconUrl?: string | null | undefined;
    remote?: boolean | undefined;
    connections: Array<ConnectionInfo$Outbound>;
    security?: ServerDetailResponseSecurity$Outbound | null | undefined;
    tools?: Array<Tool$Outbound> | null | undefined;
};
/** @internal */
export declare const ServerDetailResponse$outboundSchema: z.ZodType<ServerDetailResponse$Outbound, z.ZodTypeDef, ServerDetailResponse>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace ServerDetailResponse$ {
    /** @deprecated use `ServerDetailResponse$inboundSchema` instead. */
    const inboundSchema: z.ZodType<ServerDetailResponse, z.ZodTypeDef, unknown>;
    /** @deprecated use `ServerDetailResponse$outboundSchema` instead. */
    const outboundSchema: z.ZodType<ServerDetailResponse$Outbound, z.ZodTypeDef, ServerDetailResponse>;
    /** @deprecated use `ServerDetailResponse$Outbound` instead. */
    type Outbound = ServerDetailResponse$Outbound;
}
export declare function serverDetailResponseToJSON(serverDetailResponse: ServerDetailResponse): string;
export declare function serverDetailResponseFromJSON(jsonString: string): SafeParseResult<ServerDetailResponse, SDKValidationError>;
//# sourceMappingURL=serverdetailresponse.d.ts.map