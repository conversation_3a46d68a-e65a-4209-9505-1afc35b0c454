import * as z from "zod";
/**
 * Authentication information is missing or invalid
 */
export type UnauthorizedErrorData = {
    error?: string | undefined;
};
/**
 * Authentication information is missing or invalid
 */
export declare class UnauthorizedError extends Error {
    error?: string | undefined;
    /** The original data that was passed to this error instance. */
    data$: UnauthorizedErrorData;
    constructor(err: UnauthorizedErrorData);
}
/** @internal */
export declare const UnauthorizedError$inboundSchema: z.ZodType<UnauthorizedError, z.ZodTypeDef, unknown>;
/** @internal */
export type UnauthorizedError$Outbound = {
    error?: string | undefined;
};
/** @internal */
export declare const UnauthorizedError$outboundSchema: z.ZodType<UnauthorizedError$Outbound, z.ZodTypeDef, UnauthorizedError>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace UnauthorizedError$ {
    /** @deprecated use `UnauthorizedError$inboundSchema` instead. */
    const inboundSchema: z.ZodType<UnauthorizedError, z.ZodTypeDef, unknown>;
    /** @deprecated use `UnauthorizedError$outboundSchema` instead. */
    const outboundSchema: z.ZodType<UnauthorizedError$Outbound, z.ZodTypeDef, UnauthorizedError>;
    /** @deprecated use `UnauthorizedError$Outbound` instead. */
    type Outbound = UnauthorizedError$Outbound;
}
//# sourceMappingURL=unauthorizederror.d.ts.map