/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type ServerListItem = {
  /**
   * Unique identifier for the server
   */
  qualifiedName: string;
  /**
   * Human-readable name of the server
   */
  displayName: string;
  /**
   * Description of the server's functionality
   */
  description: string;
  /**
   * Link to Smithery server page
   */
  homepage: string;
  /**
   * Number of times the server has been used via tool calling
   */
  useCount: number;
  /**
   * Server creation timestamp
   */
  createdAt: Date;
};

/** @internal */
export const ServerListItem$inboundSchema: z.ZodType<
  ServerListItem,
  z.ZodTypeDef,
  unknown
> = z.object({
  qualifiedName: z.string(),
  displayName: z.string(),
  description: z.string(),
  homepage: z.string(),
  useCount: z.number().int(),
  createdAt: z.string().datetime({ offset: true }).transform(v => new Date(v)),
});

/** @internal */
export type ServerListItem$Outbound = {
  qualifiedName: string;
  displayName: string;
  description: string;
  homepage: string;
  useCount: number;
  createdAt: string;
};

/** @internal */
export const ServerListItem$outboundSchema: z.ZodType<
  ServerListItem$Outbound,
  z.ZodTypeDef,
  ServerListItem
> = z.object({
  qualifiedName: z.string(),
  displayName: z.string(),
  description: z.string(),
  homepage: z.string(),
  useCount: z.number().int(),
  createdAt: z.date().transform(v => v.toISOString()),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ServerListItem$ {
  /** @deprecated use `ServerListItem$inboundSchema` instead. */
  export const inboundSchema = ServerListItem$inboundSchema;
  /** @deprecated use `ServerListItem$outboundSchema` instead. */
  export const outboundSchema = ServerListItem$outboundSchema;
  /** @deprecated use `ServerListItem$Outbound` instead. */
  export type Outbound = ServerListItem$Outbound;
}

export function serverListItemToJSON(serverListItem: ServerListItem): string {
  return JSON.stringify(ServerListItem$outboundSchema.parse(serverListItem));
}

export function serverListItemFromJSON(
  jsonString: string,
): SafeParseResult<ServerListItem, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ServerListItem$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ServerListItem' from JSON`,
  );
}
