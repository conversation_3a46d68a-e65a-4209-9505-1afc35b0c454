/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type Pagination = {
  /**
   * Current page number
   */
  currentPage: number;
  /**
   * Number of items per page
   */
  pageSize: number;
  /**
   * Total number of pages
   */
  totalPages: number;
  /**
   * Total number of items
   */
  totalCount: number;
};

/** @internal */
export const Pagination$inboundSchema: z.ZodType<
  Pagination,
  z.ZodTypeDef,
  unknown
> = z.object({
  currentPage: z.number().int(),
  pageSize: z.number().int(),
  totalPages: z.number().int(),
  totalCount: z.number().int(),
});

/** @internal */
export type Pagination$Outbound = {
  currentPage: number;
  pageSize: number;
  totalPages: number;
  totalCount: number;
};

/** @internal */
export const Pagination$outboundSchema: z.ZodType<
  Pagination$Outbound,
  z.ZodTypeDef,
  Pagination
> = z.object({
  currentPage: z.number().int(),
  pageSize: z.number().int(),
  totalPages: z.number().int(),
  totalCount: z.number().int(),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Pagination$ {
  /** @deprecated use `Pagination$inboundSchema` instead. */
  export const inboundSchema = Pagination$inboundSchema;
  /** @deprecated use `Pagination$outboundSchema` instead. */
  export const outboundSchema = Pagination$outboundSchema;
  /** @deprecated use `Pagination$Outbound` instead. */
  export type Outbound = Pagination$Outbound;
}

export function paginationToJSON(pagination: Pagination): string {
  return JSON.stringify(Pagination$outboundSchema.parse(pagination));
}

export function paginationFromJSON(
  jsonString: string,
): SafeParseResult<Pagination, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Pagination$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Pagination' from JSON`,
  );
}
