/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import {
  collectExtraKeys as collectExtraKeys$,
  safeParse,
} from "../../lib/schemas.js";
import { ClosedEnum } from "../../types/enums.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export const ToolType = {
  Object: "object",
} as const;
export type ToolType = ClosedEnum<typeof ToolType>;

/**
 * JSON Schema defining the required parameters for the tool
 */
export type InputSchema = {
  type: ToolType;
  properties?: { [k: string]: any } | undefined;
  additionalProperties?: { [k: string]: any };
};

export type Tool = {
  /**
   * Name of the tool
   */
  name: string;
  /**
   * Description of the tool
   */
  description?: string | null | undefined;
  /**
   * JSON Schema defining the required parameters for the tool
   */
  inputSchema: InputSchema;
};

/** @internal */
export const ToolType$inboundSchema: z.ZodNativeEnum<typeof ToolType> = z
  .nativeEnum(ToolType);

/** @internal */
export const ToolType$outboundSchema: z.ZodNativeEnum<typeof ToolType> =
  ToolType$inboundSchema;

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ToolType$ {
  /** @deprecated use `ToolType$inboundSchema` instead. */
  export const inboundSchema = ToolType$inboundSchema;
  /** @deprecated use `ToolType$outboundSchema` instead. */
  export const outboundSchema = ToolType$outboundSchema;
}

/** @internal */
export const InputSchema$inboundSchema: z.ZodType<
  InputSchema,
  z.ZodTypeDef,
  unknown
> = collectExtraKeys$(
  z.object({
    type: ToolType$inboundSchema,
    properties: z.record(z.any()).optional(),
  }).catchall(z.any()),
  "additionalProperties",
  true,
);

/** @internal */
export type InputSchema$Outbound = {
  type: string;
  properties?: { [k: string]: any } | undefined;
  [additionalProperties: string]: unknown;
};

/** @internal */
export const InputSchema$outboundSchema: z.ZodType<
  InputSchema$Outbound,
  z.ZodTypeDef,
  InputSchema
> = z.object({
  type: ToolType$outboundSchema,
  properties: z.record(z.any()).optional(),
  additionalProperties: z.record(z.any()),
}).transform((v) => {
  return {
    ...v.additionalProperties,
    ...remap$(v, {
      additionalProperties: null,
    }),
  };
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace InputSchema$ {
  /** @deprecated use `InputSchema$inboundSchema` instead. */
  export const inboundSchema = InputSchema$inboundSchema;
  /** @deprecated use `InputSchema$outboundSchema` instead. */
  export const outboundSchema = InputSchema$outboundSchema;
  /** @deprecated use `InputSchema$Outbound` instead. */
  export type Outbound = InputSchema$Outbound;
}

export function inputSchemaToJSON(inputSchema: InputSchema): string {
  return JSON.stringify(InputSchema$outboundSchema.parse(inputSchema));
}

export function inputSchemaFromJSON(
  jsonString: string,
): SafeParseResult<InputSchema, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => InputSchema$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'InputSchema' from JSON`,
  );
}

/** @internal */
export const Tool$inboundSchema: z.ZodType<Tool, z.ZodTypeDef, unknown> = z
  .object({
    name: z.string(),
    description: z.nullable(z.string()).optional(),
    inputSchema: z.lazy(() => InputSchema$inboundSchema),
  });

/** @internal */
export type Tool$Outbound = {
  name: string;
  description?: string | null | undefined;
  inputSchema: InputSchema$Outbound;
};

/** @internal */
export const Tool$outboundSchema: z.ZodType<Tool$Outbound, z.ZodTypeDef, Tool> =
  z.object({
    name: z.string(),
    description: z.nullable(z.string()).optional(),
    inputSchema: z.lazy(() => InputSchema$outboundSchema),
  });

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace Tool$ {
  /** @deprecated use `Tool$inboundSchema` instead. */
  export const inboundSchema = Tool$inboundSchema;
  /** @deprecated use `Tool$outboundSchema` instead. */
  export const outboundSchema = Tool$outboundSchema;
  /** @deprecated use `Tool$Outbound` instead. */
  export type Outbound = Tool$Outbound;
}

export function toolToJSON(tool: Tool): string {
  return JSON.stringify(Tool$outboundSchema.parse(tool));
}

export function toolFromJSON(
  jsonString: string,
): SafeParseResult<Tool, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => Tool$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'Tool' from JSON`,
  );
}
