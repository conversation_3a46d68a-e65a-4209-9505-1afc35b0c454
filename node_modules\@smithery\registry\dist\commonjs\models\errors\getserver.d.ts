import * as z from "zod";
/**
 * Server not found
 */
export type NotFoundErrorData = {
    error?: string | undefined;
};
/**
 * Server not found
 */
export declare class NotFoundError extends Error {
    error?: string | undefined;
    /** The original data that was passed to this error instance. */
    data$: NotFoundErrorData;
    constructor(err: NotFoundErrorData);
}
/** @internal */
export declare const NotFoundError$inboundSchema: z.ZodType<NotFoundError, z.ZodTypeDef, unknown>;
/** @internal */
export type NotFoundError$Outbound = {
    error?: string | undefined;
};
/** @internal */
export declare const NotFoundError$outboundSchema: z.ZodType<NotFoundError$Outbound, z.ZodTypeDef, NotFoundError>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace NotFoundError$ {
    /** @deprecated use `NotFoundError$inboundSchema` instead. */
    const inboundSchema: z.ZodType<NotFoundError, z.ZodTypeDef, unknown>;
    /** @deprecated use `NotFoundError$outboundSchema` instead. */
    const outboundSchema: z.ZodType<NotFoundError$Outbound, z.ZodTypeDef, NotFoundError>;
    /** @deprecated use `NotFoundError$Outbound` instead. */
    type Outbound = NotFoundError$Outbound;
}
//# sourceMappingURL=getserver.d.ts.map