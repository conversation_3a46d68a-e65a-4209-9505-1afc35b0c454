Introduction - API

Navigate through spaces

⌘K

Vast.ai API

Introduction

Instances

Machines

Accounts

Autoscaler

Team

Templates

Search

Volumes

Billing

CLI

Overview & quickstart

Commands

Permissions-and-authorization

Python SDK Usage

Docs powered by Archbee

Vast.ai API

# Introduction

0 min

welcome to vast ai 's api documentation our api allows you to programmatically manage gpu instances, handle machine operations, and automate your ai/ml workflow whether you're running individual gpu instances or managing a fleet of machines, our api provides comprehensive control over all vast ai platform features

Updated 29 May 2025

Did this page help you?

NEXT

attach ssh-key

Docs powered by Archbee

Docs powered by Archbee
