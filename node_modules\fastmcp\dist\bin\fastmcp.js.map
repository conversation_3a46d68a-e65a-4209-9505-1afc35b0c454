{"version": 3, "sources": ["../../src/bin/fastmcp.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { execa } from \"execa\";\nimport yargs from \"yargs\";\nimport { hideBin } from \"yargs/helpers\";\n\nawait yargs(hideBin(process.argv))\n  .scriptName(\"fastmcp\")\n  .command(\n    \"dev <file>\",\n    \"Start a development server\",\n    (yargs) => {\n      return yargs\n        .positional(\"file\", {\n          demandOption: true,\n          describe: \"The path to the server file\",\n          type: \"string\",\n        })\n\n        .option(\"watch\", {\n          alias: \"w\",\n          default: false,\n          describe: \"Watch for file changes and restart server\",\n          type: \"boolean\",\n        })\n\n        .option(\"verbose\", {\n          alias: \"v\",\n          default: false,\n          describe: \"Enable verbose logging\",\n          type: \"boolean\",\n        });\n    },\n\n    async (argv) => {\n      try {\n        const command = argv.watch\n          ? `npx @wong2/mcp-cli npx tsx --watch ${argv.file}`\n          : `npx @wong2/mcp-cli npx tsx ${argv.file}`;\n\n        if (argv.verbose) {\n          console.log(`[FastMCP] Starting server: ${command}`);\n          console.log(`[FastMCP] File: ${argv.file}`);\n          console.log(\n            `[FastMCP] Watch mode: ${argv.watch ? \"enabled\" : \"disabled\"}`,\n          );\n        }\n\n        await execa({\n          shell: true,\n          stderr: \"inherit\",\n          stdin: \"inherit\",\n          stdout: \"inherit\",\n        })`${command}`;\n      } catch (error) {\n        console.error(\n          \"[FastMCP Error] Failed to start development server:\",\n          error instanceof Error ? error.message : String(error),\n        );\n\n        if (argv.verbose && error instanceof Error && error.stack) {\n          console.error(\"[FastMCP Debug] Stack trace:\", error.stack);\n        }\n\n        process.exit(1);\n      }\n    },\n  )\n\n  .command(\n    \"inspect <file>\",\n    \"Inspect a server file\",\n    (yargs) => {\n      return yargs.positional(\"file\", {\n        demandOption: true,\n        describe: \"The path to the server file\",\n        type: \"string\",\n      });\n    },\n\n    async (argv) => {\n      try {\n        await execa({\n          stderr: \"inherit\",\n          stdout: \"inherit\",\n        })`npx @modelcontextprotocol/inspector npx tsx ${argv.file}`;\n      } catch (error) {\n        console.error(\n          \"[FastMCP Error] Failed to inspect server:\",\n          error instanceof Error ? error.message : String(error),\n        );\n\n        process.exit(1);\n      }\n    },\n  )\n\n  .command(\n    \"validate <file>\",\n    \"Validate a FastMCP server file for syntax and basic structure\",\n    (yargs) => {\n      return yargs\n        .positional(\"file\", {\n          demandOption: true,\n          describe: \"The path to the server file\",\n          type: \"string\",\n        })\n\n        .option(\"strict\", {\n          alias: \"s\",\n          default: false,\n          describe: \"Enable strict validation (type checking)\",\n          type: \"boolean\",\n        });\n    },\n\n    async (argv) => {\n      try {\n        const { existsSync } = await import(\"fs\");\n        const { resolve } = await import(\"path\");\n        const filePath = resolve(argv.file);\n\n        if (!existsSync(filePath)) {\n          console.error(`[FastMCP Error] File not found: ${filePath}`);\n          process.exit(1);\n        }\n\n        console.log(`[FastMCP] Validating server file: ${filePath}`);\n\n        const command = argv.strict\n          ? `npx tsc --noEmit --strict ${filePath}`\n          : `npx tsc --noEmit ${filePath}`;\n\n        try {\n          await execa({\n            shell: true,\n            stderr: \"pipe\",\n            stdout: \"pipe\",\n          })`${command}`;\n\n          console.log(\"[FastMCP] ✓ TypeScript compilation successful\");\n        } catch (tsError) {\n          console.error(\"[FastMCP] ✗ TypeScript compilation failed\");\n\n          if (tsError instanceof Error && \"stderr\" in tsError) {\n            console.error(tsError.stderr);\n          }\n\n          process.exit(1);\n        }\n\n        try {\n          await execa({\n            shell: true,\n            stderr: \"pipe\",\n            stdout: \"pipe\",\n          })`node -e \"\n            (async () => {\n              try {\n                const { FastMCP } = await import('fastmcp');\n                await import('file://${filePath}');\n                console.log('[FastMCP] ✓ Server structure validation passed');\n              } catch (error) {\n                console.error('[FastMCP] ✗ Server structure validation failed:', error.message);\n                process.exit(1);\n              }\n            })();\n          \"`;\n        } catch {\n          console.error(\"[FastMCP] ✗ Server structure validation failed\");\n          console.error(\"Make sure the file properly imports and uses FastMCP\");\n\n          process.exit(1);\n        }\n\n        console.log(\n          \"[FastMCP] ✓ All validations passed! Server file looks good.\",\n        );\n      } catch (error) {\n        console.error(\n          \"[FastMCP Error] Validation failed:\",\n          error instanceof Error ? error.message : String(error),\n        );\n\n        process.exit(1);\n      }\n    },\n  )\n\n  .help()\n  .parseAsync();\n"], "mappings": ";;;AAEA,SAAS,aAAa;AACtB,OAAO,WAAW;AAClB,SAAS,eAAe;AAExB,MAAM,MAAM,QAAQ,QAAQ,IAAI,CAAC,EAC9B,WAAW,SAAS,EACpB;AAAA,EACC;AAAA,EACA;AAAA,EACA,CAACA,WAAU;AACT,WAAOA,OACJ,WAAW,QAAQ;AAAA,MAClB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC,EAEA,OAAO,SAAS;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC,EAEA,OAAO,WAAW;AAAA,MACjB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACL;AAAA,EAEA,OAAO,SAAS;AACd,QAAI;AACF,YAAM,UAAU,KAAK,QACjB,sCAAsC,KAAK,IAAI,KAC/C,8BAA8B,KAAK,IAAI;AAE3C,UAAI,KAAK,SAAS;AAChB,gBAAQ,IAAI,8BAA8B,OAAO,EAAE;AACnD,gBAAQ,IAAI,mBAAmB,KAAK,IAAI,EAAE;AAC1C,gBAAQ;AAAA,UACN,yBAAyB,KAAK,QAAQ,YAAY,UAAU;AAAA,QAC9D;AAAA,MACF;AAEA,YAAM,MAAM;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC,IAAI,OAAO;AAAA,IACd,SAAS,OAAO;AACd,cAAQ;AAAA,QACN;AAAA,QACA,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,MACvD;AAEA,UAAI,KAAK,WAAW,iBAAiB,SAAS,MAAM,OAAO;AACzD,gBAAQ,MAAM,gCAAgC,MAAM,KAAK;AAAA,MAC3D;AAEA,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACF,EAEC;AAAA,EACC;AAAA,EACA;AAAA,EACA,CAACA,WAAU;AACT,WAAOA,OAAM,WAAW,QAAQ;AAAA,MAC9B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO,SAAS;AACd,QAAI;AACF,YAAM,MAAM;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,CAAC,gDAAgD,KAAK,IAAI;AAAA,IAC5D,SAAS,OAAO;AACd,cAAQ;AAAA,QACN;AAAA,QACA,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,MACvD;AAEA,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACF,EAEC;AAAA,EACC;AAAA,EACA;AAAA,EACA,CAACA,WAAU;AACT,WAAOA,OACJ,WAAW,QAAQ;AAAA,MAClB,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC,EAEA,OAAO,UAAU;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACL;AAAA,EAEA,OAAO,SAAS;AACd,QAAI;AACF,YAAM,EAAE,WAAW,IAAI,MAAM,OAAO,IAAI;AACxC,YAAM,EAAE,QAAQ,IAAI,MAAM,OAAO,MAAM;AACvC,YAAM,WAAW,QAAQ,KAAK,IAAI;AAElC,UAAI,CAAC,WAAW,QAAQ,GAAG;AACzB,gBAAQ,MAAM,mCAAmC,QAAQ,EAAE;AAC3D,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAEA,cAAQ,IAAI,qCAAqC,QAAQ,EAAE;AAE3D,YAAM,UAAU,KAAK,SACjB,6BAA6B,QAAQ,KACrC,oBAAoB,QAAQ;AAEhC,UAAI;AACF,cAAM,MAAM;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV,CAAC,IAAI,OAAO;AAEZ,gBAAQ,IAAI,oDAA+C;AAAA,MAC7D,SAAS,SAAS;AAChB,gBAAQ,MAAM,gDAA2C;AAEzD,YAAI,mBAAmB,SAAS,YAAY,SAAS;AACnD,kBAAQ,MAAM,QAAQ,MAAM;AAAA,QAC9B;AAEA,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAEA,UAAI;AACF,cAAM,MAAM;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV,CAAC;AAAA;AAAA;AAAA;AAAA,uCAI4B,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQvC,QAAQ;AACN,gBAAQ,MAAM,qDAAgD;AAC9D,gBAAQ,MAAM,sDAAsD;AAEpE,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAEA,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,cAAQ;AAAA,QACN;AAAA,QACA,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,MACvD;AAEA,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACF,EAEC,KAAK,EACL,WAAW;", "names": ["yargs"]}