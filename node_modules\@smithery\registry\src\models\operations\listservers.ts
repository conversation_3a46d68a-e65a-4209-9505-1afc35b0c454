/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { remap as remap$ } from "../../lib/primitives.js";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import * as components from "../components/index.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";

export type ListServersRequest = {
  /**
   * Search query for semantic search. Can include special filters:
   *
   * @remarks
   * - `owner:username` to filter by repository owner
   * - `repo:repository-name` to filter by repository name
   * - `is:deployed` to show only deployed servers
   * - `is:verified` to show only verified servers
   */
  q?: string | undefined;
  /**
   * Page number for pagination
   */
  page?: number | undefined;
  /**
   * Number of items per page
   */
  pageSize?: number | undefined;
};

export type ListServersResponse = {
  result: components.ServerListResponse;
};

/** @internal */
export const ListServersRequest$inboundSchema: z.ZodType<
  ListServersRequest,
  z.ZodTypeDef,
  unknown
> = z.object({
  q: z.string().optional(),
  page: z.number().int().default(1),
  pageSize: z.number().int().default(10),
});

/** @internal */
export type ListServersRequest$Outbound = {
  q?: string | undefined;
  page: number;
  pageSize: number;
};

/** @internal */
export const ListServersRequest$outboundSchema: z.ZodType<
  ListServersRequest$Outbound,
  z.ZodTypeDef,
  ListServersRequest
> = z.object({
  q: z.string().optional(),
  page: z.number().int().default(1),
  pageSize: z.number().int().default(10),
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListServersRequest$ {
  /** @deprecated use `ListServersRequest$inboundSchema` instead. */
  export const inboundSchema = ListServersRequest$inboundSchema;
  /** @deprecated use `ListServersRequest$outboundSchema` instead. */
  export const outboundSchema = ListServersRequest$outboundSchema;
  /** @deprecated use `ListServersRequest$Outbound` instead. */
  export type Outbound = ListServersRequest$Outbound;
}

export function listServersRequestToJSON(
  listServersRequest: ListServersRequest,
): string {
  return JSON.stringify(
    ListServersRequest$outboundSchema.parse(listServersRequest),
  );
}

export function listServersRequestFromJSON(
  jsonString: string,
): SafeParseResult<ListServersRequest, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListServersRequest$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListServersRequest' from JSON`,
  );
}

/** @internal */
export const ListServersResponse$inboundSchema: z.ZodType<
  ListServersResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  Result: components.ServerListResponse$inboundSchema,
}).transform((v) => {
  return remap$(v, {
    "Result": "result",
  });
});

/** @internal */
export type ListServersResponse$Outbound = {
  Result: components.ServerListResponse$Outbound;
};

/** @internal */
export const ListServersResponse$outboundSchema: z.ZodType<
  ListServersResponse$Outbound,
  z.ZodTypeDef,
  ListServersResponse
> = z.object({
  result: components.ServerListResponse$outboundSchema,
}).transform((v) => {
  return remap$(v, {
    result: "Result",
  });
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ListServersResponse$ {
  /** @deprecated use `ListServersResponse$inboundSchema` instead. */
  export const inboundSchema = ListServersResponse$inboundSchema;
  /** @deprecated use `ListServersResponse$outboundSchema` instead. */
  export const outboundSchema = ListServersResponse$outboundSchema;
  /** @deprecated use `ListServersResponse$Outbound` instead. */
  export type Outbound = ListServersResponse$Outbound;
}

export function listServersResponseToJSON(
  listServersResponse: ListServersResponse,
): string {
  return JSON.stringify(
    ListServersResponse$outboundSchema.parse(listServersResponse),
  );
}

export function listServersResponseFromJSON(
  jsonString: string,
): SafeParseResult<ListServersResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ListServersResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ListServersResponse' from JSON`,
  );
}
