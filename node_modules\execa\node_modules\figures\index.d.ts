declare const figureSet: {
	readonly tick: string;
	readonly info: string;
	readonly warning: string;
	readonly cross: string;
	readonly square: string;
	readonly squareSmall: string;
	readonly squareSmallFilled: string;
	readonly squareDarkShade: string;
	readonly squareMediumShade: string;
	readonly squareLightShade: string;
	readonly squareTop: string;
	readonly squareBottom: string;
	readonly squareLeft: string;
	readonly squareRight: string;
	readonly squareCenter: string;
	readonly circle: string;
	readonly circleFilled: string;
	readonly circleDotted: string;
	readonly circleDouble: string;
	readonly circleCircle: string;
	readonly circleCross: string;
	readonly circlePipe: string;
	readonly circleQuestionMark: string;
	readonly radioOn: string;
	readonly radioOff: string;
	readonly checkboxOn: string;
	readonly checkboxOff: string;
	readonly checkboxCircleOn: string;
	readonly checkboxCircleOff: string;
	readonly questionMarkPrefix: string;
	readonly bullet: string;
	readonly dot: string;
	readonly ellipsis: string;
	readonly pointer: string;
	readonly pointerSmall: string;
	readonly triangleUp: string;
	readonly triangleUpSmall: string;
	readonly triangleUpOutline: string;
	readonly triangleDown: string;
	readonly triangleDownSmall: string;
	readonly triangleLeft: string;
	readonly triangleLeftSmall: string;
	readonly triangleRight: string;
	readonly triangleRightSmall: string;
	readonly lozenge: string;
	readonly lozengeOutline: string;
	readonly home: string;
	readonly hamburger: string;
	readonly smiley: string;
	readonly mustache: string;
	readonly heart: string;
	readonly star: string;
	readonly play: string;
	readonly musicNote: string;
	readonly musicNoteBeamed: string;
	readonly nodejs: string;
	readonly arrowUp: string;
	readonly arrowDown: string;
	readonly arrowLeft: string;
	readonly arrowRight: string;
	readonly arrowLeftRight: string;
	readonly arrowUpDown: string;
	readonly almostEqual: string;
	readonly notEqual: string;
	readonly lessOrEqual: string;
	readonly greaterOrEqual: string;
	readonly identical: string;
	readonly infinity: string;
	readonly subscriptZero: string;
	readonly subscriptOne: string;
	readonly subscriptTwo: string;
	readonly subscriptThree: string;
	readonly subscriptFour: string;
	readonly subscriptFive: string;
	readonly subscriptSix: string;
	readonly subscriptSeven: string;
	readonly subscriptEight: string;
	readonly subscriptNine: string;
	readonly oneHalf: string;
	readonly oneThird: string;
	readonly oneQuarter: string;
	readonly oneFifth: string;
	readonly oneSixth: string;
	readonly oneSeventh: string;
	readonly oneEighth: string;
	readonly oneNinth: string;
	readonly oneTenth: string;
	readonly twoThirds: string;
	readonly twoFifths: string;
	readonly threeQuarters: string;
	readonly threeFifths: string;
	readonly threeEighths: string;
	readonly fourFifths: string;
	readonly fiveSixths: string;
	readonly fiveEighths: string;
	readonly sevenEighth: string;
	readonly line: string;
	readonly lineBold: string;
	readonly lineDouble: string;
	readonly lineDashed0: string;
	readonly lineDashed1: string;
	readonly lineDashed2: string;
	readonly lineDashed3: string;
	readonly lineDashed4: string;
	readonly lineDashed5: string;
	readonly lineDashed6: string;
	readonly lineDashed7: string;
	readonly lineDashed8: string;
	readonly lineDashed9: string;
	readonly lineDashed10: string;
	readonly lineDashed11: string;
	readonly lineDashed12: string;
	readonly lineDashed13: string;
	readonly lineDashed14: string;
	readonly lineDashed15: string;
	readonly lineVertical: string;
	readonly lineVerticalBold: string;
	readonly lineVerticalDouble: string;
	readonly lineVerticalDashed0: string;
	readonly lineVerticalDashed1: string;
	readonly lineVerticalDashed2: string;
	readonly lineVerticalDashed3: string;
	readonly lineVerticalDashed4: string;
	readonly lineVerticalDashed5: string;
	readonly lineVerticalDashed6: string;
	readonly lineVerticalDashed7: string;
	readonly lineVerticalDashed8: string;
	readonly lineVerticalDashed9: string;
	readonly lineVerticalDashed10: string;
	readonly lineVerticalDashed11: string;
	readonly lineDownLeft: string;
	readonly lineDownLeftArc: string;
	readonly lineDownBoldLeftBold: string;
	readonly lineDownBoldLeft: string;
	readonly lineDownLeftBold: string;
	readonly lineDownDoubleLeftDouble: string;
	readonly lineDownDoubleLeft: string;
	readonly lineDownLeftDouble: string;
	readonly lineDownRight: string;
	readonly lineDownRightArc: string;
	readonly lineDownBoldRightBold: string;
	readonly lineDownBoldRight: string;
	readonly lineDownRightBold: string;
	readonly lineDownDoubleRightDouble: string;
	readonly lineDownDoubleRight: string;
	readonly lineDownRightDouble: string;
	readonly lineUpLeft: string;
	readonly lineUpLeftArc: string;
	readonly lineUpBoldLeftBold: string;
	readonly lineUpBoldLeft: string;
	readonly lineUpLeftBold: string;
	readonly lineUpDoubleLeftDouble: string;
	readonly lineUpDoubleLeft: string;
	readonly lineUpLeftDouble: string;
	readonly lineUpRight: string;
	readonly lineUpRightArc: string;
	readonly lineUpBoldRightBold: string;
	readonly lineUpBoldRight: string;
	readonly lineUpRightBold: string;
	readonly lineUpDoubleRightDouble: string;
	readonly lineUpDoubleRight: string;
	readonly lineUpRightDouble: string;
	readonly lineUpDownLeft: string;
	readonly lineUpBoldDownBoldLeftBold: string;
	readonly lineUpBoldDownBoldLeft: string;
	readonly lineUpDownLeftBold: string;
	readonly lineUpBoldDownLeftBold: string;
	readonly lineUpDownBoldLeftBold: string;
	readonly lineUpDownBoldLeft: string;
	readonly lineUpBoldDownLeft: string;
	readonly lineUpDoubleDownDoubleLeftDouble: string;
	readonly lineUpDoubleDownDoubleLeft: string;
	readonly lineUpDownLeftDouble: string;
	readonly lineUpDownRight: string;
	readonly lineUpBoldDownBoldRightBold: string;
	readonly lineUpBoldDownBoldRight: string;
	readonly lineUpDownRightBold: string;
	readonly lineUpBoldDownRightBold: string;
	readonly lineUpDownBoldRightBold: string;
	readonly lineUpDownBoldRight: string;
	readonly lineUpBoldDownRight: string;
	readonly lineUpDoubleDownDoubleRightDouble: string;
	readonly lineUpDoubleDownDoubleRight: string;
	readonly lineUpDownRightDouble: string;
	readonly lineDownLeftRight: string;
	readonly lineDownBoldLeftBoldRightBold: string;
	readonly lineDownLeftBoldRightBold: string;
	readonly lineDownBoldLeftRight: string;
	readonly lineDownBoldLeftBoldRight: string;
	readonly lineDownBoldLeftRightBold: string;
	readonly lineDownLeftRightBold: string;
	readonly lineDownLeftBoldRight: string;
	readonly lineDownDoubleLeftDoubleRightDouble: string;
	readonly lineDownDoubleLeftRight: string;
	readonly lineDownLeftDoubleRightDouble: string;
	readonly lineUpLeftRight: string;
	readonly lineUpBoldLeftBoldRightBold: string;
	readonly lineUpLeftBoldRightBold: string;
	readonly lineUpBoldLeftRight: string;
	readonly lineUpBoldLeftBoldRight: string;
	readonly lineUpBoldLeftRightBold: string;
	readonly lineUpLeftRightBold: string;
	readonly lineUpLeftBoldRight: string;
	readonly lineUpDoubleLeftDoubleRightDouble: string;
	readonly lineUpDoubleLeftRight: string;
	readonly lineUpLeftDoubleRightDouble: string;
	readonly lineUpDownLeftRight: string;
	readonly lineUpBoldDownBoldLeftBoldRightBold: string;
	readonly lineUpDownBoldLeftBoldRightBold: string;
	readonly lineUpBoldDownLeftBoldRightBold: string;
	readonly lineUpBoldDownBoldLeftRightBold: string;
	readonly lineUpBoldDownBoldLeftBoldRight: string;
	readonly lineUpBoldDownLeftRight: string;
	readonly lineUpDownBoldLeftRight: string;
	readonly lineUpDownLeftBoldRight: string;
	readonly lineUpDownLeftRightBold: string;
	readonly lineUpBoldDownBoldLeftRight: string;
	readonly lineUpDownLeftBoldRightBold: string;
	readonly lineUpBoldDownLeftBoldRight: string;
	readonly lineUpBoldDownLeftRightBold: string;
	readonly lineUpDownBoldLeftBoldRight: string;
	readonly lineUpDownBoldLeftRightBold: string;
	readonly lineUpDoubleDownDoubleLeftDoubleRightDouble: string;
	readonly lineUpDoubleDownDoubleLeftRight: string;
	readonly lineUpDownLeftDoubleRightDouble: string;
	readonly lineCross: string;
	readonly lineBackslash: string;
	readonly lineSlash: string;
};

type FigureSet = typeof figureSet;

/**
Symbols to use when the terminal supports Unicode symbols.
*/
export const mainSymbols: FigureSet;

/**
Symbols to use when the terminal does not support Unicode symbols.
*/
export const fallbackSymbols: FigureSet;

/**
Symbols to use on any terminal.
*/
export default figureSet;

export type Options = {
	/**
	Whether to replace symbols with fallbacks.

	This can be set to `true` to always use fallback symbols, whether the terminal has poor Unicode support or not.

	@default `true` if the terminal has poor Unicode support
	*/
	readonly useFallback?: boolean;
};

/**
Returns the input with replaced fallback symbols if the terminal has poor Unicode support.

@param string - String where the Unicode symbols will be replaced with fallback symbols depending on the terminal.
@returns The input with replaced fallback Unicode symbols.

@example
```
import figures, {replaceSymbols} from 'figures';

console.log(replaceSymbols('✔ check'));
// On terminals with Unicode symbols:  ✔ check
// On other terminals:                 √ check

console.log(replaceSymbols('✔ check', {useFallback: true}));
// On terminals with Unicode symbols:  √ check
// On other terminals:                 √ check
```
*/
export function replaceSymbols(string: string, options?: Options): string;
