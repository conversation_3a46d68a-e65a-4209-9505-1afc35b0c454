{"name": "@brightdata/mcp", "version": "2.4.1", "description": "An MCP interface into the Bright Data toolset", "type": "module", "bin": {"@brightdata/mcp": "./server.js"}, "keywords": ["mcp", "brightdata"], "author": "Bright Data", "repository": {"type": "git", "url": "https://github.com/brightdata-com/brightdata-mcp.git"}, "license": "MIT", "dependencies": {"axios": "^1.8.4", "fastmcp": "^3.1.1", "playwright": "^1.51.1", "zod": "^3.24.2"}, "publishConfig": {"access": "public"}, "files": ["server.js", "browser_tools.js", "browser_session.js"]}