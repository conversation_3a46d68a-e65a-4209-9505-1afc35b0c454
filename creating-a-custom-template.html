<!DOCTYPE html><html lang="en">
<!-- Mirrored from docs.vast.ai/creating-a-custom-template by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 12 Jul 2025 12:01:27 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head><meta charSet="utf-8" data-next-head=""/><title data-next-head="">Creating a Custom Template - Guides</title><meta name="description" content="Guides" data-next-head=""/><meta name="image" content="" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:title" content="Creating a Custom Template - Guides" data-next-head=""/><meta name="twitter:description" content="Guides" data-next-head=""/><meta name="twitter:image" content="" data-next-head=""/><meta property="og:title" content="Creating a Custom Template - Guides" data-next-head=""/><meta property="og:type" content="product" data-next-head=""/><meta property="og:image" content="" data-next-head=""/><meta property="og:description" content="Guides" data-next-head=""/><meta name="language" content="en" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=6.0, user-scalable=1" data-next-head=""/><link rel="shortcut icon" href="../images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/K8kL9zPy3Yuuym96Ony5l_vast-social-profile-photo.png" data-next-head=""/><script type="text/javascript" data-next-head="">
window.$crisp=[];
window.CRISP_WEBSITE_ID="734d7b1a-86fc-470d-b60a-f6d4840573ae";

// Set up the ready trigger before loading Crisp
window.CRISP_READY_TRIGGER = function() {
    // Set current page URL as session data
    $crisp.push(["set", "session:data", [[
        "current_page", window.location.href
    ]]]);
};

(function(){
    d=document;
    s=d.createElement("script");
    s.src="../client.crisp.chat/l.js";
    s.async=1;
    d.getElementsByTagName("head")[0].appendChild(s);
})();

// Also track URL changes if you have a single-page application
window.addEventListener('popstate', function() {
    if (window.$crisp.is("website:available")) {
        $crisp.push(["set", "session:data", [[
            "current_page", window.location.href
        ]]]);
    }
});
</script><script async="" src="../external.html?link=https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG" data-next-head=""></script><script data-next-head="">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-DG15WC8WXG');
</script><style data-next-head="">
  /* Adjust chat button position */
  .crisp-client {
    --crisp-position-reverse: 1 !important; /* Left side positioning */
    --crisp-button-color: #4B5563 !important; /* Custom button color */
  }
  
  
  
  /* Optional: Hide chat widget on mobile */
  @media (max-width: 768px) {
    .crisp-client {
      display: none !important;
    }
  }
</style><link rel="preconnect" href="../external.html?link=https://cdn.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://cdnjs.cloudflare.com/"/><link rel="preconnect" href="../external.html?link=https://fonts.googleapis.com/"/><link rel="preconnect" href="../external.html?link=https://fonts.gstatic.com/" crossorigin=""/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="apple-mobile-web-app-capable" content="yes"/><link href="../external.html?link=https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/><link type="text/css" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/public/normalize.css"/><style data-emotion-css="1uiwhkr 1ro3qzv 1i6d6vk rbmaoo ydpz48 1dgdik1 v5vzns 1fl8vw4 u4v8xq roftna 1t9hfmb 1b94hea 9whsf3 w36zip 1ydoibe 1b4fjyx ypjd7c whnwm4 64jqxe 1tv6ror 1321q7x 1ys6oi3 1p004sg 130k67h 15slsu4 1bz6gk4 4zrcm0 1c21mnf wufc4w 1pjpc1 ggw9mt gn5mcr 1k7mldi ju1r6u 1srr18y 1tx6k1l xw1pqi dild6v 4dpz50 71vex2 j88me8 1iamho1 5knetf x4c2bn 7mn3wt p1cn1o 49mvvd vxmz0c fzs3dc 7lplk8 1724ov3 mh68s5 5mgflz bkpjdf mhu4l3 hkc7xg 1lljsge jgdyep ghq01l jknmwy 1149rud 1qet1p cjepy5 blbbuw hlwo6c 1wsux5p 8k8wai wwft1u 1wbwnsm 1whh9et 8he6ch euksn3 1xi6o7y 1k5vv28 1ms681s 18h9xkc 9qukwk 16kn019 rw7xsu 4779de pnt15a yfh3wc 13qucz6 15y0cxp 10e4hpw t7putw a27kb1 tfaolw tomm9q fe43hj 1gradx1 13g7z6x u0dj5y qf3nol q8renl 17z7aj8 1g8mgcm 8vo1gy veby95 esg2bf z6hwls 1xp4b63 1of59pv ay17xk s5mzj7 1o2veex 1v3aa8h 1u0h1r2 1iu3i8p 17q9201 87h1z1 1he04i1 1os98ik 13u1zyh rn2g6o 1306ufs 16bdtp4 rxerzz qizsd 14xxzio hqfggw x7qujw 1gygdlm sn6axx ee07zz hrn9nv 1nm73v8 ryel3r 1fmx8db 1mb92vv kkia1w 1nlzxni 1d5czsu q5fwlo 6zvay4 7sluno f7ifu9 1mh5ntg ae5q20 w25jvf 1l091ft yi4an8 za4e7k 1myj5jx 1ggsefg od8u8e s33lbd 3fu96b 1ngj57b va58lw v9rovc 1sirhxu ocw36r cn6axj 1kdg4v8 1epjdjo 1y1dhpv vyvlbq 1ea3n21 11qudoy 1nqd0y1 19zi4nq 9pg19u 1i7f6np j2z86 1bgl7dd gv9yrh 1b3nikh 1u12gcq 1d8xnr6 tkabts 1p6ltz1 1uprvsl 12n8vte 77goi7 188wvs8 6q3mca 63d9l0 11263bm gie19p l81hax 13dgy09 18yrr1h jjsjcg frjp74 c2lo6u 1cjyxhb 1pe94ip 14jremo 1vcs6o7 3b4wvg 1ink54o jvtmgo 1ekr0xj l75tm1 1bteluw 1ezqz7u 1cjgca1 am5scv 1nsqb76 1v9g95b 1wsgm2l eqvarw 19a7cd6 6va3et 1ecznfq 1u0lf8v 7iqawo zfdg47 wkw5oq 11tke5i zj4zjz a6hlge s2lg7y ppe5mx 1xt6kub mcv8nk uixzdi 1ob8ogp pm8ktx a2aim 16290om 1r7wx12 3kfn3n re0pe 1lwg67g 5z0b09 1kaono8 3802nw nfhqnj 1yku8fa 1jcnz6r 4hl7c0 qxkkhs xpp5q4 1u5t5yx 1uedzoz vkd4 wxglv8 6sjddz 1a0ztic q247p8 g8bmer 15fibn8 w46nsu 12f3pwh 14elb1x 4wxci8 ttmdd3 jl0nxc 13dzzt 1izd2ee 1mp63us jk60lu h3mayo 11hpfbx tplma3 5b0gu9 ub2tfp 95er94 1xcrlpv 1gpbixi 1mrqrk6 ffvq9h na5ygo 1hjn58v o1kdlj 1nwilma esdacs 1fmuwhn 14se2g5 cekyvf 63ihqe 1uye9f0 1jzmuxk 18wotnf euad1m sr2yzv 1qy29vz gy1ky8 lty3ak 1fnanxh ao6spu 1laga1l 1apyou2 11a4vci fd4syp kx3ph9 uiowgc 2ooi2b 1bahjjh 1ipukr5 18pr5mp 135mz3m 1amh8dt o7bkb1 w6crv0 xbugmj 1dlirv8 1beh7e2 bmwl98 1gbzpq5 1e705dl nzt13c 1fy1f3j 1xwlzvm yh5qkw">:root{--smokey-grey:#d1d5db;--blue:#2166ae;--dark-blue:#0C121D;--red:#E95C5C;--grey:#0C121D;--light-grey:#9ca3af;--very-light-grey:#d1d5db;--smokey-grey-background:#f3f4f6;--line-grey:#d1d5db;}.graphiql-container .topBar{height:auto!important;}.doc-explorer-back{overflow:hidden;}.doc-explorer-title{overflow:hidden;}@-webkit-keyframes animation-1i6d6vk{0%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0.7);}70%{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);transform:scale(1);box-shadow:0 0 0 10px rgba(255, 82, 82, 0);}100%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0);}}@keyframes animation-1i6d6vk{0%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0.7);}70%{-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);transform:scale(1);box-shadow:0 0 0 10px rgba(255, 82, 82, 0);}100%{-webkit-transform:scale(0.95);-moz-transform:scale(0.95);-ms-transform:scale(0.95);transform:scale(0.95);box-shadow:0 0 0 0 rgba(255, 82, 82, 0);}}:root{--ab-accent-color:#2166ae;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}.css-ydpz48{letter-spacing:-0.1rem;}.css-1dgdik1{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);}.css-v5vzns:hover{stroke:#2166ae;}.css-1fl8vw4{color:#ffffff;background-color:#2166ae!important;}.css-1fl8vw4:hover{background-color:#194d83!important;}.css-u4v8xq{height:inherit;max-width:inherit;}.css-roftna{border-color:transparent;}.css-1t9hfmb{border-color:#2166ae;}.css-1t9hfmb:hover{background-color:#eff6fd;}.css-1b94hea{margin-top:14px;margin-bottom:14px;font-weight:600;}.css-9whsf3{max-width:100%;}.css-w36zip{margin-top:28px;margin-bottom:14px;font-weight:600;}.css-1ydoibe{max-width:calc(100% - 18px);}.css-1b4fjyx{color:#2166ae!important;}.css-ypjd7c{-webkit-transition:border 200ms ease-out;transition:border 200ms ease-out;}.css-ypjd7c:hover{color:#2166ae;}.css-ypjd7c:hover .navigation-arrow{stroke:#2166ae;}.css-whnwm4{margin-top:14px;margin-bottom:14px;font-weight:400;}:root{--ab-accent-color:#e88004;--ab-bg-color:#fbe7d2;--ab-hover-bg-color:#fdf4eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006699;--ab-bg-color:#cbf0ff;--ab-hover-bg-color:#e4f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6a4ee1;--ab-bg-color:#e8e8f9;--ab-hover-bg-color:#f4f4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#F2F7FD;--ab-hover-bg-color:#eef4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4750EF;--ab-bg-color:#C9D8FD;--ab-hover-bg-color:#f1f4fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7950F2;--ab-bg-color:#F3F0FF;--ab-hover-bg-color:#f7f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1ecbb6;--ab-bg-color:#eaf5f5;--ab-hover-bg-color:#edf7f7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#664FEE;--ab-bg-color:#664fee1e;--ab-hover-bg-color:rgba(244,244,254,0.12);--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#1463ff;--ab-bg-color:#e6eeff;--ab-hover-bg-color:#f0f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6e01fa;--ab-bg-color:#f3e8fe;--ab-hover-bg-color:#f8f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#62c7c8;--ab-bg-color:#daefef;--ab-hover-bg-color:#eaf6f6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#003655;--ab-bg-color:#d2f0ff;--ab-hover-bg-color:#e6f8ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#155d84;--ab-bg-color:#ddedfb;--ab-hover-bg-color:#f1f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#180459;--ab-bg-color:#d5d0ef;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2b2b2b;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ca1289;--ab-bg-color:#fce4f2;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5c98ce;--ab-bg-color:#e4ecf4;--ab-hover-bg-color:#f0f4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7e5efd;--ab-bg-color:#eeeafd;--ab-hover-bg-color:#f6f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b98ce;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#116530;--ab-bg-color:#c0f7d5;--ab-hover-bg-color:#defbe7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#911211;--ab-bg-color:#c3a0a2;--ab-hover-bg-color:#fbf5f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#7d40cd;--ab-bg-color:#d2bee4;--ab-hover-bg-color:#f8f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#EA1D25;--ab-bg-color:#fce5e5;--ab-hover-bg-color:#fef4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0051a4;--ab-bg-color:#dbedff;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#35b234;--ab-bg-color:#ddf1dd;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1f2fa3;--ab-bg-color:#e7eafc;--ab-hover-bg-color:#f5f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c91589;--ab-bg-color:#fce4f0;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c8252c;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de23fc;--ab-bg-color:#fce6fc;--ab-hover-bg-color:#fdf0fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5b36f5;--ab-bg-color:#ebeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#543af4;--ab-bg-color:#e9e9fd;--ab-hover-bg-color:#f3f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6921ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#16a1e1;--ab-bg-color:#d1f0fa;--ab-hover-bg-color:#e5f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f47c6a;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000116;--ab-bg-color:#e8e9ff;--ab-hover-bg-color:#f2f3ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#192d89;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#1f9cff;--ab-hover-bg-color:#ecf6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6262F5;--ab-bg-color:#e9e9fd;--ab-hover-bg-color:#f3f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1463ff;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6550ff;--ab-bg-color:#edebff;--ab-hover-bg-color:#f6f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c79817;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4D87E2;--ab-bg-color:#e6ecfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4F2ba7;--ab-bg-color:#311c6b;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#27ae60;--ab-bg-color:#c6f5de;--ab-hover-bg-color:#defbed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#93c741;--ab-bg-color:#e4efd9;--ab-hover-bg-color:#eff6e9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4a61e0;--ab-bg-color:#eaedfd;--ab-hover-bg-color:#f4f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9649b6;--ab-bg-color:#efe9f6;--ab-hover-bg-color:#f8f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4ab423;--ab-bg-color:#c9f7ba;--ab-hover-bg-color:#e5fbdd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5145bf;--ab-bg-color:#e9e9f6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0069ff;--ab-bg-color:#dcedff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ae8221;--ab-bg-color:#f9eac7;--ab-hover-bg-color:#fcf4e0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#328af6;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070740;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#96c534;--ab-bg-color:#e3efc8;--ab-hover-bg-color:#f1f6e4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00c0ff;--ab-bg-color:#cdf0ff;--ab-hover-bg-color:#e6f8ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#01d093;--ab-bg-color:#91fede;--ab-hover-bg-color:#cdfef0;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5056ea;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f2a808;--ab-bg-color:#f8ebc1;--ab-hover-bg-color:#fbf4df;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1f9387;--ab-bg-color:#c1f5ea;--ab-hover-bg-color:#dbfbf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#13b0fc;--ab-bg-color:#d1f1fc;--ab-hover-bg-color:#e5f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0050a0;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8b6dff;--ab-bg-color:#efeaff;--ab-hover-bg-color:#f7f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#402e96;--ab-bg-color:#ebebf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0092;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e1676a;--ab-bg-color:#fce8e8;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#38645a;--ab-bg-color:#F4EFE9;--ab-hover-bg-color:#f8f4ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#171717;--ab-bg-color:#ececec;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1b2836;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#194896;--ab-bg-color:#e5ebfc;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#001267;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f84b0b;--ab-bg-color:#fce8e2;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5646C0;--ab-bg-color:#ddf1dd;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6f00ff;--ab-bg-color:#f3e6ff;--ab-hover-bg-color:#faf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb609e;--ab-bg-color:#fefbfd;--ab-hover-bg-color:#fcf3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6228C0;--ab-bg-color:#eeebf7;--ab-hover-bg-color:#f4f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#616194;--ab-bg-color:#ececf4;--ab-hover-bg-color:#f5f5fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00A2E9;--ab-bg-color:#DBF4FF;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2459A9;--ab-bg-color:#CACBC7;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0e4683;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff361d;--ab-bg-color:#ff361d85;--ab-hover-bg-color:rgba(255,241,239,0.52);--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#199aab;--ab-bg-color:#d4f1fa;--ab-hover-bg-color:#e8f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1D7EA9;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#20A8D8;--ab-hover-bg-color:#e8f7fc;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0C121D;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4c4c4c;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5bbb7b;--ab-bg-color:#dbf0db;--ab-hover-bg-color:#ebf7eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae6a;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#202020;--ab-bg-color:#E50914;--ab-hover-bg-color:#fef2f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#0083b3;--ab-bg-color:#ccf0ff;--ab-hover-bg-color:#e5f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2611e7;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6239EB;--ab-bg-color:#F1EAFE;--ab-hover-bg-color:#f7f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#F4EAF6;--ab-hover-bg-color:#f8f2f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f47715;--ab-bg-color:#fae7d3;--ab-hover-bg-color:#fdf4ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#f7e8fc;--ab-hover-bg-color:#fbf2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#191847;--ab-bg-color:#ececf7;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#223f99;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#97c634;--ab-bg-color:#e4efc9;--ab-hover-bg-color:#f2f6e5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6639b7;--ab-bg-color:#ede8f5;--ab-hover-bg-color:#f6f4fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#574feb;--ab-bg-color:#eaeafd;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B9132F;--ab-bg-color:#fde9e9;--ab-hover-bg-color:#fef3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9B69FF;--ab-bg-color:#efefef;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3694fc;--ab-bg-color:#94c2fa;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0c0c0c;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff6f61;--ab-bg-color:#ffe6e3;--ab-hover-bg-color:#fff4f2;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#45ada0;--ab-bg-color:#d9efef;--ab-hover-bg-color:#edf7f7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e41689;--ab-bg-color:#fce4f0;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4bc2fa;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FF6E00;--ab-bg-color:#E6E6E6;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1e4467;--ab-bg-color:#e5edf4;--ab-hover-bg-color:#f1f5f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4360a4;--ab-bg-color:#e9e9f6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#225d3a;--ab-bg-color:#f5f3ef;--ab-hover-bg-color:#f7f5f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0f2db3;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4f2ba7;--ab-bg-color:#311c6b;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#5d1996;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#004d47;--ab-bg-color:#ade0bc;--ab-hover-bg-color:#edf7ed;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ed3831;--ab-bg-color:#fce5e5;--ab-hover-bg-color:#fef4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2166ae;--ab-bg-color:#244481;--ab-hover-bg-color:#f2f5f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#402E96;--ab-bg-color:#F5F5FA;--ab-hover-bg-color:#F5F5FA;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc6201;--ab-bg-color:#fee6db;--ab-hover-bg-color:#fef3ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#58cd8a;--ab-bg-color:#dcf0e7;--ab-hover-bg-color:#ecf7f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#007235;--ab-bg-color:#9affca;--ab-hover-bg-color:#ccffe6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#333366;--ab-bg-color:#eaeaf6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#d45e30;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2b7fd7;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#086454;--ab-bg-color:#b5f7ec;--ab-hover-bg-color:#d8fbf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0068ac;--ab-bg-color:#d4efff;--ab-hover-bg-color:#e8f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#cc0100;--ab-bg-color:#ffe6e5;--ab-hover-bg-color:#fff5f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f84b0b;--ab-bg-color:#ffe7cd;--ab-hover-bg-color:#fff3e6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#016a4b;--ab-bg-color:#94fedf;--ab-hover-bg-color:#d0fef1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#245dc1;--ab-bg-color:#e8ebff;--ab-hover-bg-color:#f2f4ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f55cc;--ab-bg-color:#ebecf7;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3483DC;--ab-bg-color:#FFFFFF;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b5b5b5;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#01bdb1;--ab-bg-color:#121829;--ab-hover-bg-color:#f3f3fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#009e74;--ab-bg-color:#8fffde;--ab-hover-bg-color:#cbfff0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#01bdb1;--ab-bg-color:#83fef3;--ab-hover-bg-color:#c9fefa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#070A0D;--ab-bg-color:#F6F8F9;--ab-hover-bg-color:#f3f6f7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#096dd9;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8566ab;--ab-bg-color:#f1ebf7;--ab-hover-bg-color:#f6f3fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3074d9;--ab-bg-color:#e1ecfc;--ab-hover-bg-color:#f0f5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2bccbd;--ab-bg-color:#baf6e8;--ab-hover-bg-color:#ddfbf4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FA530B;--ab-bg-color:#D9D2C3;--ab-hover-bg-color:#f7f4f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#C53DD8;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a04a21;--ab-bg-color:#fce8e1;--ab-hover-bg-color:#fdf3f0;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1E8AAE;--ab-bg-color:#EDF5F7;--ab-hover-bg-color:#f1f7f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff361d;--ab-bg-color:#ffe7c3;--ab-hover-bg-color:#fff3e1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc4c02;--ab-bg-color:#fde6dd;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de281d;--ab-bg-color:#fce6e6;--ab-hover-bg-color:#fef5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0096DC;--ab-bg-color:#E5EFF7;--ab-hover-bg-color:#f1f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#45ada0;--ab-bg-color:#ececf7;--ab-hover-bg-color:#f4f4fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#121313;--ab-bg-color:#e8ebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2da295;--ab-bg-color:#d4f0ee;--ab-hover-bg-color:#ecf7f7;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6A47FF;--ab-bg-color:#F6F3FF;--ab-hover-bg-color:#F6F3FF;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8353f9;--ab-bg-color:#eee9fd;--ab-hover-bg-color:#f5f3fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#10c180;--ab-bg-color:#b9f7df;--ab-hover-bg-color:#dcfbed;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae62;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c7007d;--ab-bg-color:#ffe5f7;--ab-hover-bg-color:#fff4fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#004b87;--ab-bg-color:#dceeff;--ab-hover-bg-color:#ebf5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1e94e6;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#29cf92;--ab-bg-color:#FF0079;--ab-hover-bg-color:#fff0f8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#9ec500;--ab-bg-color:#cafc00;--ab-hover-bg-color:#e7ff84;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3b82f6;--ab-bg-color:#e5ebfc;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb6334;--ab-bg-color:#134dce;--ab-hover-bg-color:#f4f6fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#2aa3dc;--ab-bg-color:#d9effb;--ab-hover-bg-color:#edf7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2ed167;--ab-bg-color:#EBFFF4;--ab-hover-bg-color:#d2ffe5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1a73e8;--ab-bg-color:#e2ebfc;--ab-hover-bg-color:#f1f4fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f06292;--ab-bg-color:#fde9ee;--ab-hover-bg-color:#fef3f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#089488;--ab-bg-color:#ddd;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FA3803;--ab-bg-color:#fce6dd;--ab-hover-bg-color:#fdf4f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ea0e89;--ab-bg-color:#fddaeb;--ab-hover-bg-color:#fef3f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000116;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0745de;--ab-bg-color:#f9f9f9;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#24cca7;--ab-bg-color:#b6f7e7;--ab-hover-bg-color:#d9fbf2;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#87ca29;--ab-bg-color:#d4f5b2;--ab-hover-bg-color:#e8fad5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#263B2C;--ab-bg-color:#e1ede7;--ab-hover-bg-color:#f0f7f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fc3229;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f5a400;--ab-bg-color:#ffe9b4;--ab-hover-bg-color:#fff3d7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff6600;--ab-bg-color:#ffe7d7;--ab-hover-bg-color:#fff3eb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1568E4;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0b382b;--ab-bg-color:#c6f4e7;--ab-hover-bg-color:#defaf3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0079c1;--ab-bg-color:#d0f1ff;--ab-hover-bg-color:#e4f7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#FF5000;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#625DEC;--ab-bg-color:#EFEFFD;--ab-hover-bg-color:#f4f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#b7b7b7;--ab-bg-color:#1100A7;--ab-hover-bg-color:#f8f7ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#489be5;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#657220;--ab-bg-color:#D9F400;--ab-hover-bg-color:#eeff4f;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4da0ff;--ab-bg-color:#deefff;--ab-hover-bg-color:#edf6ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5fa846;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4a0ea3;--ab-bg-color:#f0ecfd;--ab-hover-bg-color:#f8f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e55775;--ab-bg-color:#fce5eb;--ab-hover-bg-color:#fef4f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#339af0;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3E95D1;--ab-bg-color:#deecf9;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#5D29FF;--ab-bg-color:#BDA7FF;--ab-hover-bg-color:#f6f2ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21ae72;--ab-bg-color:#bdf8db;--ab-hover-bg-color:#dbfbeb;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6eb3f3;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff0000;--ab-bg-color:#ffe6e6;--ab-hover-bg-color:#fff5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#142b3a;--ab-bg-color:#e6edf4;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#160a70;--ab-bg-color:#ededfd;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#214777;--ab-bg-color:#bdd3ef;--ab-hover-bg-color:#f1f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1c48d9;--ab-bg-color:#e6ebfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2366ad;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2350af;--ab-bg-color:#e6ecfc;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1563ff;--ab-bg-color:#e2ebff;--ab-hover-bg-color:#f1f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0bb599;--ab-bg-color:#b7f7ee;--ab-hover-bg-color:#dafbf5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#21aea1;--ab-bg-color:#e3fdf8;--ab-hover-bg-color:#d9fdf6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f98858;--ab-bg-color:#fbe6df;--ab-hover-bg-color:#fdf2ee;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#B91C1C;--ab-bg-color:#F3F4F6;--ab-hover-bg-color:#F3F4F6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e6007e;--ab-bg-color:#ffe6f3;--ab-hover-bg-color:#fff0f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2B8E94;--ab-bg-color:#f4f8fb;--ab-hover-bg-color:#f0f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff93c1;--ab-bg-color:#ffe3f1;--ab-hover-bg-color:#fff2f9;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0772ec;--ab-bg-color:#72ffff;--ab-hover-bg-color:#c2ffff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#33285d;--ab-bg-color:#eaeaf6;--ab-hover-bg-color:#f5f5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#550055;--ab-bg-color:#ffe1ff;--ab-hover-bg-color:#fff0ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8FC640;--ab-bg-color:#cccccc;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00B373;--ab-bg-color:#F9FAE4;--ab-hover-bg-color:#f6f7d8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#789904;--ab-bg-color:#C4D600;--ab-hover-bg-color:#eeff4f;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb6334;--ab-bg-color:#fbe6de;--ab-hover-bg-color:#fef5f2;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#74b01a;--ab-bg-color:#d1f6ac;--ab-hover-bg-color:#e8fad4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#a23aff;--ab-bg-color:#f4e9ff;--ab-hover-bg-color:#f9f3ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1100A7;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#226da7;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#268ff9;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2ec4ff;--ab-bg-color:#cef1ff;--ab-hover-bg-color:#e7f8ff;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2485cc;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#39a730;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#278b9b;--ab-bg-color:#d6f0f5;--ab-hover-bg-color:#eaf7fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4692c0;--ab-bg-color:#e6edf4;--ab-hover-bg-color:#f2f6f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f28e00;--ab-bg-color:#ffe7ca;--ab-hover-bg-color:#fff4e8;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#212121;--ab-bg-color:#eaeaea;--ab-hover-bg-color:#f6f6f6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ffa200;--ab-bg-color:#ffe9b4;--ab-hover-bg-color:#fff3d7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#9146ED;--ab-bg-color:#e7e513;--ab-hover-bg-color:#f8f8bd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#14467a;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#191a1a;--ab-bg-color:#ebebeb;--ab-hover-bg-color:#f4f4f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#911211;--ab-bg-color:#cd1b18;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#3a4958;--ab-bg-color:#e7ecf0;--ab-hover-bg-color:#f3f6f8;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3c9a35;--ab-bg-color:#ddf0dc;--ab-hover-bg-color:#ecf7ec;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#18181b;--ab-bg-color:#abdeed;--ab-hover-bg-color:#e7f7fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3bb76b;--ab-bg-color:#dbf0e6;--ab-hover-bg-color:#ebf7f1;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1eaf9e;--ab-bg-color:#b9f7e8;--ab-hover-bg-color:#dcfbf4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#008179;--ab-bg-color:#77fff7;--ab-hover-bg-color:#c7fffa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#365742;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2365a9;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#393a3c;--ab-bg-color:#ececec;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f0b40;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#006d00;--ab-bg-color:#a9ffa9;--ab-hover-bg-color:#d6ffd6;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#e51743;--ab-bg-color:#fce6e9;--ab-hover-bg-color:#fef5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#38616f;--ab-bg-color:#e3ebf3;--ab-hover-bg-color:#f3f6fa;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f6922e;--ab-bg-color:#fae7d3;--ab-hover-bg-color:#fdf4ec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1d4354;--ab-bg-color:#e4edf4;--ab-hover-bg-color:#f0f4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#00255f;--ab-bg-color:#e1ecff;--ab-hover-bg-color:#f0f5ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#25cd93;--ab-bg-color:#bcf8e0;--ab-hover-bg-color:#dffbee;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#de1a1c;--ab-bg-color:#fce7e7;--ab-hover-bg-color:#fdf1f1;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6421ae;--ab-bg-color:#f3eafd;--ab-hover-bg-color:#f9f4fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#56296f;--ab-bg-color:#5a96f0;--ab-hover-bg-color:#f5f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6e69f3;--ab-bg-color:#ebebfd;--ab-hover-bg-color:#f5f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#6c26db;--ab-bg-color:#ede8fc;--ab-hover-bg-color:#f5f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#8b5cf7;--ab-bg-color:#ede8fc;--ab-hover-bg-color:#f5f2fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#005eb8;--ab-bg-color:#dbedff;--ab-hover-bg-color:#eff7ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#0e71cc;--ab-bg-color:#deedfb;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fe5d27;--ab-bg-color:#fee6db;--ab-hover-bg-color:#fef3ef;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2179e2;--ab-bg-color:#e4eefc;--ab-hover-bg-color:#f3f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#f1602b;--ab-bg-color:#fbe6df;--ab-hover-bg-color:#fdf2ee;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1cce86;--ab-bg-color:#bef8db;--ab-hover-bg-color:#dcfbec;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#04c9b5;--ab-bg-color:#2c2c2c;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#007991;--ab-bg-color:#b4f5ff;--ab-hover-bg-color:#d7fbff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#419BF9;--ab-bg-color:#dcecfb;--ab-hover-bg-color:#f0f7fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#eb609e;--ab-bg-color:#FEFBFD;--ab-hover-bg-color:#fcf3f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2A9D8F;--ab-bg-color:#efefef;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3f2aa3;--ab-bg-color:#eeecf9;--ab-hover-bg-color:#f5f4fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#2D2836;--ab-bg-color:#FBF9FE;--ab-hover-bg-color:#f5f1fc;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#c490d1;--ab-bg-color:#f0e8f5;--ab-hover-bg-color:#f7f4fa;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#3E3EF9;--ab-bg-color:#f2f6ff;--ab-hover-bg-color:#edf2ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#14467a;--ab-bg-color:#dfedfb;--ab-hover-bg-color:#eef6fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#4f90d3;--ab-bg-color:#e3eef8;--ab-hover-bg-color:#eff5fb;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#000000;--ab-bg-color:#002BFF;--ab-hover-bg-color:#f5f6ff;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#1d1fa0;--ab-bg-color:#ededfd;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ff4451;--ab-bg-color:#ffe4e7;--ab-hover-bg-color:#fff3f4;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#ae217e;--ab-bg-color:#fce5f2;--ab-hover-bg-color:#fef4f9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#96c534;--ab-bg-color:#394915;--ab-hover-bg-color:#f1f6e4;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#21ae35;--ab-bg-color:#bdf8cc;--ab-hover-bg-color:#e0fce7;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#03111f;--ab-bg-color:#deedfc;--ab-hover-bg-color:#edf5fd;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#1b6aee;--ab-bg-color:#e8edfc;--ab-hover-bg-color:#f2f5fe;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#55a51c;--ab-bg-color:#d0f6b1;--ab-hover-bg-color:#e8fbd9;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#F8922F;--ab-bg-color:#ddd;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#68aaef;--ab-bg-color:#e0eefc;--ab-hover-bg-color:#eff6fd;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#fda22f;--ab-bg-color:#02012D;--ab-hover-bg-color:#f7f7fe;--ab-text-to-accent-color:#0C121D;--ab-text-to-bg-color:#ffffff;}:root{--ab-accent-color:#305079;--ab-bg-color:#ffffff;--ab-hover-bg-color:#f3f3f3;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}:root{--ab-accent-color:#141414;--ab-bg-color:#ececec;--ab-hover-bg-color:#f5f5f5;--ab-text-to-accent-color:#ffffff;--ab-text-to-bg-color:#0C121D;}</style><link rel="stylesheet" href="../cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"/><link rel="preload" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/css/b69acd3085e4480a.css" as="style"/><link rel="stylesheet" href="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/css/b69acd3085e4480a.css" data-n-g=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/polyfills-42372ed130431b0a.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5810.e6a0789a46dba285.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1cc2734a-a2bd46b48dcfb414.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1976-3a1e16ed39f257ff.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4747-66e20966a06d24d8.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7764-f3b14f364a0d1b52.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/416-97c1497291412984.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4225-22f657dc0f739d91.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/1539-00ecead0d9ee55cc.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/6652-fc6900588edd6271.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5176-afc4cc13abd1d35c.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9370-c81561713f82c466.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/111-6561208ec0d2be20.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7020-baaec7260f5e6fc1.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9292-f2fe7a82877d47bf.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5089.32630609a654ddd8.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5159-41323e3b79d2d278.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/509-bc3789384db0db85.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4638-8cca8ce68235216a.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5118-075b126111014c7d.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/4024-970f73672e22cd2c.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7968-7d9b753edf6c1f99.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7187-496bdd6418e0be57.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3395.9eef5e529d29ff6f.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/8992-017f87979f45ecc7.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2541.bb0abb704527903d.js"></script><script defer="" src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3156.a91f1bbd6e134c38.js"></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/webpack-91f79a938b1b1dfb.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/framework-5648639edb99e1bd.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/main-2255b1a2dca1d2dd.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/pages/_app-73995db85270ee67.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/48e0f7fa-2695100ece8812c4.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2edb282b-e1fef83e163cf249.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9a20ca01-4cbfe13ddba0b528.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/91bbf309-07ffab0a9657d31d.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/8bd53eb9-3850191f2ece3222.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3514-c52c7aad05a7e48f.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/7422-4c22c8698c393ea5.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/3424-375f0b69ace252f6.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9442-be30be087aa68cc6.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/562-dc8635dfded4d697.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/5913-96d7a578979c260a.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/6342-6d0fbb2ac6aa8697.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/2893-f98caa64b2e95191.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/9849-f5651e5e76c2f026.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/chunks/pages/public/%5b%5b...slug%5d%5d-26ef0790e35d5b2c.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/dBe88brPNkUEoyQSYuIle/_buildManifest.js" defer=""></script><script src="../cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2/_next/static/dBe88brPNkUEoyQSYuIle/_ssgManifest.js" defer=""></script><meta name="sentry-trace" content="9b5b92706915b262da3efaf58b95bc71-94a23f5f1bd00ece-1"/><meta name="baggage" content="sentry-environment=live,sentry-release=96c7914b49966e400b4f53ac904fa80945d640a2,sentry-public_key=ad82dc9f244e490fbe33eeb465274185,sentry-trace_id=9b5b92706915b262da3efaf58b95bc71,sentry-sample_rate=1,sentry-transaction=GET%20%2Fcreating-a-custom-template,sentry-sampled=true"/><style id="__jsx-2251086973">#nprogress{pointer-events:none}#nprogress .bar{background:#2166ae;background:--ab-accent-color;position:fixed;z-index:1031;top:0;left:0;width:100%;height:2px}#nprogress .peg{display:block;position:absolute;right:0px;width:100px;height:100%;-webkit-box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;-moz-box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;box-shadow:0 0 5px#0070f3,0 0 5px#0070f3;opacity:1;-webkit-transform:rotate(3deg)translate(0px,-4px);-ms-transform:rotate(3deg)translate(0px,-4px);-moz-transform:rotate(3deg)translate(0px,-4px);-o-transform:rotate(3deg)translate(0px,-4px);transform:rotate(3deg)translate(0px,-4px)}</style></head><body style="display:block"><div id="__next"><div class="h-full w-full"><link rel="dns-prefetch" href="../external.html?link=https://app.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://app.archbee.com/"/><link rel="dns-prefetch" href="../external.html?link=https://cdn.archbee.com/"/><link rel="preconnect" href="../external.html?link=https://cdn.archbee.com/"/><div class="h-full w-full" style="visibility:hidden"><div data-overlayscrollbars-initialize="" id="docs-scroll-container" class="ab-space ab-collection min-h-full min-w-full h-full dark:text-white fixed top-0 left-0 right-0 bottom-0 print:overflow-visible print:!static print:!h-auto"><div data-overlayscrollbars-contents=""><div class="ab-space-container ab-collection-container flex flex-col w-full justify-center bg-white dark:bg-gray-900"><nav role="navigation" class="ab-top-navbar flex flex-col z-20 sticky top-0 items-center bg-white/70 dark:bg-gray-900/80 border-b border-gray-100 dark:border-gray-800 css-1dgdik1" id="ab-public-nav-header"><div class="w-full mx-auto px-7"><div class="flex items-center justify-between w-full py-4"><a class="ab-public-logo ab-tab-focus flex items-center max-w-[160px] h-[48px] relative justify-start" tabindex="0" href="../external.html?link=https://vast.ai/" aria-label="website logo"><img src="../external.html?link=https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/hY3Z66NYu_wT-evx8EFi5_logo-symbol-dark.svg?format=webp&amp;width=400" class="w-full block mx-auto py-1 object-contain css-u4v8xq" alt="Website logo"/></a><div class="flex items-center print:hidden"><div class="flex items-center text-gray-400 cursor-pointer mx-4"><div class="flex items-center xl:hidden"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 cursor-pointer css-v5vzns"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-4 cursor-pointer stroke-current"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg></div><div class="flex items-center gap-2"><div class="flex items-center justify-center p-2.5 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-400 dark:text-gray-100" type="button" id="radix-:R1kijamm:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg></div></div></div></div><div class="hidden xl:flex flex-1 items-center justify-end gap-2"><a href="../external.html?link=https://cloud.vast.ai/" tabindex="0" class="ab-external-link-btn py-3 px-7 font-semibold rounded-lg text-lg btn-blue relative inline-flex items-center leading-6 css-1fl8vw4">Console</a><a href="../external.html?link=https://discord.gg/hSuEbSQ4X8" tabindex="0" class="ab-external-link-btn py-3 px-7 font-semibold rounded-lg text-lg btn ab-tab-focus text-gray-700 dark:text-gray-100 hover:text-gray-800 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 undefined&#x27;,">Discord</a></div></div><div class="no-print hidden xl:flex w-full pt-1 overflow-x-auto ab-scrollbars"><div class="ab-public-space-links-wrap flex items-center flex-1 -left-4"><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg css-1t9hfmb" role="link" tabindex="0"><div class="w-auto max-w-250 truncate font-semibold text-gray-900 dark:text-white" data-state="closed">Guides</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">Instances</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">Serverless</div></div><div class="ab-public-space-link ab-tab-focus select-none flex px-6 py-2 cursor-pointer items-center border-b-2 border-transparent rounded-t-lg hover:bg-gray-50 dark:hover:bg-gray-850 css-roftna" role="link" tabindex="0"><div class="w-auto max-w-250 truncate text-gray-700 dark:text-gray-300" data-state="closed">API</div></div></div></div></div></nav><style data-emotion="css 14yoxd">.css-14yoxd{z-index:1200;}</style><div class="ab-space-content ab-collection-content w-full mx-auto relative xl:flex xl:flex-row justify-between px-7 xl:px-0"><div class="ab-tree-navigation no-print sticky flex-col border-r border-gray-100 dark:border-gray-800 w-[360px] hidden xl:flex xl:flex-shrink-0 transition-width transition-slowest ease" style="top:100px;height:calc(100vh - 100px);max-height:calc(100vh - 100px)"><div class="ab-left-nav-public-header flex flex-col w-full px-6 xl:px-7 xl:pt-7 pb-6 !px-0"><div class="flex w-full xl:hidden ab-space-navigation mb-4"><div class="flex flex-col w-full p-6 bg-gray-200 border-b border-gray-300 dark:bg-gray-700 dark:border-gray-600 ab-space-navigation"><div class="flex justify-between font-semibold items-center text-gray-700 dark:text-gray-200"><span>Navigate through spaces</span><div class="text-gray-400 dark:text-gray-500 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="strike-current"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></svg></div></div></div></div><div role="search" class="ab-public-search flex justify-center px-6 xl:px-7"><div class="w-full flex h-11 items-center border pl-4 pr-2 rounded-lg hover:shadow-sm border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-xs"><div class="w-6 h-6 flex items-center justify-center mr-0 lg:mr-2 shrink-0 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="css-v5vzns"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg></div><input class="ab-search-input border-none bg-transparent placeholder-gray-400::placeholder w-full leading-none" placeholder="Search or ask..." value=""/><kbd class="inline-flex justify-center items-center p-2 rounded border border-gray-300 dark:border-gray-700 align-middle leading-3 cursor-default text-gray-600 dark:text-gray-400 border-none bg-gray-50 dark:bg-gray-800 font-medium mx-0 ml-1 shadow-none" style="height:22px;min-width:22px">⌘</kbd><kbd class="inline-flex justify-center items-center p-2 rounded border border-gray-300 dark:border-gray-700 align-middle cursor-default text-gray-600 dark:text-gray-400 text-xs border-none bg-gray-50 dark:bg-gray-800 font-medium mx-0 ml-1 shadow-none" style="height:22px;min-width:22px">K</kbd></div></div></div><div data-overlayscrollbars-initialize="" id="ab-left-nav-public-wrap" class="ab-left-nav-public scroll-smooth flex flex-1 flex-col items-center max-h-full xl:px-7 xl:pb-5"><div data-overlayscrollbars-contents=""><div role="navigation" class="w-full relative overflow-x-hidden px-6 xl:px-0"><div title="Overview"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-1b94hea" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Overview"><span class="truncate">Overview</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron transform rotate-90 text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a><div class="flex-1 max-w-full pl-7 ml-4"><div title="Introduction"><div href="index.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="Introduction"><span class="truncate">Introduction</span></div></a></div></div><div title="QuickStart"><div href="quickstart.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="QuickStart"><span class="truncate">QuickStart</span></div></a></div></div><div title="FAQ"><div href="faq.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="FAQ"><span class="truncate">FAQ</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div></div></div></div><div title="Use Cases"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Use Cases"><span class="truncate">Use Cases</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Teams"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Teams"><span class="truncate">Teams</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Hosting"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Hosting"><span class="truncate">Hosting</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Distributed Computing"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Distributed Computing"><span class="truncate">Distributed Computing</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Console"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Console"><span class="truncate">Console</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron stroke-current transform text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a></div><div title="Specific GPUs"><a class="ab-tree-navigation-link ab-tab-focus cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-lg text-gray-900 dark:text-white css-w36zip" role="link" tabindex="0"><div class="flex items-center css-1ydoibe" title="Specific GPUs"><span class="truncate">Specific GPUs</span></div><div class="flex flex-col" role="button" aria-label="expandable"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ab-left-nav-chevron transform rotate-90 text-gray-300 dark:text-gray-600"><polyline points="9 18 15 12 9 6"></polyline></svg></div></div></a><div class="flex-1 max-w-full pl-7 ml-4"><div title="RTX 5 Series"><div href="jDpX-XUZdy-zKWbAZQPNK.html"><a class="ab-tree-navigation-link ab-tab-focus text-lg cursor-pointer select-none flex items-center justify-between pr-0 my-4 !leading-6 ab-tree-navigation-link-inactive text-gray-600 dark:text-gray-300 css-whnwm4" role="link" tabindex="0"><div class="flex items-center css-9whsf3" title="RTX 5 Series"><span class="truncate">RTX 5 Series</span></div></a></div></div></div></div></div></div></div><div class="px-[24px] flex my-4"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-full"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div><div role="main" class="ab-center-column md:flex justify-center grow xl:px-7 xl:overflow-x-hidden"><div class="w-full max-w-[768px]" id="main-column"><div><div class="flex flex-col justify-center pt-4 xl:pt-7"><div class="flex flex-1 w-full justify-center pb-0"><div class="flex flex-initial flex-col w-full"><div class="flex items-center pl-0 pr-2 mb-5 xl:-mt-1.5"><div class="flex"><div><div class="flex text-gray-400 flex-wrap"><div class="flex"><div class="flex pr-2 text-gray-500 dark:text-gray-400 items-center font-semibold css-1b4fjyx">Use Cases</div></div></div></div></div></div><h1 class="ab-doc-name h1 font-bold text-5xl break-words w-full max-w-full mt-0 pb-0 xl:-mt-1.5 mb-0 css-ydpz48">Creating a Custom Template</h1><div class="flex gap-6 text-sm mt-5 mb-6 text-gray-400 dark:text-gray-500"><div class="flex items-center gap-1.5 flex-shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="1rem" height="1rem" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="stroke-current"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg><span>31 min</span></div></div><div><div class="max-h-dvh invisible"> how to create your own template introduction this guide will help you create custom templates for running containers on vast ai there are three approaches using provisioning script with recommended templates (simplest) create dockerfile from vast ai base images (recommended for custom images) using existing docker images (with security considerations) vast instances are currently linux docker containers and ubuntu virtual machines many linux based images should work within vast prerequisites a vast ai account ssh client installed on your local machine and ssh public key added in the ssh keys tab in the keys section of your console (optional) install and use vast cli approach 1 using provisioning script (simplest) this approach lets you customize recommended templates without building a custom image steps go to the templates tab in vast ai interface search for &quot;base image&quot; or &quot;pytorch&quot; depending on your needs vast ai/base image is a general purpose image vast ai/pytorch is a base image for working with pytorch based applications on vast click &quot;edit&quot; on your chosen template add the provisioning script environment variable in the environment variables section, add a new variable named &quot;provisioning script&quot; the value should be a url pointing to a shell script (from github, gist, etc ) if you&#x27;re specifying a shell script stored in github repo, specify url like url https //raw\ githubusercontent com/karthik vast ai/vast cli/distributed inference integration/provisioning script sh7 make sure to click &quot;+&quot; to add the environment variable click create and use example provisioning script \#!/bin/bash \# cd /workspace/ \# cause the script to exit on failure set eo pipefail \# activate the main virtual environment /venv/main/bin/activate \# install your packages pip install your packages \# download some useful files wget p &quot;${workspace}/&quot; https //example org/my application tar gz tar xvf ${workspace}/my application tar gz&quot; \# set up any additional services echo &quot;my supervisor config&quot; &gt; /etc/supervisor/conf d/my application conf echo &quot;my supervisor wrapper&quot; &gt; /opt/supervisor scripts/my application sh chmod +x /opt/supervisor scripts/my application sh \# reconfigure the instance portal rm f /etc/portal yaml export portal config=&quot;localhost 1111 11111 /\ instance portal|localhost 1234 11234 /\ my application&quot; \# reload supervisor supervisorctl reload this script will run on first boot to set up your environment all installations should go to /workspace/ for proper persistence configuring application access with portal config the base image template includes portal config for secure application access management this environment variable controls how applications are exposed and accessed portal config structure hostname\ external port\ local port\ url path\ application name|hostname\ external port\ local port\ url path\ application name the structure of this variable is each application is separated by the | character each application parameter is separated by the character each application must specify hostname\ external port\ local port\ url path\ application name example &quot;localhost 8002 18002 /hello\ myapp|localhost 1111 11111 /\ instance portal|localhost 8080 18080 /\ jupyter|localhost 8080 8080 /terminals/1\ jupyter terminal|localhost 8384 18384 /\ syncthing|localhost 6006 16006 /\ tensorboard&quot; the hostname in docker instances will always be localhost where the internal port and local port are not equal then caddy will be configured to listen on 0 0 0 0\ external port acting as a reverse proxy for hostname\ local port if the external port and local port are equal then caddy will not act as a proxy but the instance portal ui will still create links this is useful because it allows us to create links to jupyter which is not controlled by supervisor in jupyter launch mode url path will be appended to the instance address and is generally set to / but can be used to create application deep links the caddy manager script will write an equivalent config file at /etc/portal yaml on boot if it does not already exist this file can be edited in a running instance important when defining multiple links to a single application, only the first should have non equal ports we cannot proxy one application multiple times note instance portal ui is not required and its own config declaration can be removed from portal config this will not affect the authentication system approach 2 create dockerfile from vast ai base images why use vast ai base images? built in security features through caddy proxy automatic tls encryption for web services authentication token protection proper isolation between external and internal services steps start with a vast ai base image or vast ai pytorch base image in your dockerfile \#for example from vastai/base image\ cuda 12 6 3 cudnn devel ubuntu22 04 py313 \# or from vastai/pytorch 2 6 0 cuda 12 6 3 py312 \# install your applications into /opt/workspace internal/ \# this ensures files can be properly synced between instances workdir /opt/workspace internal/ \# activate virtual environment from base image run /venv/main/bin/activate run your installation commands build and push your image to a container registry create a new template using your custom image see next approach for steps on how to do that approach 3 create template using existing docker images you can use existing docker hub images, but please note running external facing services without proper security measures is not recommended consider wrapping the service in vast ai&#x27;s security infrastructure step 1 navigate to the &#x27;templates&#x27; section once logged in, click on templates to go to the template section this is where you can create, manage, and edit templates step 2 create a new template click the button + new to create a new template create step 3 specify the image path/tag of your docker image in the image path tag, you can add the path/tag of the docker image you wish to use the version tag is where you would set the version image\&amp;tag step 4 set environment variables you can use the e option to set environment variables in the docker options field by adding something like e myvar1 you can also add environment variables one by one in the environment variables section these entries will automatically sync with the docker options field make sure to click &quot;+&quot; adding the environment variable and value env\&amp;stuff these options will be appended to the docker run command before the docker run command is executed within one of the machines similar to how it would be if you were executing &quot;docker run e myvar1 image\[ |@digest]&quot; on your local machine docker run env option documentation https //docs docker com/reference/cli/docker/container/run/#env step 5 \[optional] open custom ports in addition to setting environment variables, you can also open custom ports by adding p ${host machine port} ${container port} to the docker options section you can also add them to the ports section once more, this will automatically sync with the docker options field env\&amp;stuff faq docid fgh0zckklw1nz3kttmal you can see what&#x27;s happening inside a container at particular port by finding the public ip address mapped to that container port instances guide docid\ v5zbddmwmtwvghat6wnu instances guide docid\ v5zbddmwmtwvghat6wnu internally, one of the machine&#x27;s open ports is mapped to this container port you can use ssh to open a ssh connection to the host machine&#x27;s public ip address the machine will forward traffic from the host machine&#x27;s public port to the container port you specified you can use a ssh command like the one below ssh p \[ssh port] \[user]@\[remote host] l \[local port]\ localhost \[remote port] ssh p 22 user\@remote example com l 8080\ localhost 8080 step 6 pick a launch mode you can choose the jupyter, ssh, or entrypoint launch mode depending on your needs vast launch modes https //docs vast ai/instances/launch modes step 7 add starting bash commands to on start script section these commands will be executed as soon the docker container starts, similar to how a bash script be would be executed suppose you created a bash script and added a cmd command in the dockerfile so docker would execute this bash script when the container starts before vast \# use a base image from ubuntu 20 04 \# make the script executable run chmod +x /usr/local/bin/start sh \# set the script as the default command cmd \[&quot;/usr/local/bin/start sh&quot;] in this case cmd specifies the default command to run when the container starts, which is your start sh script in on start script section with vast chmod +x /usr/local/bin/start sh bash /usr/local/bin/start sh you can also try to overwrite existing files built into the image make sure you can switch to a user that has write permissions to that particular file for example, you can remove all instances of &#x27; sslonly&#x27; in a particular file using sed sed i &#x27;s/ sslonly//g&#x27; /dockerstartup/vnc startup sh you can also make directories sudo i u kasm user mkdir p /home/<USER>/desktop exampleonstart make sure to append environment variables to /etc/environment file in your on start section because this makes environment variables available to all users and processes and ensures they are persisted even if your instance/docker container is rebooted this code snippet will take care of this env &gt;&gt; /etc/environment also make sure to find the image’s entrypoint or cmd command and call that command at the end of the on start section we overwrite that command to set up jupyter/ssh server, etc under the hood step 8 specify docker repository server name if necessary you don&#x27;t have to specify docker io as the server name if your repository is docker hub docker automatically uses docker io to pull the image if no other registry is specified you do have to specify your server name if your repository is something else for example, github container registry (ghcr) server name ghcr io google container registry (gcr) server name gcr io step 9 specify docker login username and password we will have the same character set for docker login usernames and passwords as docker soon dockerlogin step 10 specify template name and description templatename step 11 select the amount of disk space you want diskspace2 step 12 fill out accessibility section choose whether to include readme, make template public, and fill out readme if you desire readme step 13 choose to save and/or select your template a new template will be created when you edit, make changes, and save createbuttons \[optional] download/upload files/backup to cloud storage you can download and upload files copy data to/from instance storage https //docs vast ai/instances/data movement you can also backup data to cloud storage backup data to cloud storage https //docs vast ai/instances/cloud sync troubleshooting if your image is built for a different cpu architecture than your vast machine, then it won&#x27;t work you can try to find a machine with the required cpu architecture using our gui or cli cli documentation commands docid\ lxgndpzugf1ini1sq2gt0 if your image requires a higher cuda version, then look for a machine with a higher max cuda version the max cuda version can be found on the instance card instances guide docid\ v5zbddmwmtwvghat6wnu if your image is built to run jupyter, then try running it on a port different than 8080 if you are having issues using ssh launch mode, add your public key to / authorized keys, install openssh, start openssh when the container starts, and forward the ssh server&#x27;s port template examples creating templates for grobid </div></div></div></div><div class="flex flex-col"><div class="no-print flex flex-col justify-end sm:flex-col w-full max-w-full pt-16"><div class="ab-doc-template-footer-container flex flex-col md:flex-row md:pt-7 md:justify-between sm:items-center 2xl:pb-7 border-t border-gray-100 dark:border-gray-800"><div class="flex flex-1 flex-col 2xl:mb-0 p-6 md:p-0 items-center md:items-start"><div class="flex items-center text-gray-500 dark:text-gray-400 text-base p-1"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1.5 stroke-current"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>Updated<!-- --> <!-- -->05 Mar 2025</div></div><div><div class="flex flex-col items-center p-6 pr-0 w-full border-t border-gray-100 dark:border-gray-800 2xl:items-center 2xl:justify-end 2xl:p-0 md:rounded md:items-end md:border-none md:w-fit md:p-4 md:pr-0 bg-white dark:bg-transparent text-gray-600 dark:text-gray-300"><div class="flex w-full justify-center md:justify-start text-base mb-4 md:mb-2 text-gray-500 dark:text-gray-300">Did this page help you?</div><div class="flex flex-wrap md:flex-nowrap justify-center md:justify-end"></div></div></div></div><div class="border-t border-gray-100 dark:border-gray-800 my-7 2xl:mt-0"></div><div class="flex flex-col lg:flex-row justify-between w-full dark:border-gray-800"><a title="Data Movement" tabindex="0" class="ab-nav-left ab-tab-focus flex flex-1 items-center lg:max-w-[calc(50%-0.5rem)] justify-start text-left mb-2 rounded-2xl cursor-pointer py-3 px-4 bg-gray-50 dark:bg-gray-850 hover:bg-gray-100 dark:hover:bg-gray-800 css-ypjd7c" href="data-movement.html"><div class="mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="navigation-arrow stroke-current text-gray-300 dark:text-gray-500 transition-all"><circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line></svg></div><div class="max-w-[90%]"><div class="text-gray-500 dark:text-gray-400 text-xs font-bold mt-0.5 text-left">PREVIOUS</div><div class="ab-nav-left-text text-left font-bold text-lg truncate">Data Movement</div></div></a><a title="Creating Templates for GROBID" tabindex="0" class="ab-nav-right ab-tab-focus flex flex-1 items-center lg:max-w-[calc(50%-0.5rem)] justify-end text-right mb-2 rounded-2xl cursor-pointer py-3 px-4 bg-gray-50 dark:bg-gray-850 hover:bg-gray-100 dark:hover:bg-gray-800 css-ypjd7c" href="creating-templates-for-grobid.html"><div class="max-w-[90%]"><div class="text-gray-500 dark:text-gray-400 text-xs font-bold mt-0.5">NEXT</div><div class="ab-nav-right-text text-right max-w-full font-bold text-lg truncate">Creating Templates for GROBID</div></div><div class="ml-3"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="navigation-arrow stroke-current text-gray-300 dark:text-gray-500 transition-all"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></svg></div></a></div><div class="flex justify-center mt-12"><div class="my-2 flex mb-8 xl:hidden"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-[248px]"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div></div></div></div></div></div></div><div class="hidden"><div class="w-0 h-0 fixed right-0 bottom-0 pr-2 pt-7 xl:h-screen xl:w-1/3 xl:min-w-[33.33%] 2xl:min-w-[auto] 2xl:max-w-[450px] bg-white dark:bg-gray-900" style="top:100px"><div class="overflow-hidden h-full"><div id="ab-code-drawer" class="h-full"></div></div></div></div><div class="hidden xl:block w-[256px] 2xl:w-[360px] xl:flex-shrink-0"><div class="ab-right-column"><div class="ab-toc-container"><div id="ab-toc-portal"></div></div></div><div class="my-2 hidden"><a href="../external.html?link=https://www.archbee.com/?utm_campaign=hosted-docs&amp;utm_medium=referral&amp;utm_source=docs.vast.ai" target="_blank" class="flex items-center justify-center mx-auto unselectable px-4 py-1 h-[40px] border cursor-pointer rounded-lg bg-gray-400/5 border-transparent w-full"><span class="text-gray-600 dark:text-gray-300">Docs powered by</span> <span class="flex items-center gap-1.5 font-semibold text-gray-600 dark:text-gray-100">Archbee</span></a></div></div></div></div></div></div></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section></div></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"hostname":"docs.vast.ai","pdfExport":false,"showToC":true,"shareableToken":"","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","docId":"/creating-a-custom-template","_doc":{"id":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","name":"Creating a Custom Template","icon":"","title":"Creating a Custom Template","previewImageURL":"","summary":null,"urlKey":"creating-a-custom-template","description":"","urlAlias":"","data":{"nodes":[{"id":"GiAA75c3lz2wVn33jURUM","type":"h1","children":[{"text":"How To: Create Your Own Template","id":"GJA5dZan_iEAd0qzd5AYw"}]},{"id":"QhiynpaTz2KB7pVfg_Vxf","type":"h2","children":[{"text":"Introduction","id":"pHV8UubnuJJhks_TKgFy_"}]},{"id":"N3ZmfxlNBCxNhUB4vz-Bd","type":"paragraph","children":[{"text":"This guide will help you create custom templates for running containers on Vast.ai. There are three approaches:"}]},{"type":"numbered-list","children":[{"type":"list-item","children":[{"id":"gAj1IG4iljmUa7WatlXWv","type":"list-item-child","data":null,"children":[{"text":"Using PROVISIONING_SCRIPT with recommended templates (simplest)"}]}],"id":"Kyk71sjteBsDKOVb2GO7s"},{"type":"list-item","children":[{"id":"N79ldP2yZxio2FcV82xOa","type":"list-item-child","children":[{"text":"Create Dockerfile from Vast.ai base images (recommended for custom images)"}]}],"id":"4TiAmtLp1TstJDyplJuFu"},{"type":"list-item","children":[{"id":"-oFHB5KGjlhtWxuAeRArE","type":"list-item-child","data":null,"children":[{"text":"Using existing Docker images (with security considerations)"}]}],"id":"DQbf0d-RGzRnTLF4TfGqd"}],"id":"ufvQThG2dw-IoE9HiGDi8"},{"type":"paragraph","children":[{"text":"Vast instances are currently Linux Docker containers and Ubuntu virtual machines. Many Linux based images should work within Vast. ","id":"AXnKtKSkisNTd3e0ZCxvt"}],"id":"15CtphSEDwITtbuty2Umq"},{"id":"77RGdjAWl1H5hApwFZAuW","type":"h2","children":[{"text":"Prerequisites","id":"rILjJ4TmfID-oMTijU1eY"}]},{"id":"zaEBr3kP-jCgL2kBZ78N8","type":"bulleted-list","children":[{"id":"Dt-ZT0no4wKzhBQEvzuwG","type":"list-item","children":[{"id":"fp7vvJ_vh7iRIPBxNVxn_","type":"list-item-child","children":[{"text":"A Vast.ai account","id":"cyDXMVC8B7VX_Nk7YX2qM"}]}]},{"id":"2OgFEOS0zHLbTjfhS4hDi","type":"list-item","children":[{"id":"n-FOw2YDQS3pYyGzf_V_z","type":"list-item-child","children":[{"text":"","id":"XaJcRDZTob6v4RzhBNcWT"},{"id":"drPosWRzag7DfPTAtm7vO","type":"link","data":{"href":"https://cloud.vast.ai/manage-keys/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"SSH client installed on your local machine and SSH public key added in the SSH Keys tab in the Keys section of your console","id":"Xg2wnwlE1JAdejnvWc9iR"}]},{"text":" ","id":"7YNzuUGSmev9K-tV4Xt_N"}]}]},{"id":"kAjnFkHaU4FBwEGCT4dsY","type":"list-item","children":[{"id":"TLcViSzSIn7pV1fgkEdCj","type":"list-item-child","data":null,"children":[{"text":"","id":"RMi-UgPv8g-XfFllcWYeZ"},{"id":"-VMr20OgYD5tPlkj-DaN6","type":"link","data":{"href":"https://docs.vast.ai/cli","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"(Optional) Install and use vast-cli","id":"uiVnEYpcXPw8H2pyEZHsC"}]},{"text":" ","id":"GwUUcAcl6bvbXuBPrXcu7"}]}]}]},{"id":"JqM6i3yZset1V8GzWxdGT","type":"h2","children":[{"text":"Approach 1: Using PROVISIONING_SCRIPT (Simplest)"}]},{"type":"paragraph","children":[{"text":"This approach lets you customize recommended templates without building a custom image."}],"id":"93CrCNdDmQp8UXKIwJA-Z"},{"type":"h3","children":[{"text":"Steps"}],"id":"PV_5KOip_QCOn4DgksG21"},{"type":"numbered-list","children":[{"type":"list-item","children":[{"id":"8kjNk_Q4cJ6qa4MKGJQZF","type":"list-item-child","data":null,"children":[{"text":"Go to the "},{"id":"k6B68kQ3Nixx1q3XIPr38","type":"link","data":{"href":"https://cloud.vast.ai/templates/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Templates tab "}]},{"text":"in Vast.ai interface"}]}],"id":"Ul0MrvhR6KXg09Q6ep1EM"},{"type":"list-item","children":[{"children":[{"text":"Search for \"base-image\" or \"Pytorch\" depending on your needs: "}],"type":"list-item-child","id":"Y6JvKqYueJ1X5aLrU6dKP"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"hzPxMN8sCeI4OucBuEtqT","type":"list-item-child","data":null,"children":[{"text":"vast-ai/base-image is a general purpose image"}]}],"id":"9Xh6mbAejLgsaoCb0LAsu"},{"type":"list-item","children":[{"id":"Jf1wIdyJ-zAwuOUlIN8gh","type":"list-item-child","data":null,"children":[{"text":"vast-ai/pytorch is a base image for working with PyTorch-based applications on Vast"}]}],"id":"hxGiZBrfJ7oos_rpH96Zr"}],"id":"uBq7obi6ghUOo1s-IYSeR"}],"id":"pAd94GLkBMeAWdzJzjsMs"},{"type":"list-item","children":[{"id":"8VaBsfUy0zpo1XU8MdbUE","type":"list-item-child","data":null,"children":[{"text":"Click \"Edit\" on your chosen template"}]}],"id":"tQevDwXURhsni09WjidUi"},{"type":"list-item","children":[{"children":[{"text":"Add the PROVISIONING_SCRIPT environment variable: "}],"type":"list-item-child","id":"V5zWYbLlWgq-wHACD0VOA"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"rxMCrHaKXQKArAffrQmqI","type":"list-item-child","data":null,"children":[{"text":"In the Environment Variables section, add a new variable named \"PROVISIONING_SCRIPT\""}]}],"id":"m_IEs6NG-3KpFNzqMmH6J"},{"type":"list-item","children":[{"id":"mPAzsZLSPYdzIqIwfZ_QN","type":"list-item-child","data":null,"children":[{"text":"The value should be a URL pointing to a shell script (from GitHub, Gist, etc.)"}]},{"id":"AkmjjNqGIjqmjbSFbX6ly","type":"bulleted-list","children":[{"id":"PnIEkSqA6JwVAT_RNH3nQ","type":"list-item","children":[{"id":"t2XsR9st8sOBgi_BXMB46","type":"list-item-child","data":null,"children":[{"text":"If you're specifying a shell script stored in GitHub repo, specify url like "}]}]}]}],"id":"h9hggaWdiZZBVankLAW6f"}],"id":"p6KrYPvEETVnHUhko-zVX"}],"id":"YCeHsVmdCF-88GCvRJkkH"}],"id":"pbMERL1dZgecPEaFirdZ4"},{"id":"kkRK8A7kOmcED0a_k5iWV","type":"code-editor-v2","data":{"languages":[{"id":"Hnj_hz0eFH9WVgTbRiiYE","language":"curl","code":"https://raw.githubusercontent.com/karthik-vast-ai/vast-cli/distributed-inference-integration/provisioning_script.sh7","customLabel":"Url"}],"selectedLanguageId":"Hnj_hz0eFH9WVgTbRiiYE"},"children":[{"text":""}]},{"id":"uIzsNHu-p6IiRWSh3ouN1","type":"numbered-list","children":[{"id":"e-jA1ENsPwwh4sHQ1sQh_","type":"list-item","children":[{"id":"26EaRzEh3-9j7HI5BFDYJ","type":"list-item-child","data":null,"children":[{"text":"Make sure to click \"+\" to add the environment variable"}]}]},{"id":"6wqciJ-MW5HOIVVuQUGGr","type":"list-item","children":[{"id":"HKkj1M5i15CoxXxyi1_-3","type":"list-item-child","data":null,"children":[{"text":"Click Create and Use"}]}]}]},{"id":"G9VIR98TXb8E7yZ2jV0UX","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/DXSM3LlVb4llv9ZkijIZ9-ecTniHpPJjmzK40ccpJ0g-20250211-200512.png","signedSrc":"","size":100,"width":1569,"height":1337,"position":"center","caption":"Add PROVISIONING_SCRIPT variable","alt":""},"children":[{"text":""}]},{"type":"paragraph","children":[{"text":"Example PROVISIONING_SCRIPT:"}],"id":"iFt4pTCAd0Wqguqvc2iBQ"},{"id":"Mvf1bQkh-_-VX6crMCY7O","type":"code-editor-v2","children":[{"text":""}],"data":{"languages":[{"id":"PmBrTmVoVW1vpknI_ltpt","language":"bash","code":"#!/bin/bash\n\n# \ncd /workspace/\n# Cause the script to exit on failure.\nset -eo pipefail\n\n# Activate the main virtual environment\n. /venv/main/bin/activate\n\n# Install your packages\npip install your-packages\n\n# Download some useful files\nwget -P \"${WORKSPACE}/\" https://example.org/my-application.tar.gz\ntar xvf ${WORKSPACE}/my-application.tar.gz\"\n\n# Set up any additional services\necho \"my-supervisor-config\" \u003e /etc/supervisor/conf.d/my-application.conf\necho \"my-supervisor-wrapper\" \u003e /opt/supervisor-scripts/my-application.sh\nchmod +x /opt/supervisor-scripts/my-application.sh\n\n# Reconfigure the instance portal\nrm -f /etc/portal.yaml\nexport PORTAL_CONFIG=\"localhost:1111:11111:/:Instance Portal|localhost:1234:11234:/:My Application\"\n\n# Reload Supervisor\nsupervisorctl reload","customLabel":""}],"selectedLanguageId":"PmBrTmVoVW1vpknI_ltpt"}},{"id":"tvmR_CcHH9NhphlpWvOZp","type":"paragraph","children":[{"text":"This script will run on first boot to set up your environment. All installations should go to /workspace/ for proper persistence."}]},{"type":"h3","children":[{"text":"Configuring Application Access with PORTAL_CONFIG"}],"id":"O0StRqYoPRkIiTGfO1mzZ"},{"type":"paragraph","children":[{"text":"The base-image template includes PORTAL_CONFIG for secure application access management. This environment variable controls how applications are exposed and accessed."}],"id":"lVRk-bGciUxY977VsBkeC"},{"id":"U8rd-RYl0waWTDvlpMGwH","type":"code-editor-v2","children":[{"text":""}],"data":{"languages":[{"id":"NodSj5z7qiyZBNPbcQZqS","language":"bash","code":"hostname:external_port:local_port:url_path:Application Name|hostname:external_port:local_port:url_path:Application Name","customLabel":"PORTAL_CONFIG structure"}],"selectedLanguageId":"NodSj5z7qiyZBNPbcQZqS"}},{"id":"1QXn1n-GzVUrmQsarz8tq","type":"paragraph","children":[{"text":"The structure of this variable is:"}]},{"id":"YrzaAqCZjptizY__tpk0v","type":"bulleted-list","children":[{"id":"078xeouklymALF_pk5nVl","type":"list-item","children":[{"id":"VkuxLy-_xxIDz0wV__8tD","type":"list-item-child","children":[{"text":"Each application is separated by the "},{"text":"|","highlight":true},{"text":" character"}]}]},{"id":"3HsoubZryr454vBnyHDst","type":"list-item","children":[{"id":"by3xR9GhbnPVNi8wA_8NZ","type":"list-item-child","children":[{"text":"Each application parameter is separated by the "},{"text":":","highlight":true},{"text":" character"}]}]},{"id":"0UZn_PVazSD6_WRblYqI3","type":"list-item","children":[{"id":"8pLHuE7Od0zsuUelHxTKZ","type":"list-item-child","children":[{"text":"Each application must specify "},{"text":"hostname:external_port:local_port:url_path:Application Name","highlight":true}]}]}]},{"id":"E15HS6iUiZL8Yzb1ZS-2S","type":"paragraph","data":null,"children":[{"text":"Example:"}]},{"id":"6aqTAbL07lAzR8ysCmutG","type":"code-editor-v2","children":[{"text":""}],"data":{"languages":[{"id":"IGVX85fQm89cuGXjnRber","language":"bash","code":"\"localhost:8002:18002:/hello:MyApp|localhost:1111:11111:/:Instance Portal|localhost:8080:18080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing|localhost:6006:16006:/:Tensorboard\"","customLabel":""}],"selectedLanguageId":"IGVX85fQm89cuGXjnRber"}},{"id":"OQfU5TWGdxTfnjj-BgkFr","type":"paragraph","children":[{"text":"The hostname in Docker instances will always be "},{"text":"localhost","highlight":true}]},{"id":"rDWeWqJ_GQuNOqkAyoK5r","type":"paragraph","children":[{"text":"Where the internal port and local port are not equal then Caddy will be configured to listen on "},{"text":"0.0.0.0:external_port","highlight":true},{"text":" acting as a reverse proxy for "},{"text":"hostname:local_port","highlight":true}]},{"id":"hEnwXKX7-nbCpjTq-L5Or","type":"paragraph","children":[{"text":"If the "},{"text":"external_port","highlight":true},{"text":" and "},{"text":"local_port","highlight":true},{"text":" are equal then Caddy will not act as a proxy but the Instance Portal UI will still create links. This is useful because it allows us to create links to Jupyter which is not controlled by Supervisor in Jupyter Launch mode."}]},{"id":"QGO7CyT7xbNokXqi9aTPH","type":"paragraph","children":[{"text":"url_path","highlight":true},{"text":" will be appended to the instance address and is generally set to "},{"text":"/","highlight":true},{"text":" but can be used to create application deep links."}]},{"id":"XB70M-_PrI9N7fZH_Gcv2","type":"paragraph","children":[{"text":"The "},{"text":"caddy_manager","highlight":true},{"text":" script will write an equivalent config file at "},{"text":"/etc/portal.yaml","highlight":true},{"text":" on boot if it does not already exist. This file can be edited in a running instance."}]},{"id":"OrF0ZKgaR-U3yaTk-Lje5","type":"paragraph","children":[{"text":"Important: When defining multiple links to a single application, only the first should have non equal ports - We cannot proxy one application multiple times."}]},{"id":"TpAb3-0fg9b6MFsBKUKJn","type":"paragraph","children":[{"text":"Note: Instance Portal UI is "},{"text":"not","bold":true},{"text":" required and its own config declaration can be removed from "},{"text":"PORTAL_CONFIG","highlight":true},{"text":". This will not affect the authentication system."}]},{"id":"sRA5Y98IZM3fPRP0wXC4J","type":"h2","children":[{"text":"Approach 2: Create Dockerfile from Vast.ai Base Images"}]},{"type":"h3","children":[{"text":"Why Use Vast.ai Base Images?"}],"id":"iKLJ4K3m82po2eNh3Yrw6"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"RqgULDUS32LLRBiSEN5Rd","type":"list-item-child","data":null,"children":[{"text":"Built-in security features through Caddy proxy"}]}],"id":"rjc-LFZxYvZ5ZrrFjJHd3"},{"type":"list-item","children":[{"id":"MDXnMlEad2A_8hLbbG8jq","type":"list-item-child","data":null,"children":[{"text":"Automatic TLS encryption for web services"}]}],"id":"ltLLrWcox2f_mCd5CammG"},{"type":"list-item","children":[{"id":"T639intDn6FYADeAKQGVG","type":"list-item-child","data":null,"children":[{"text":"Authentication token protection"}]}],"id":"UJ4zcqxzrGUDnzctTgFjw"},{"type":"list-item","children":[{"id":"_Qw61yJhfa1clM5Sj3dr8","type":"list-item-child","data":null,"children":[{"text":"Proper isolation between external and internal services"}]}],"id":"q-C7PBG5sk4THxWkMmCxA"}],"id":"9SGHtbtkMlwXT9cbrurq2"},{"type":"h3","children":[{"text":"Steps"}],"id":"FjnbCdud0fgbpyx-jbPg7"},{"type":"numbered-list","children":[{"type":"list-item","children":[{"id":"eSfAIpOuEz9oPJlkVfpQx","type":"list-item-child","data":null,"children":[{"text":"Start with a "},{"id":"mMpJ9511GBKbUQVxwtyc5","type":"link","data":{"href":"https://hub.docker.com/r/vastai/base-image/tags","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Vast.ai base image"}]},{"text":" or "},{"id":"wPSB7LgNHepSL-D4-ZzTO","type":"link","data":{"href":"https://hub.docker.com/r/vastai/pytorch/tags","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Vast.ai Pytorch base image"}]},{"text":" in your Dockerfile:"}]}],"id":"Zl5dJQl9xptEMGVT89itw"}],"id":"1lXBG-TpanhGLK0irX4H3"},{"id":"4lOAoUtTKGGIENGMcBGfq","type":"code-editor-v2","data":{"languages":[{"id":"HiHRQoDPxO4nHNykwznWp","language":"dockerfile","code":"#For example \nFROM vastai/base-image:cuda-12.6.3-cudnn-devel-ubuntu22.04-py313\n# or\nFROM vastai/pytorch:2.6.0-cuda-12.6.3-py312\n\n# Install your applications into /opt/workspace-internal/\n# This ensures files can be properly synced between instances\nWORKDIR /opt/workspace-internal/\n\n# Activate virtual environment from base image \nRUN . /venv/main/bin/activate\n\nRUN your-installation-commands","customLabel":""}],"selectedLanguageId":"HiHRQoDPxO4nHNykwznWp"},"children":[{"text":""}]},{"id":"9TsfTyDJuIDAeidMQdgfW","type":"bulleted-list","data":null,"children":[{"type":"list-item","children":[{"id":"pqxhOzLwBFQHY14GixnTN","type":"list-item-child","data":null,"children":[{"text":""},{"id":"neD06SAy6eME4m3zdaDzX","type":"link","data":{"href":"https://docs.docker.com/build/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Build"}]},{"text":" and "},{"id":"7hy3IUj5auyIbvroDXNKo","type":"link","data":{"href":"https://docs.docker.com/reference/cli/docker/image/push/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"push your image"}]},{"text":" to a container registry"}]}],"id":"07xb28ex7Gx8EyNCR6dnl"},{"type":"list-item","children":[{"id":"tcV78MZJveQt4jtBSlena","type":"list-item-child","data":null,"children":[{"text":"Create a new template using your custom image"}]},{"id":"_9JqZduz-czchSiZn3Fsm","type":"bulleted-list","children":[{"id":"udmM2HXHoUjO89ocmoVqY","type":"list-item","children":[{"id":"TnCP34UZ7ifZvylKtRO_y","type":"list-item-child","data":null,"children":[{"text":"See next approach for steps on how to do that"}]}]}]}],"id":"CxX4ozH1sP2LOWuIt0u0e"}]},{"id":"X8MZuFRBltS82MbWMWuiC","type":"h2","children":[{"text":"Approach 3: Create Template Using Existing Docker Images"},{"text":" ","id":"tn4ySw3IQxQBhpxbL_4fu"}]},{"type":"paragraph","children":[{"text":"You can use existing Docker Hub images, but please note:"}],"id":"g506sDm0g3lPYZfzoLRgL"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"0t6mTuZuFo8sAQhYIouEz","type":"list-item-child","data":null,"children":[{"text":"Running external-facing services without proper security measures is not recommended"}]}],"id":"XJq_JZEdhs_qqsrvVOgIr"},{"type":"list-item","children":[{"id":"TAt7SIdp5mw7lQcQ_uFia","type":"list-item-child","data":null,"children":[{"text":"Consider wrapping the service in Vast.ai's security infrastructure"}]}],"id":"JBWIqCXIuKYoSINq2gmns"}],"id":"gAlvhVPBsgaAf_fEOo3pz"},{"id":"UyI7CJygLaSD7PRAqqBNB","type":"h3","children":[{"text":"Step 1 -  Navigate to the 'Templates' Section","id":"kLkPXIZVZ86mNYvJWzlN-"}]},{"id":"juJgaGznwA8PCG1HjosBV","type":"paragraph","children":[{"text":"Once logged in, click on ","id":"eVzMYhWHSCeqZrGFu9u8j"},{"text":"Templates ","bold":true,"id":"jpND6koFsLXlvPZW15hOd"},{"text":"to go to the template section. This is where you can create, manage, and edit templates.","id":"mHqz083lwVYJaZpobSxTl"}]},{"id":"FU5xZ7O4bQzk49HVmiTM3","type":"h3","children":[{"text":"Step 2 - Create a New Template","id":"GmtOfXRSSg16U0Mrw3l4g"}]},{"id":"S8UMBHKlP6tmqhXygsO5T","type":"paragraph","children":[{"text":"Click the button ","id":"uAGvOtpLKYd0EjeDPanab"},{"text":"+ New","bold":true,"id":"7g0-r0udmAtstW28bFqQZ"},{"text":" to create a new template.\n","id":"_HFUG0gXpRj5qU_OoAJDd"}]},{"id":"WHiisD4H2_W4OUXWqMyQ1","type":"image","data":{"src":"https://vast.ai/uploads/templates/Create.jpg","signedSrc":"https://vast.ai/uploads/templates/Create.jpg","size":100,"alt":"Create","caption":"Create","isUploading":false},"children":[{"text":"","id":"Ke7Ko9VLwhCro5TYpYwmE"}]},{"id":"W00orqzC7aC3gykoT24n-","type":"h3","children":[{"text":"Step 3 - Specify the image path/tag of your docker image","id":"-jPGp-3d3AhK5miTBKZ5j"}]},{"id":"_YumK-t4P1aZgTuJburxR","type":"paragraph","children":[{"text":"In the Image Path Tag, you can add the path/tag of the docker image you wish to use. The Version Tag is where you would set the version.","id":"U0GOP8laX8q5Z-0ntJ8K2"}]},{"id":"o7I6i_CidMoB36LcVIjTn","type":"image","data":{"src":"https://vast.ai/uploads/templates/Image\u0026Tag.png","signedSrc":"https://vast.ai/uploads/templates/Image\u0026Tag.png","size":100,"alt":"Image\u0026Tag","caption":"Image\u0026Tag","isUploading":false},"children":[{"text":"","id":"-muXYi8EPqWIQVyhruisu"}]},{"id":"sfmJBS87J51cTDMzDNCIX","type":"h3","children":[{"text":"Step 4 - Set Environment Variables","id":"J6IM7zE5b4sqzBD8odOvL"}]},{"id":"tgKFq7MgsQke1dpIMBQdv","type":"paragraph","children":[{"text":"You can use the -e option to set environment variables in the Docker Options field by adding something like:","id":"ap_kDld50eVhvhw_i5GgL"}]},{"id":"RIMq957QC-IkeG7JT_s6l","type":"paragraph","children":[{"text":"-e MYVAR1","bold":true,"id":"i4GKKuV1ydiWXUozDusUs"}]},{"id":"1d-VRFlbFZ3b_k1Piw6gw","type":"paragraph","children":[{"text":"You can also add environment variables one by one in the Environment Variables section. These entries will automatically sync with the Docker Options field. ","id":"7Blq-X5ivd2OetAGlL-KA"},{"text":"Make sure to click \"+\"  adding the environment variable and value."}]},{"id":"8FTrM7CqtsUqyI492ARH0","type":"image","data":{"src":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","signedSrc":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","size":100,"alt":"Env\u0026Stuff","caption":"Env\u0026Stuff","isUploading":false},"children":[{"text":"","id":"HyMj-6mG_SM7iQUgkiRpr"}]},{"id":"tHd-oZVHZ_bpyGiSgS8yg","type":"paragraph","children":[{"text":"These options will be appended to the docker run command before\nthe docker run command is executed within one of the machines. Similar to how\nit would be if you were executing \"docker run -e MYVAR1 IMAGE[","id":"IkHrSGh1dEaCLbfpzaT2E"},{"text":"|@DIGEST]\" on your local machine.","id":"Vh_Op48-ciBtMynu9kXL2"}]},{"id":"RQSVwhUIgzfLymPNwP8JJ","type":"paragraph","children":[{"text":"","id":"uFuEyp8GF9P6iH47HCsy8"},{"id":"Y2JRDDN_ZIYtFL2Z5hTUj","type":"link","data":{"href":"https://docs.docker.com/reference/cli/docker/container/run/#env","newTab":false},"children":[{"text":"Docker Run Env Option Documentation","id":"eY6Vuj9TPxeqBjSEmFoIJ"}]},{"text":"","id":"bdOVYyW2A7KSx02Y1miUf"}]},{"id":"d3CG08BGoRpKTRi4tZ_Gf","type":"h3","children":[{"text":"Step 5 - [Optional] Open Custom Ports","id":"p3acPF5xei6cRckoWoA0Z"}]},{"id":"LN5GESca5ldV21ylULklE","type":"paragraph","children":[{"text":"In addition to setting environment variables, you can also open custom ports by adding\n","id":"6Hp4-TLr0Hu7sipvIYC_A"},{"text":"-p ${HOST_MACHINE_PORT}:${CONTAINER_PORT}","bold":true,"id":"NaLx1a82pbma3Mr-6oa5V"},{"text":" to the Docker Options section.","id":"ZVqBnPKYVEDYO_MDk7TPu"}]},{"id":"x3-CR-HzGH9qxwVQ5DsXC","type":"paragraph","children":[{"text":"You can also add them to the Ports section. Once more, this will automatically sync with the Docker Options field.","id":"rSCXYf8kXlByAohj0YeJn"}]},{"id":"V9I5i371u4UeRe0fSJ2yF","type":"image","data":{"src":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","signedSrc":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","size":100,"alt":"Env\u0026Stuff","caption":"Env\u0026Stuff","isUploading":false},"children":[{"text":"","id":"bXK4y4YNvislX9TOojjMx"}]},{"id":"W3lK2RkC8W2KXTryYq82e","type":"paragraph","children":[{"text":"","id":"Nue0kH5lyyBzngyYya65m"},{"id":"1t8U20XSiglRwcb9iecln","type":"mention-doc","data":{"label":"FAQ","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"_fGH0ZcKKlW1NZ3kTTMaL","version":"v2","docAnchorId":"#SrJLn","loadingMethod":"static","overridedLabel":"FAQ: How can I Open Custom Ports","href":"/faq"},"children":[{"text":"","id":"Dn_HF-RSdY6eij6aqxYN9"}]},{"text":" ","id":"vB8rpwUwV5lzQVvJOMw-J"}]},{"id":"iunW8m0BVPCfNWilttVY8","type":"paragraph","children":[{"text":"You can see what's happening inside a container at particular port by finding the public ip address","id":"cE2Tk0DN5I44gg2sjfcEh"},{"text":"\nmapped to that container port.","id":"JcjY5UHxZG-LWuj-3UPoq"}]},{"id":"mJQ9VWp2AS2uYqxiFxtA9","type":"paragraph","children":[{"text":"","id":"XbQ3VIfWDjoyFevQJXem0"},{"id":"-K74sDvawl0tnl7yRdM1y","type":"mention-doc","data":{"label":"Instances Guide","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"v5ZbddMWMtwvGHat6WNu-","version":"v2","docAnchorId":"#dkpT9","loadingMethod":"static","overridedLabel":"How to View Instance's IP Addresses and Open Ports","href":"/instances-guide"},"children":[{"text":"","id":"J08SJZoChuqAeZ7bnNKta"}]},{"text":" ","id":"U74K8lbhSTLfm8_OKwvPY"}]},{"id":"N0gCqfUphaIoX3ePoSucG","type":"paragraph","children":[{"text":"","id":"SMMerft0SoOsQe3k00xw2"},{"id":"bGCpHE1NoThRYrkhqTiIw","type":"mention-doc","data":{"label":"Instances Guide","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"v5ZbddMWMtwvGHat6WNu-","version":"v2","docAnchorId":"#E94QR","loadingMethod":"static","overridedLabel":"More details on How to View Instance's IP Addresses and Open Ports","href":"/instances-guide"},"children":[{"text":"","id":"6lA-ACrORBmvz4Nq8C8dH"}]},{"text":" ","id":"UjTwci8cKyc4tKG0gdrok"}]},{"id":"H00sSBa7XfOru7nvaDJ_E","type":"paragraph","children":[{"text":"Internally, one of the machine's open ports is mapped to this container port.","id":"yw-Ra_VZWGP5gyOMW9yal"}]},{"id":"OkpXf4siL5KwGHSSZTX5d","type":"paragraph","children":[{"text":"You can use ssh to open a ssh connection to the host machine's public ip address","id":"DSv_Agb8uhbtt38gR6hfm"},{"text":".\nThe machine will forward traffic from the host machine's public port to the container port you\nspecified.","id":"5nVxJ2Fhnr8FSoqWPylYg"}]},{"id":"F3wBbC33qnfnY_1gkIyTV","type":"paragraph","children":[{"text":"You can use a ssh command like the one below.","id":"IHjQB4MD9ki8mzKso4V86"}]},{"id":"75cxPZyL11C-SSimX_ZsE","type":"code-editor-v2","data":{"languages":[{"id":"Pnytp_LhzrUSIXFkns90E","language":null,"code":"ssh -p [SSH_PORT] [USER]@[REMOTE_HOST] -L [LOCAL_PORT]:localhost:[REMOTE_PORT]\nssh -p 22 <EMAIL> -L 8080:localhost:8080"}],"selectedLanguageId":"Pnytp_LhzrUSIXFkns90E"},"children":[{"text":"","id":"Lg2Jl6SBRTauKmqTRRSd7"}]},{"id":"kiD4z4jvmVVh0XbmUoM2Q","type":"h3","children":[{"text":"Step 6 - Pick a Launch Mode","id":"sTXLsPGsoi8fn85GLRGDq"}]},{"id":"HuadYnzzinL8HDNXIfTfZ","type":"paragraph","children":[{"text":"You can choose the jupyter, ssh, or entrypoint launch mode depending on your needs.","id":"WxnOAq_ovy4JkHoQracZS"}]},{"id":"YFsoXFSLDdmPljDTux1_M","type":"paragraph","children":[{"text":"","id":"THY7mxEAOV6K0sTLJCNI8"},{"id":"yrw9sMsX9jaXWXdvFxywr","type":"link","data":{"href":"https://docs.vast.ai/instances/launch-modes","newTab":false},"children":[{"text":"Vast Launch Modes","id":"7DJi4vdMPra2cKr2u0F46"}]},{"text":"","id":"0vTqsbMYMsNIcwI5Pou6T"}]},{"id":"TfjpYATZbI36BXgjBYeZD","type":"h3","children":[{"text":"Step 7 - Add Starting Bash Commands to On-start Script Section","id":"-RdGoOPJfIqI6sFNNEjpn"}]},{"id":"2430hQxyE7XQznFs5SIl2","type":"paragraph","children":[{"text":"These commands will be executed as soon the docker container starts, similar to how a bash script be\nwould be executed suppose you created a bash script and added a CMD command in the Dockerfile so Docker would\nexecute this bash script when the container starts.","id":"KSXgXhQy7RRfxcoovxjfc"}]},{"id":"CU2JHnOs7J9ZStY7q7_rA","type":"numbered-list","children":[{"id":"a3Fu6mhgc1v6SxGNs8C2G","type":"list-item","children":[{"id":"nVuIxVJcTVYnyvAOwTzP0","type":"list-item-child","children":[{"text":"Before Vast","id":"XeXqjZKzxqpcqy5huOzo0"}]}]}]},{"id":"9L-CYIYWm-L8UtK_SZmIU","type":"code-editor-v2","data":{"languages":[{"id":"qi5Pv4LhCMxwmxy5kQoFt","language":null,"code":"# Use a base image\nFROM ubuntu:20.04\n\n# Make the script executable\nRUN chmod +x /usr/local/bin/start.sh\n\n# Set the script as the default command\nCMD [\"/usr/local/bin/start.sh\"]"}],"selectedLanguageId":"qi5Pv4LhCMxwmxy5kQoFt"},"children":[{"text":"","id":"Ab5zstrmL7X5PlPyY4UsC"}]},{"id":"QWoeWtyc4p_4uYCPuieyR","type":"paragraph","children":[{"text":"In this case:\nCMD specifies the default command to run when the container starts, which is your start.sh script.","id":"IKlmQeZpqT6WIVqrr0TFQ"}]},{"id":"37k9HCW9hT4TjtZ74z9ih","type":"numbered-list","children":[{"id":"O7DeKpCqPqpFmBU5gKavu","type":"list-item","children":[{"id":"qhuuYatditKn8VC1S72gi","type":"list-item-child","children":[{"text":"In On-Start Script Section With Vast:","id":"ZVxbM_0wIDkoo99UoLMc4"}]}]}]},{"id":"aXWHEj94UrwX9JtdN_72B","type":"code-editor-v2","data":{"languages":[{"id":"st0aUdpkIXIA2yZO1F3v7","language":null,"code":"chmod +x /usr/local/bin/start.sh\nbash /usr/local/bin/start.sh"}],"selectedLanguageId":"st0aUdpkIXIA2yZO1F3v7"},"children":[{"text":"","id":"XvWIBazjxGG4FPesNGFw0"}]},{"id":"MTg9WaYgMKuvdFN6RvcDJ","type":"paragraph","children":[{"text":"You can also try to overwrite existing files built into the image.\nMake sure you can switch to a user that has write permissions to that particular file.","id":"nw1SNZyepuWfn-FEVNa4b"}]},{"id":"A7yKCdiTz7ilSr-IxLZQH","type":"paragraph","children":[{"text":"For example, you can remove all instances of '-sslOnly' in a particular file using sed.","id":"RFsvyTbvwZoRp7jc7ubwD"}]},{"id":"sJCN_XWdtH9ZoJqutvive","type":"code-editor-v2","data":{"languages":[{"id":"bRVNUsP_Z8pXYy57zgE-u","language":null,"code":"sed -i 's/-sslOnly//g' /dockerstartup/vnc_startup.sh"}],"selectedLanguageId":"bRVNUsP_Z8pXYy57zgE-u"},"children":[{"text":"","id":"FucYtfHiZGcCmnXPesgGT"}]},{"id":"P9obi_C-w4uwFB-18AgOs","type":"paragraph","children":[{"text":"You can also make directories.","id":"xuK9onZZbrJ6kUX-j_Ubp"}]},{"id":"3xSiOJYfqjAtwxYfpRLty","type":"code-editor-v2","data":{"languages":[{"id":"xfJyiljO7RjsCWz3dx-DJ","language":null,"code":"sudo -i -u kasm-user mkdir -p /home/<USER>/Desktop"}],"selectedLanguageId":"xfJyiljO7RjsCWz3dx-DJ"},"children":[{"text":"","id":"ocvAHCjfXaXF4uDVYCOLt"}]},{"id":"Cd2_812UNGx9z3qCkCNdG","type":"image","data":{"src":"https://vast.ai/uploads/templates/ExampleOnstart.png","signedSrc":"https://vast.ai/uploads/templates/ExampleOnstart.png","size":100,"alt":"Exampleonstart","caption":"Exampleonstart","isUploading":false},"children":[{"text":"","id":"xVF0_A_Oe4HyT7_xuDU6f"}]},{"id":"8jur0cBZys45ofeSheXps","type":"paragraph","children":[{"text":"Make sure to append environment variables to /etc/environment file in your on-start section because this makes environment variables available to all users and processes and ensures they are persisted even if your instance/docker container is rebooted. This code snippet will take care of this.","id":"WySC5sPL7WYOSyjit7_rj"}]},{"id":"d2_cOwjifZqbtHg83aE3C","type":"code-editor-v2","data":{"languages":[{"id":"grad92VGQy3TYTAlAgLom","language":null,"code":"env \u003e\u003e /etc/environment"}],"selectedLanguageId":"grad92VGQy3TYTAlAgLom"},"children":[{"text":"","id":"JY2spcuYEIAY0fJzOepIZ"}]},{"id":"FmJ-mp6yajH9op05Zugg7","type":"paragraph","children":[{"text":"Also make sure to find the image’s ENTRYPOINT or CMD command and call that command at the end of the on-start section. We overwrite that command to set up jupyter/ssh server, etc. under the hood.","id":"fpqnNM7hpD2ceMXNw3eBZ"}]},{"id":"sDcuOGC9f5KBvaH6tJtEm","type":"h3","children":[{"text":"Step 8 - Specify Docker Repository Server Name if Necessary","id":"5MEwtX8qNVH36TH-V7rzh"}]},{"id":"lqz5x_DRQh-I16r8HQLoO","type":"paragraph","children":[{"text":"You don't have to specify docker.io as the server name if your repository is Docker Hub.\nDocker automatically uses docker.io to pull the image if no other registry is specified.","id":"wroRpnNA5raSkeqSaU7bb"}]},{"id":"eSQiJsx21BnoGaNpQW1Tp","type":"paragraph","children":[{"text":"You do have to specify your server name if your repository is something else.","id":"2c8lXDea7q3PRp2nwCLHi"}]},{"id":"NdTto4HGtQJusHr-2GVW5","type":"paragraph","children":[{"text":"For example,","id":"s5DoVN1-o3-MPOBTJ8EV5"}]},{"id":"pn4Cufd45hP0ZOdUJlGBs","type":"paragraph","children":[{"text":"GitHub Container Registry (GHCR) - Server Name: ghcr.io","id":"J5dK-Tgj-2y0LLI3JH0PE"}]},{"id":"iH9KnwFl7gOb9xTbn_-4_","type":"paragraph","children":[{"text":"Google Container Registry (GCR) - Server Name: gcr.io","id":"4Dt4LcOXN1QlIXOmVXkII"}]},{"id":"KjeVxsKcYll2yA-UA75NK","type":"h3","children":[{"text":"Step 9 - Specify Docker Login Username and Password","id":"sq4ecr3tK9Uzk5FODCrpx"}]},{"id":"Fe9M1_hMpHGaZKnNvIjdK","type":"paragraph","children":[{"text":"We will have the same character set for docker login usernames and passwords as Docker soon.","id":"DSiE8aHGPNEqe7u5HAyJ7"}]},{"id":"xVaECxUzQhSMOqCw6_ZhT","type":"image","data":{"src":"https://vast.ai/uploads/templates/DockerLogin.png","signedSrc":"https://vast.ai/uploads/templates/DockerLogin.png","size":100,"alt":"Dockerlogin","caption":"Dockerlogin","isUploading":false},"children":[{"text":"","id":"4dSexir47lLaG1AJCE-LQ"}]},{"id":"1Sb8YB7CN2pHjljan9Qo7","type":"h3","children":[{"text":"Step 10 - Specify Template Name and Description","id":"MNcys80Q34XmqloLnJud3"}]},{"id":"lYQMdtpTOZ9eytmewkPp-","type":"image","data":{"src":"https://vast.ai/uploads/templates/TemplateName.png","signedSrc":"https://vast.ai/uploads/templates/TemplateName.png","size":100,"alt":"Templatename","caption":"Templatename","isUploading":false},"children":[{"text":"","id":"0vZ4z-wM5Pb5zchJHCMIj"}]},{"id":"YHDrASsDwWm1QBvo-9hwZ","type":"h3","children":[{"text":"Step 11- Select the Amount of Disk Space You Want","id":"AL-UifE_8eGSET0UxfqZa"}]},{"id":"88ndFWIhCoe4C70AuKZ53","type":"image","data":{"src":"https://vast.ai/uploads/templates/DiskSpace2.png","signedSrc":"https://vast.ai/uploads/templates/DiskSpace2.png","size":100,"alt":"Diskspace2","caption":"Diskspace2","isUploading":false},"children":[{"text":"","id":"LYsc2tFC2NA4u6d2fqitI"}]},{"id":"H1aVbbOtZhY_czzQivPic","type":"h3","children":[{"text":"Step 12 - Fill Out Accessibility Section","id":"3rfR3I5cf3U9Px8Oq_CYF"}]},{"id":"ziRQmRvo9SKbA7NxHFAdG","type":"paragraph","children":[{"text":"Choose whether to include ReadMe, make template public, and\nfill out readme if you desire.","id":"ggrN_dkV6TC6z14yqW8ao"}]},{"id":"RtZx6xPdXD_pvXPG2jw2Y","type":"image","data":{"src":"https://vast.ai/uploads/templates/Readme.png","signedSrc":"https://vast.ai/uploads/templates/Readme.png","size":100,"alt":"Readme","caption":"Readme","isUploading":false},"children":[{"text":"","id":"iEav1WxG1NhVvTgJ8_2pf"}]},{"id":"pIN1rvVDT58aINOqtxe9u","type":"h3","children":[{"text":"Step 13 - Choose to Save And/Or Select Your Template","id":"3N18JG9GK67rn5O1N-G7c"}]},{"id":"6EoR2CAQqiv9W9vtySZFY","type":"paragraph","children":[{"text":"A new template will be created when you edit, make changes, and save.","id":"BTodzIU_UoG9CBZPzcUqa"}]},{"id":"G79DY4RnNo5u2Svxh0Q-i","type":"image","data":{"src":"https://vast.ai/uploads/templates/CreateButtons.png","signedSrc":"https://vast.ai/uploads/templates/CreateButtons.png","size":100,"alt":"Createbuttons","caption":"Createbuttons","isUploading":false},"children":[{"text":"","id":"RyjatPSZ3iblLOkMQ8y_u"}]},{"id":"7jV1VunM26Q1u2f2BkmdM","type":"h2","children":[{"text":"[Optional] Download/Upload Files/Backup To Cloud Storage","id":"-c03YSn9RP-2gcDMxypJA"}]},{"id":"K77K1ntsnT3zgeeV68Sbr","type":"paragraph","children":[{"text":"You can download and upload files:\n","id":"GNh-7CONLnYmHXfQN2_Bd"},{"id":"ybukp8oETI7qjCrvpILMf","type":"link","data":{"href":"https://docs.vast.ai/instances/data-movement","newTab":false},"children":[{"text":"Copy Data To/From Instance Storage","id":"f7W_MIVAh9HW1M1kyCTer"}]},{"text":"","id":"LeZN8A-7k97G_g8WZx2-R"}]},{"id":"3QRHOrMcWJEsNHIWBWg4-","type":"paragraph","children":[{"text":"You can also backup data to cloud storage:\n","id":"5FJaj86CsBRESlBfofbPG"},{"id":"3S5gs5LB0g6RYEkeKqf7R","type":"link","data":{"href":"https://docs.vast.ai/instances/cloud-sync","newTab":false},"children":[{"text":"Backup Data to Cloud Storage","id":"kzTCH95lYI4i2ajNKFnKT"}]},{"text":"","id":"G4sj93FT_YtZoda7kA2KU"}]},{"id":"HiHb4QpVSE0xHN_mCvHN4","type":"h2","children":[{"text":"Troubleshooting","id":"jrQQG7gfLXEM7EgFdsU_q"}]},{"id":"64hrMdRSu-98wB06nd3d2","type":"bulleted-list","children":[{"id":"YMhOdowuQGKU5tms50v63","type":"list-item","children":[{"id":"_-73eXbbAz5UEqAEP0k9J","type":"list-item-child","children":[{"text":"If your image is built for a different CPU architecture than your Vast machine, then it won't work. You can try to find a machine with the required CPU architecture using our GUI or CLI. ","id":"irMyTsgICcR77mkbSfVT5"},{"id":"V4hQwrwHlV_ORjH7Q16Eq","type":"link","data":{"href":"https://docs.vast.ai/cli","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"CLI Documentation","id":"Vs-iwtYsj0glOxL2fN8LV"}]},{"text":"  ","id":"FWHQjc7GS88dmOAgYSbv9"},{"id":"P8Ns_yypH4H-pm_lvx3Kn","type":"mention-doc","data":{"label":"Commands","spaceId":"yC9YDSGxNOpyhS0i8A-6f","docId":"LxGnDPZuGf1INi1Sq2Gt0","version":"v2","docAnchorId":"#DtIdq","loadingMethod":"dynamic","overridedLabel":"Command to Search Offers Using CLI"},"children":[{"text":"","id":"sb49PD4aDtqm5pB2rKCdF"}]},{"text":" ","id":"mEdDrF0RKLWoHVNc4zVbN"}]}]},{"id":"eZtPlTF0HQ8pH6c8sEMUg","type":"list-item","children":[{"id":"bN1WQyYntg06NtMIXNe5p","type":"list-item-child","children":[{"text":"If your image requires a higher CUDA version, then look for a machine with a higher Max CUDA version. The Max CUDA version can be found on the instance card. ","id":"7v6jlJYgTIE4Jr_rkPOLm"},{"id":"SiRBhjORz6d9cOzCt7pAh","type":"mention-doc","data":{"label":"Instances Guide","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"v5ZbddMWMtwvGHat6WNu-","version":"v2","docAnchorId":"#arsYL","loadingMethod":"static","overridedLabel":"How to Find Max CUDA version in Instance Card","href":"/instances-guide"},"children":[{"text":"","id":"oUwao9mvE6fqcThNDqBNU"}]},{"text":" ","id":"CSNiGmmdKjSoX4SMJy-0l"}]}]},{"id":"KMiRZiv_sBCyhAiy2GpXv","type":"list-item","children":[{"id":"UsT-ZLsyCzeB1VGx12bIy","type":"list-item-child","children":[{"text":"If your image is built to run jupyter, then try running it on a port different than 8080.","id":"kBKxHeLbf1vVxkSvg7zBB"}]}]},{"id":"rtthafHIUktPO_1chWmNh","type":"list-item","children":[{"id":"lyD9vhJNz4S0IONUQbI8b","type":"list-item-child","children":[{"text":"If you are having issues using ssh launch mode, add your public key to ~/.authorized_keys, install openssh, start openssh when the container starts, and forward the ssh server's port.","id":"qeoTo2DKszUeBsBux2GHg"}]}]}]},{"id":"8erML5DSsqoxM3gkdfOvb","type":"h2","children":[{"text":"Template Examples","id":"wcYKxtFc2a7hTrXE7aaDU"}]},{"id":"Svs3qmB-sHlSQ14CE2cHU","type":"paragraph","children":[{"text":"","id":"fuEzLI_4-uYAxFE7pD_if"},{"id":"9Mj4uMSnimgxjtbagfFAu","type":"link","data":{"href":"https://docs.vast.ai/creating-templates-for-grobid","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Creating Templates for GROBID","id":"15VOYHZt1GhKZH9-yHSlO"}]},{"text":"","id":"7ewACC93bQJClFk0N0hPj"}]}],"metadata":{"type":"doc","version":"v2","convertedDate":"2025-02-21T13:42:13.931Z"}},"version":21,"privacy":"shared with team","shareableToken":"THb7DMwOXMophifKd8yFC","tags":[],"docTags":[],"children":[],"hasDraft":false,"createdByUserId":"DXSM3LlVb4llv9ZkijIZ9","createdBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"lastModifiedByUserId":"e6iVwAuSsPyna9wLwboc9","lastModifiedBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"contributorsDetails":[],"watchers":[],"isArchbeeBrandVisible":false,"customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","updatedAt":"2025-03-05T20:15:46.849Z","createdAt":"2025-02-21T13:42:13.937Z","deletedAt":null,"editable":false,"expanded":true,"reusableContentVariables":[{"contentVariableId":"q3exSma0JEJZI5e1dH_V6","name":"Worker_Groups","content":"Worker Groups","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"UP_Tl2jcgO5gOIMbrO-GS","name":"Endpoints","content":"Endpoints","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"5aHM10OFigKWTwHuB3fMP","name":"PyWorker","content":"PyWorker","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The Vast PyWorker is a Python web server designed to run alongside a machine learning model instance, providing autoscaler compatibility."},{"contentVariableId":"4t0syUbNAbAxpMhfF1SS9","name":"Worker_Group","content":"Worker Group","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"07Mp-kz9OjvJv1gj7w4H2","name":"Endpoint","content":"Endpoint","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"sP383XCx12brqPeq_8qVA","name":"min_cold_workers","content":"min_cold_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The minimum number of workers you want to keep \"cold\" (meaning stopped and fully loaded) when your group has no load."},{"contentVariableId":"ud8V8Q4s-JoB5vW8wVEJS","name":"max_workers","content":"max_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The maximum number of workers your router group can have."}],"reusableContentDocRefs":{},"externalSync":"","contentFromExternalLink":"","leftDoc":{"id":"PUBLISHED-VfosYMdo2IumeiqSJdd2g","children":[],"expanded":false,"name":"Data Movement","urlKey":"data-movement","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},"rightDoc":{"id":"PUBLISHED-aNV7igw-IqgsWPPhf73et","children":[],"expanded":false,"name":"Creating Templates for GROBID","urlKey":"creating-templates-for-grobid","icon":"","parentDocId":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","conditionalRuleId":"","docTags":[]}},"_docSpace":{"id":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","name":"Guides","organizationName":"Vast.ai 2","icon":"","type":"team","publicDocsTree":[{"id":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","isCategory":true,"categoryName":"Overview","gitHubPath":"docs/overview","children":[{"id":"PUBLISHED-704s_lXkTRgEFkRLGeF1p","isCategory":false,"categoryName":"1-introduction","children":[],"expanded":false,"name":"Introduction","urlKey":"","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-aKvlCTuCrf4gl2NR7hO2W","isCategory":false,"categoryName":"2-quickstart","children":[],"expanded":false,"name":"QuickStart","urlKey":"quickstart","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","children":[{"id":"PUBLISHED-_biJv6YaRTqcfsFCsFS50","children":[],"expanded":false,"name":"Instances Help","urlKey":"instances-help","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-PlnZzvcNBmO9p7BEBwDFS","children":[],"expanded":false,"name":"Billing Help","urlKey":"billing-help","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-sVUb5JPCzlvMnKDfU-2Op","children":[],"expanded":false,"name":"Networking","urlKey":"networking","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_0-0A7rQNefJSou1KR6CK","children":[],"expanded":false,"name":"Troubleshooting","urlKey":"troubleshooting","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-VfosYMdo2IumeiqSJdd2g","children":[],"expanded":false,"name":"Data Movement","urlKey":"data-movement","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]}],"name":"FAQ","expanded":false,"urlKey":"faq","icon":"","parentDocId":"PUBLISHED-N2soO3lRFhLBxTEqYfZyO","conditionalRuleId":"","docTags":[]}],"name":"overview","expanded":true,"urlKey":"N2so-overview","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-oocGb1edF0t5aErXyJR2f","isCategory":true,"categoryName":"Use Cases","gitHubPath":"docs/use-cases","children":[{"id":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","children":[{"id":"PUBLISHED-aNV7igw-IqgsWPPhf73et","children":[],"expanded":false,"name":"Creating Templates for GROBID","urlKey":"creating-templates-for-grobid","icon":"","parentDocId":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","conditionalRuleId":"","docTags":[]}],"name":"Creating a Custom Template","expanded":false,"urlKey":"creating-a-custom-template","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-8OgArwW8g6Y_17wGT-SNf","children":[{"id":"PUBLISHED-k-Bha16hWnYinwkb4lD2O","children":[],"expanded":false,"name":"PyTorch","urlKey":"pytorch","icon":"","parentDocId":"PUBLISHED-8OgArwW8g6Y_17wGT-SNf","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI/ML Frameworks","name":"AI/ML Frameworks","expanded":false,"urlKey":"aiml-frameworks","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-FGY05fV4_DdhtOukqQRu7","children":[{"id":"PUBLISHED-jTdh63niYUhgnZqlLJN9y","children":[],"expanded":false,"name":"CUDA","urlKey":"cuda","icon":"","parentDocId":"PUBLISHED-FGY05fV4_DdhtOukqQRu7","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"GPU Programming","name":"GPU Programming","expanded":false,"urlKey":"gpu-programming","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-4TMoTzzxDABXAuieagxQu","children":[{"id":"PUBLISHED-ODY3UoJAhLsqR71AARmJJ","children":[],"expanded":false,"name":"Linux Virtual Desktop","urlKey":"linux-virtual-desktop","icon":"","parentDocId":"PUBLISHED-4TMoTzzxDABXAuieagxQu","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-qklVPbUTSdi2iDW1S8HLN","children":[],"expanded":false,"name":"Linux Virtual Machines","urlKey":"linux-virtual-machines","icon":"","parentDocId":"PUBLISHED-4TMoTzzxDABXAuieagxQu","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Virtual Computing","name":"Virtual Computing","expanded":false,"urlKey":"virtual-computing","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-20FJ-CC_3dkM_s4PSdZ2F","children":[{"id":"PUBLISHED-WPQDtP5RBJyVW-5J1Q2M8","children":[],"expanded":false,"name":"TTS with Nari Labs Dia","urlKey":"tts-with-nari-labs-dia","icon":"","parentDocId":"PUBLISHED-20FJ-CC_3dkM_s4PSdZ2F","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Audio Generation","expanded":false,"name":"AI Audio Generation","urlKey":"ai-audio-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-SHmVLRqlmubft4sblMWsR","children":[{"id":"PUBLISHED-Pe3Za4T6xtQeIXLMP5Qqb","children":[],"expanded":false,"name":"Ollama + Webui","urlKey":"ollama-webui","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-787gRc7SEF6IXK4TzZYoZ","isCategory":false,"categoryName":"1-oobabooga","children":[],"expanded":false,"name":"Oobabooga (LLM webui)","urlKey":"oobabooga-llm-webui","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-p379GpTWJQL7SU2H0AqZk","isCategory":false,"categoryName":"8-tgi-llama3","children":[],"expanded":false,"name":"Huggingface TGI with LLama3","urlKey":"huggingface-tgi-with-llama3","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-jDgLap_KE2zPowqF-tpeu","children":[],"expanded":false,"name":"Quantized GGUF models (cloned)","urlKey":"quantized-gguf-models-cloned","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-6rtLs7cFxc61_uHKG8Ez9","children":[],"expanded":false,"name":"vLLM (LLM inference and serving)","urlKey":"vllm-llm-inference-and-serving","icon":"","parentDocId":"PUBLISHED-SHmVLRqlmubft4sblMWsR","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Text Generation","name":"AI Text Generation","expanded":false,"urlKey":"ai-text-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cit6SDDxsqPFy7W79duf2","children":[{"id":"PUBLISHED-GHt2haaEuiL6GPBQyTBxF","children":[],"expanded":false,"name":"Image Generation","urlKey":"image-generation","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-k7iTLYdjB0Frdv3rQW_ca","children":[],"expanded":false,"name":"Stable Diffusion","urlKey":"stable-diffusion","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-zq3-YW2UEXhITlDCCTg6r","isCategory":false,"categoryName":"3-disco-diffusion","children":[],"expanded":false,"name":"Disco Diffusion","urlKey":"disco-diffusion","icon":"","parentDocId":"PUBLISHED-cit6SDDxsqPFy7W79duf2","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Image Generation","name":"AI Image Generation","expanded":false,"urlKey":"ai-image-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-pB-IyK90u76M-ersF3WVv","children":[{"id":"PUBLISHED-oIHRc63tcSWGEACRRhHkI","children":[],"expanded":false,"name":"Video Generation","urlKey":"video-generation","icon":"","parentDocId":"PUBLISHED-pB-IyK90u76M-ersF3WVv","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"AI Video Generation","expanded":false,"name":"AI Video Generation","urlKey":"ai-video-generation","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--OQJF1kNwwKNJ08fC6Yp-","children":[{"id":"PUBLISHED-ii8e4fy1Tlup9M977dXpb","isCategory":false,"categoryName":"10-serving-infinity","children":[],"expanded":false,"name":"Infinity Embeddings","urlKey":"infinity-embeddings","icon":"","parentDocId":"PUBLISHED--OQJF1kNwwKNJ08fC6Yp-","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Text Embeddings","name":"Text Embeddings","expanded":false,"urlKey":"text-embeddings","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","children":[{"id":"PUBLISHED-mMqv1J1TQtpgiXH-eVm14","children":[],"expanded":false,"name":"Blender in the Cloud","urlKey":"blender-in-the-cloud","icon":"","parentDocId":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-AIPiFyiKn52NGHCH5nq1x","children":[],"expanded":false,"name":"Blender Batch Rendering","urlKey":"blender-batch-rendering","icon":"","parentDocId":"PUBLISHED-YqFqwLYlfEleXBGplVsjc","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"3D Rendering","name":"3D Rendering","expanded":false,"urlKey":"3d-rendering","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-o-tDtwQLB3sjdU5HXlAH4","children":[{"id":"PUBLISHED-1FvJXdy3RgsmFzXR-4Wo2","isCategory":false,"categoryName":"7-mining-on-bittensor","children":[],"expanded":false,"name":"Mining on Bittensor","urlKey":"mining-on-bittensor","icon":"","parentDocId":"PUBLISHED-o-tDtwQLB3sjdU5HXlAH4","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Cryptocurrency","name":"Cryptocurrency","expanded":false,"urlKey":"cryptocurrency","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Yb3VYsvpheOhYYR_1WcII","children":[{"id":"PUBLISHED--X-CVuYaTu3w0xlokb91n","children":[],"expanded":false,"name":"Google Colab","urlKey":"google-colab","icon":"","parentDocId":"PUBLISHED-Yb3VYsvpheOhYYR_1WcII","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Development Tools","name":"Development Tools","expanded":false,"urlKey":"development-tools","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-0E8Al-YdTkoXx5j9keaaQ","children":[{"id":"PUBLISHED-dHQJOGIJkb6T0ZbxrZXSl","children":[],"expanded":false,"name":"Whisper ASR Guide","urlKey":"whisper-asr-guide","icon":"","parentDocId":"PUBLISHED-0E8Al-YdTkoXx5j9keaaQ","conditionalRuleId":"","docTags":[]}],"isCategory":true,"categoryName":"Audio-to-Text","name":"Audio-to-Text","expanded":false,"urlKey":"audio-to-text","icon":"","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","conditionalRuleId":"","docTags":[]}],"name":"Use Cases","isFoldedByDefault":true,"expanded":false,"urlKey":"use-cases","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-bbpobgkuArCgDczPxCtwT","isCategory":true,"categoryName":"Teams","gitHubPath":"docs/team","children":[{"id":"PUBLISHED-xv11ltkqGShfl-mT7XNLu","children":[],"expanded":false,"name":"Teams Overview","urlKey":"teams-overview","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Ynj96qfNyErZfqAwa2LFD","children":[{"id":"PUBLISHED-BXJXoCydqAATNaD288O0B","children":[],"expanded":false,"name":"Edit team","urlKey":"edit-team","icon":"","parentDocId":"PUBLISHED-Ynj96qfNyErZfqAwa2LFD","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Teams Quickstart","urlKey":"teams-quickstart","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-ACyO06JuEKgjfmMjBzaU9","children":[],"expanded":false,"name":"Team Creation","urlKey":"team-creation","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-Hlv-kWiMRQoJ9t01lmNbW","children":[],"expanded":false,"name":"Teams Invitations","urlKey":"teams-invitations","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--XLpXkdTANYzK8ApWif5B","children":[],"expanded":false,"name":"Teams Roles","urlKey":"teams-roles","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-qlS14p3D6OOTphgBEQHOV","children":[],"expanded":false,"name":"Transfer Team Ownership","urlKey":"transfer-team-ownership","icon":"","parentDocId":"PUBLISHED-bbpobgkuArCgDczPxCtwT","conditionalRuleId":"","docTags":[]}],"name":"team","isFoldedByDefault":true,"expanded":false,"urlKey":"team","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","isCategory":true,"categoryName":"Hosting","gitHubPath":"docs/hosting","children":[{"id":"PUBLISHED-m8TTE0HPiTQoEwvtuGXkz","children":[],"expanded":false,"name":"Overview","urlKey":"overview","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cqNh9syMhS3kCV0cBIVEZ","isCategory":false,"categoryName":"2-taxes","children":[],"expanded":false,"name":"Guide to Taxes","urlKey":"guide-to-taxes","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-xal9KdbH6xQ8XSLsN-33N","isCategory":false,"categoryName":"3-datacenter","children":[],"expanded":false,"name":"Datacenter Status","urlKey":"datacenter-status","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-jUSbVYLeXgnYRIW7-27Ol","isCategory":false,"categoryName":"3-payment","children":[],"expanded":false,"name":"Payment","urlKey":"payment","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-VxN0-4JlmoIUdou8WmY1y","isCategory":false,"categoryName":"4-verification-stages","children":[],"expanded":false,"name":"Verification Stages","urlKey":"verification-stages","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-BMs7hJRHIZKzTgQVsYSVB","isCategory":false,"categoryName":"5-vms","children":[],"expanded":false,"name":"VMs","urlKey":"vms","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-1fI7lKmyAWNjt6O0SBcEt","children":[],"expanded":false,"name":"Clusters","urlKey":"clusters","icon":"","parentDocId":"PUBLISHED-gHkBiZPozm7d1nG4jO5sh","conditionalRuleId":"","docTags":[]}],"name":"hosting","isFoldedByDefault":true,"expanded":false,"urlKey":"hosting","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-3tqe-Mndyz9u9qhOMafF7","isCategory":true,"categoryName":"Distributed Computing","gitHubPath":"docs/distributed-computing","children":[{"id":"PUBLISHED-fAVEqWxj7VHiS6pEHzul0","children":[],"name":"Multi-Node training using Torch + NCCL","urlKey":"multi-node-training-using-torch-nccl","icon":"","parentDocId":"PUBLISHED-3tqe-Mndyz9u9qhOMafF7","conditionalRuleId":"","expanded":false,"docTags":[]}],"name":"distributed-computing","isFoldedByDefault":true,"expanded":false,"urlKey":"distributed-computing","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","isCategory":true,"categoryName":"Console","gitHubPath":"docs/console","children":[{"id":"PUBLISHED-QiRgigF76ZPU_lTGkGoLS","isCategory":false,"categoryName":"1-introduction","children":[],"expanded":false,"name":"Introduction","urlKey":"introduction","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-6s1uSe7_teJrmXnaQD36N","isCategory":false,"categoryName":"2-account","children":[],"expanded":false,"name":"Settings","urlKey":"settings","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-3Yf2niu0UjnpDreE9E94e","children":[],"expanded":false,"name":"Keys","urlKey":"keys","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-1of1jlIsPZG-adRkZ-2b9","isCategory":false,"categoryName":"4-cli","children":[],"expanded":false,"name":"CLI","urlKey":"cli","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","children":[{"id":"PUBLISHED-6tmvjeQwgFDHzL8FF7qF_","children":[],"expanded":false,"name":"Instance Portal","urlKey":"instance-portal","icon":"","parentDocId":"PUBLISHED-uUdMWQ-db1piMmtSrnMEF","conditionalRuleId":"","docTags":[]}],"expanded":false,"name":"Templates","urlKey":"templates","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-se59-5_jE_1KHjoVaYm2k","children":[],"expanded":false,"name":"Volumes","urlKey":"volumes","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-BWpjFVMPqBj_DXfYnPOpG","children":[],"expanded":false,"name":"Billing","urlKey":"billing","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-8gVeMK-hQqPNOq_hHuKlK","children":[],"expanded":false,"name":"Earning","urlKey":"earning","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-v5ZbddMWMtwvGHat6WNu-","children":[],"expanded":false,"name":"Instances Guide","urlKey":"instances-guide","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-za2xagq8dMhXLJVRkj6Mh","children":[],"expanded":false,"name":"Search","urlKey":"search","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-s7iBVewEBNHjyaKXGI018","isCategory":false,"categoryName":"referrals","children":[],"expanded":false,"name":"Referral Program","urlKey":"referral-program","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED--7dVUasT1ajlfn9VvagMv","children":[],"expanded":false,"name":"Members","urlKey":"members","icon":"","parentDocId":"PUBLISHED-cpN2WEf_2-MtH46jWX4BU","conditionalRuleId":"","docTags":[]}],"name":"console","isFoldedByDefault":true,"expanded":false,"urlKey":"console","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]},{"id":"PUBLISHED-_wACicNun9GwX_re6EJaE","children":[{"id":"PUBLISHED-UPthhpssnt-ANcq6-Hxwa","children":[],"expanded":false,"name":"RTX 5 Series","urlKey":"jDpX-XUZdy-zKWbAZQPNK","icon":"","parentDocId":"PUBLISHED-_wACicNun9GwX_re6EJaE","conditionalRuleId":"","docTags":[]}],"name":"Specific GPUs","isFoldedByDefault":false,"isCategory":true,"categoryName":"Specific GPUs","expanded":true,"urlKey":"specific-gpus","icon":"","parentDocId":"","conditionalRuleId":"","docTags":[]}],"hostingTitle":"","logoRedirectURL":"","hasPrimaryColorLinks":true,"hostingColor":"#2166ae","darkHostingColor":"#2166ae","secondaryColor":"#e0eefc","darkSecondaryColor":"#14467a","isIndexable":true,"template":"stripe","contentLayout":"two-column","hostname":"docs.vast.ai","hostnamePath":"","proxyDomain":"","publicLogoURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/hY3Z66NYu_wT-evx8EFi5_logo-symbol-dark.svg?format=webp","darkPublicLogoURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/BBMribM7Vqri9n_fLelvV_logo-symbol-light.svg?format=webp","publicTheme":"dark","faviconURL":"https://images.archbee.com/9WtD9F5n1L2IYJxS0qXDd/K8kL9zPy3Yuuym96Ony5l_vast-social-profile-photo.png","spaceLinks":[{"label":"Guides","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","hostnamePath":"","versionLinks":[],"icon":"","hostname":"docs.vast.ai"},{"label":"Instances","docSpaceId":"PUBLISHED-3IG3Byg89UtcvaXwjp66z","icon":"","hostnamePath":"instances","hostname":"docs.vast.ai","versionLinks":[]},{"label":"Serverless","docSpaceId":"PUBLISHED-5WC30cNrS-qdU0I5VeweU","icon":"","hostnamePath":"serverless","hostname":"docs.vast.ai","versionLinks":[]},{"label":"API","docSpaceId":"PUBLISHED-yC9YDSGxNOpyhS0i8A-6f","icon":"","hostnamePath":"api","hostname":"docs.vast.ai","versionLinks":[]}],"versionLinks":[],"externalLinks":[{"label":"Console","url":"https://cloud.vast.ai/","isPrimary":true},{"label":"Discord","url":"https://discord.gg/hSuEbSQ4X8","isPrimary":false}],"landingPageType":"first-doc","landingTemplate":"","landingPageHeaderText":"","landingPageSubheaderText":"","landingHeroBgLightURL":"","landingHeroBgDarkURL":"","footerTemplate":"","headerIncludes":"\u003cscript type=\"text/javascript\"\u003e\nwindow.$crisp=[];\nwindow.CRISP_WEBSITE_ID=\"734d7b1a-86fc-470d-b60a-f6d4840573ae\";\n\n// Set up the ready trigger before loading Crisp\nwindow.CRISP_READY_TRIGGER = function() {\n    // Set current page URL as session data\n    $crisp.push([\"set\", \"session:data\", [[\n        \"current_page\", window.location.href\n    ]]]);\n};\n\n(function(){\n    d=document;\n    s=d.createElement(\"script\");\n    s.src=\"https://client.crisp.chat/l.js\";\n    s.async=1;\n    d.getElementsByTagName(\"head\")[0].appendChild(s);\n})();\n\n// Also track URL changes if you have a single-page application\nwindow.addEventListener('popstate', function() {\n    if (window.$crisp.is(\"website:available\")) {\n        $crisp.push([\"set\", \"session:data\", [[\n            \"current_page\", window.location.href\n        ]]]);\n    }\n});\n\u003c/script\u003e\n\n\u003c!-- Google tag (gtag.js) --\u003e\n\u003cscript async src=\"https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG\"\u003e\u003c/script\u003e\n\u003cscript\u003e\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-DG15WC8WXG');\n\u003c/script\u003e","jwtRedirectURL":"","googleAnalyticsId":"","intercomId":"","isArchbeeBrandVisible":false,"archbeeBrandPosition":"doc-tree","i18nLanguage":"en","showReadTime":true,"showLastUpdate":true,"showThemeSwitcher":true,"showContributors":false,"showDocFeedback":true,"showEditInGitApp":true,"showDocNavigationButtons":true,"revisions":[],"customJS":"","customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","customFont":null,"custom404":"","createdAt":"2025-01-04T02:25:03.718Z","showPdfBookLink":false,"pdfBookLink":"","llmsTxtLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/llms-txt/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-llms.txt","llmsFullTxtLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/llms-txt/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-llms-full.txt","sitemapXmlLink":"https://archbee-doc-uploads.s3.amazonaws.com/export/sitemap-xml/PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q-sitemap.xml","protectionType":"None","isAuthenticated":true,"tagsCache":[],"isQAndAEnabled":false,"isQAndAPublicEnabled":false,"isQAndAAIEnabled":false,"isQAndAApprovalEnabled":false,"isLlmEnabled":true,"isLlmInternal":true,"isLlmExternal":true,"authPageLayout":"one-column","authPagePosition":"left","authBgImageURL":"","authDarkBgImageURL":"","gitAppRepoId":"","gitAppRepo":null,"isGithubEnabled":false,"isBitbucketEnabled":false,"doc":{"id":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","name":"Creating a Custom Template","icon":"","title":"Creating a Custom Template","previewImageURL":"","summary":null,"urlKey":"creating-a-custom-template","description":"","urlAlias":"","data":{"nodes":[{"id":"GiAA75c3lz2wVn33jURUM","type":"h1","children":[{"text":"How To: Create Your Own Template","id":"GJA5dZan_iEAd0qzd5AYw"}]},{"id":"QhiynpaTz2KB7pVfg_Vxf","type":"h2","children":[{"text":"Introduction","id":"pHV8UubnuJJhks_TKgFy_"}]},{"id":"N3ZmfxlNBCxNhUB4vz-Bd","type":"paragraph","children":[{"text":"This guide will help you create custom templates for running containers on Vast.ai. There are three approaches:"}]},{"type":"numbered-list","children":[{"type":"list-item","children":[{"id":"gAj1IG4iljmUa7WatlXWv","type":"list-item-child","data":null,"children":[{"text":"Using PROVISIONING_SCRIPT with recommended templates (simplest)"}]}],"id":"Kyk71sjteBsDKOVb2GO7s"},{"type":"list-item","children":[{"id":"N79ldP2yZxio2FcV82xOa","type":"list-item-child","children":[{"text":"Create Dockerfile from Vast.ai base images (recommended for custom images)"}]}],"id":"4TiAmtLp1TstJDyplJuFu"},{"type":"list-item","children":[{"id":"-oFHB5KGjlhtWxuAeRArE","type":"list-item-child","data":null,"children":[{"text":"Using existing Docker images (with security considerations)"}]}],"id":"DQbf0d-RGzRnTLF4TfGqd"}],"id":"ufvQThG2dw-IoE9HiGDi8"},{"type":"paragraph","children":[{"text":"Vast instances are currently Linux Docker containers and Ubuntu virtual machines. Many Linux based images should work within Vast. ","id":"AXnKtKSkisNTd3e0ZCxvt"}],"id":"15CtphSEDwITtbuty2Umq"},{"id":"77RGdjAWl1H5hApwFZAuW","type":"h2","children":[{"text":"Prerequisites","id":"rILjJ4TmfID-oMTijU1eY"}]},{"id":"zaEBr3kP-jCgL2kBZ78N8","type":"bulleted-list","children":[{"id":"Dt-ZT0no4wKzhBQEvzuwG","type":"list-item","children":[{"id":"fp7vvJ_vh7iRIPBxNVxn_","type":"list-item-child","children":[{"text":"A Vast.ai account","id":"cyDXMVC8B7VX_Nk7YX2qM"}]}]},{"id":"2OgFEOS0zHLbTjfhS4hDi","type":"list-item","children":[{"id":"n-FOw2YDQS3pYyGzf_V_z","type":"list-item-child","children":[{"text":"","id":"XaJcRDZTob6v4RzhBNcWT"},{"id":"drPosWRzag7DfPTAtm7vO","type":"link","data":{"href":"https://cloud.vast.ai/manage-keys/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"SSH client installed on your local machine and SSH public key added in the SSH Keys tab in the Keys section of your console","id":"Xg2wnwlE1JAdejnvWc9iR"}]},{"text":" ","id":"7YNzuUGSmev9K-tV4Xt_N"}]}]},{"id":"kAjnFkHaU4FBwEGCT4dsY","type":"list-item","children":[{"id":"TLcViSzSIn7pV1fgkEdCj","type":"list-item-child","data":null,"children":[{"text":"","id":"RMi-UgPv8g-XfFllcWYeZ"},{"id":"-VMr20OgYD5tPlkj-DaN6","type":"link","data":{"href":"https://docs.vast.ai/cli","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"(Optional) Install and use vast-cli","id":"uiVnEYpcXPw8H2pyEZHsC"}]},{"text":" ","id":"GwUUcAcl6bvbXuBPrXcu7"}]}]}]},{"id":"JqM6i3yZset1V8GzWxdGT","type":"h2","children":[{"text":"Approach 1: Using PROVISIONING_SCRIPT (Simplest)"}]},{"type":"paragraph","children":[{"text":"This approach lets you customize recommended templates without building a custom image."}],"id":"93CrCNdDmQp8UXKIwJA-Z"},{"type":"h3","children":[{"text":"Steps"}],"id":"PV_5KOip_QCOn4DgksG21"},{"type":"numbered-list","children":[{"type":"list-item","children":[{"id":"8kjNk_Q4cJ6qa4MKGJQZF","type":"list-item-child","data":null,"children":[{"text":"Go to the "},{"id":"k6B68kQ3Nixx1q3XIPr38","type":"link","data":{"href":"https://cloud.vast.ai/templates/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Templates tab "}]},{"text":"in Vast.ai interface"}]}],"id":"Ul0MrvhR6KXg09Q6ep1EM"},{"type":"list-item","children":[{"children":[{"text":"Search for \"base-image\" or \"Pytorch\" depending on your needs: "}],"type":"list-item-child","id":"Y6JvKqYueJ1X5aLrU6dKP"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"hzPxMN8sCeI4OucBuEtqT","type":"list-item-child","data":null,"children":[{"text":"vast-ai/base-image is a general purpose image"}]}],"id":"9Xh6mbAejLgsaoCb0LAsu"},{"type":"list-item","children":[{"id":"Jf1wIdyJ-zAwuOUlIN8gh","type":"list-item-child","data":null,"children":[{"text":"vast-ai/pytorch is a base image for working with PyTorch-based applications on Vast"}]}],"id":"hxGiZBrfJ7oos_rpH96Zr"}],"id":"uBq7obi6ghUOo1s-IYSeR"}],"id":"pAd94GLkBMeAWdzJzjsMs"},{"type":"list-item","children":[{"id":"8VaBsfUy0zpo1XU8MdbUE","type":"list-item-child","data":null,"children":[{"text":"Click \"Edit\" on your chosen template"}]}],"id":"tQevDwXURhsni09WjidUi"},{"type":"list-item","children":[{"children":[{"text":"Add the PROVISIONING_SCRIPT environment variable: "}],"type":"list-item-child","id":"V5zWYbLlWgq-wHACD0VOA"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"rxMCrHaKXQKArAffrQmqI","type":"list-item-child","data":null,"children":[{"text":"In the Environment Variables section, add a new variable named \"PROVISIONING_SCRIPT\""}]}],"id":"m_IEs6NG-3KpFNzqMmH6J"},{"type":"list-item","children":[{"id":"mPAzsZLSPYdzIqIwfZ_QN","type":"list-item-child","data":null,"children":[{"text":"The value should be a URL pointing to a shell script (from GitHub, Gist, etc.)"}]},{"id":"AkmjjNqGIjqmjbSFbX6ly","type":"bulleted-list","children":[{"id":"PnIEkSqA6JwVAT_RNH3nQ","type":"list-item","children":[{"id":"t2XsR9st8sOBgi_BXMB46","type":"list-item-child","data":null,"children":[{"text":"If you're specifying a shell script stored in GitHub repo, specify url like "}]}]}]}],"id":"h9hggaWdiZZBVankLAW6f"}],"id":"p6KrYPvEETVnHUhko-zVX"}],"id":"YCeHsVmdCF-88GCvRJkkH"}],"id":"pbMERL1dZgecPEaFirdZ4"},{"id":"kkRK8A7kOmcED0a_k5iWV","type":"code-editor-v2","data":{"languages":[{"id":"Hnj_hz0eFH9WVgTbRiiYE","language":"curl","code":"https://raw.githubusercontent.com/karthik-vast-ai/vast-cli/distributed-inference-integration/provisioning_script.sh7","customLabel":"Url"}],"selectedLanguageId":"Hnj_hz0eFH9WVgTbRiiYE"},"children":[{"text":""}]},{"id":"uIzsNHu-p6IiRWSh3ouN1","type":"numbered-list","children":[{"id":"e-jA1ENsPwwh4sHQ1sQh_","type":"list-item","children":[{"id":"26EaRzEh3-9j7HI5BFDYJ","type":"list-item-child","data":null,"children":[{"text":"Make sure to click \"+\" to add the environment variable"}]}]},{"id":"6wqciJ-MW5HOIVVuQUGGr","type":"list-item","children":[{"id":"HKkj1M5i15CoxXxyi1_-3","type":"list-item-child","data":null,"children":[{"text":"Click Create and Use"}]}]}]},{"id":"G9VIR98TXb8E7yZ2jV0UX","type":"image","data":{"src":"https://archbee-image-uploads.s3.amazonaws.com/DXSM3LlVb4llv9ZkijIZ9-ecTniHpPJjmzK40ccpJ0g-20250211-200512.png","signedSrc":"","size":100,"width":1569,"height":1337,"position":"center","caption":"Add PROVISIONING_SCRIPT variable","alt":""},"children":[{"text":""}]},{"type":"paragraph","children":[{"text":"Example PROVISIONING_SCRIPT:"}],"id":"iFt4pTCAd0Wqguqvc2iBQ"},{"id":"Mvf1bQkh-_-VX6crMCY7O","type":"code-editor-v2","children":[{"text":""}],"data":{"languages":[{"id":"PmBrTmVoVW1vpknI_ltpt","language":"bash","code":"#!/bin/bash\n\n# \ncd /workspace/\n# Cause the script to exit on failure.\nset -eo pipefail\n\n# Activate the main virtual environment\n. /venv/main/bin/activate\n\n# Install your packages\npip install your-packages\n\n# Download some useful files\nwget -P \"${WORKSPACE}/\" https://example.org/my-application.tar.gz\ntar xvf ${WORKSPACE}/my-application.tar.gz\"\n\n# Set up any additional services\necho \"my-supervisor-config\" \u003e /etc/supervisor/conf.d/my-application.conf\necho \"my-supervisor-wrapper\" \u003e /opt/supervisor-scripts/my-application.sh\nchmod +x /opt/supervisor-scripts/my-application.sh\n\n# Reconfigure the instance portal\nrm -f /etc/portal.yaml\nexport PORTAL_CONFIG=\"localhost:1111:11111:/:Instance Portal|localhost:1234:11234:/:My Application\"\n\n# Reload Supervisor\nsupervisorctl reload","customLabel":""}],"selectedLanguageId":"PmBrTmVoVW1vpknI_ltpt"}},{"id":"tvmR_CcHH9NhphlpWvOZp","type":"paragraph","children":[{"text":"This script will run on first boot to set up your environment. All installations should go to /workspace/ for proper persistence."}]},{"type":"h3","children":[{"text":"Configuring Application Access with PORTAL_CONFIG"}],"id":"O0StRqYoPRkIiTGfO1mzZ"},{"type":"paragraph","children":[{"text":"The base-image template includes PORTAL_CONFIG for secure application access management. This environment variable controls how applications are exposed and accessed."}],"id":"lVRk-bGciUxY977VsBkeC"},{"id":"U8rd-RYl0waWTDvlpMGwH","type":"code-editor-v2","children":[{"text":""}],"data":{"languages":[{"id":"NodSj5z7qiyZBNPbcQZqS","language":"bash","code":"hostname:external_port:local_port:url_path:Application Name|hostname:external_port:local_port:url_path:Application Name","customLabel":"PORTAL_CONFIG structure"}],"selectedLanguageId":"NodSj5z7qiyZBNPbcQZqS"}},{"id":"1QXn1n-GzVUrmQsarz8tq","type":"paragraph","children":[{"text":"The structure of this variable is:"}]},{"id":"YrzaAqCZjptizY__tpk0v","type":"bulleted-list","children":[{"id":"078xeouklymALF_pk5nVl","type":"list-item","children":[{"id":"VkuxLy-_xxIDz0wV__8tD","type":"list-item-child","children":[{"text":"Each application is separated by the "},{"text":"|","highlight":true},{"text":" character"}]}]},{"id":"3HsoubZryr454vBnyHDst","type":"list-item","children":[{"id":"by3xR9GhbnPVNi8wA_8NZ","type":"list-item-child","children":[{"text":"Each application parameter is separated by the "},{"text":":","highlight":true},{"text":" character"}]}]},{"id":"0UZn_PVazSD6_WRblYqI3","type":"list-item","children":[{"id":"8pLHuE7Od0zsuUelHxTKZ","type":"list-item-child","children":[{"text":"Each application must specify "},{"text":"hostname:external_port:local_port:url_path:Application Name","highlight":true}]}]}]},{"id":"E15HS6iUiZL8Yzb1ZS-2S","type":"paragraph","data":null,"children":[{"text":"Example:"}]},{"id":"6aqTAbL07lAzR8ysCmutG","type":"code-editor-v2","children":[{"text":""}],"data":{"languages":[{"id":"IGVX85fQm89cuGXjnRber","language":"bash","code":"\"localhost:8002:18002:/hello:MyApp|localhost:1111:11111:/:Instance Portal|localhost:8080:18080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing|localhost:6006:16006:/:Tensorboard\"","customLabel":""}],"selectedLanguageId":"IGVX85fQm89cuGXjnRber"}},{"id":"OQfU5TWGdxTfnjj-BgkFr","type":"paragraph","children":[{"text":"The hostname in Docker instances will always be "},{"text":"localhost","highlight":true}]},{"id":"rDWeWqJ_GQuNOqkAyoK5r","type":"paragraph","children":[{"text":"Where the internal port and local port are not equal then Caddy will be configured to listen on "},{"text":"0.0.0.0:external_port","highlight":true},{"text":" acting as a reverse proxy for "},{"text":"hostname:local_port","highlight":true}]},{"id":"hEnwXKX7-nbCpjTq-L5Or","type":"paragraph","children":[{"text":"If the "},{"text":"external_port","highlight":true},{"text":" and "},{"text":"local_port","highlight":true},{"text":" are equal then Caddy will not act as a proxy but the Instance Portal UI will still create links. This is useful because it allows us to create links to Jupyter which is not controlled by Supervisor in Jupyter Launch mode."}]},{"id":"QGO7CyT7xbNokXqi9aTPH","type":"paragraph","children":[{"text":"url_path","highlight":true},{"text":" will be appended to the instance address and is generally set to "},{"text":"/","highlight":true},{"text":" but can be used to create application deep links."}]},{"id":"XB70M-_PrI9N7fZH_Gcv2","type":"paragraph","children":[{"text":"The "},{"text":"caddy_manager","highlight":true},{"text":" script will write an equivalent config file at "},{"text":"/etc/portal.yaml","highlight":true},{"text":" on boot if it does not already exist. This file can be edited in a running instance."}]},{"id":"OrF0ZKgaR-U3yaTk-Lje5","type":"paragraph","children":[{"text":"Important: When defining multiple links to a single application, only the first should have non equal ports - We cannot proxy one application multiple times."}]},{"id":"TpAb3-0fg9b6MFsBKUKJn","type":"paragraph","children":[{"text":"Note: Instance Portal UI is "},{"text":"not","bold":true},{"text":" required and its own config declaration can be removed from "},{"text":"PORTAL_CONFIG","highlight":true},{"text":". This will not affect the authentication system."}]},{"id":"sRA5Y98IZM3fPRP0wXC4J","type":"h2","children":[{"text":"Approach 2: Create Dockerfile from Vast.ai Base Images"}]},{"type":"h3","children":[{"text":"Why Use Vast.ai Base Images?"}],"id":"iKLJ4K3m82po2eNh3Yrw6"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"RqgULDUS32LLRBiSEN5Rd","type":"list-item-child","data":null,"children":[{"text":"Built-in security features through Caddy proxy"}]}],"id":"rjc-LFZxYvZ5ZrrFjJHd3"},{"type":"list-item","children":[{"id":"MDXnMlEad2A_8hLbbG8jq","type":"list-item-child","data":null,"children":[{"text":"Automatic TLS encryption for web services"}]}],"id":"ltLLrWcox2f_mCd5CammG"},{"type":"list-item","children":[{"id":"T639intDn6FYADeAKQGVG","type":"list-item-child","data":null,"children":[{"text":"Authentication token protection"}]}],"id":"UJ4zcqxzrGUDnzctTgFjw"},{"type":"list-item","children":[{"id":"_Qw61yJhfa1clM5Sj3dr8","type":"list-item-child","data":null,"children":[{"text":"Proper isolation between external and internal services"}]}],"id":"q-C7PBG5sk4THxWkMmCxA"}],"id":"9SGHtbtkMlwXT9cbrurq2"},{"type":"h3","children":[{"text":"Steps"}],"id":"FjnbCdud0fgbpyx-jbPg7"},{"type":"numbered-list","children":[{"type":"list-item","children":[{"id":"eSfAIpOuEz9oPJlkVfpQx","type":"list-item-child","data":null,"children":[{"text":"Start with a "},{"id":"mMpJ9511GBKbUQVxwtyc5","type":"link","data":{"href":"https://hub.docker.com/r/vastai/base-image/tags","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Vast.ai base image"}]},{"text":" or "},{"id":"wPSB7LgNHepSL-D4-ZzTO","type":"link","data":{"href":"https://hub.docker.com/r/vastai/pytorch/tags","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Vast.ai Pytorch base image"}]},{"text":" in your Dockerfile:"}]}],"id":"Zl5dJQl9xptEMGVT89itw"}],"id":"1lXBG-TpanhGLK0irX4H3"},{"id":"4lOAoUtTKGGIENGMcBGfq","type":"code-editor-v2","data":{"languages":[{"id":"HiHRQoDPxO4nHNykwznWp","language":"dockerfile","code":"#For example \nFROM vastai/base-image:cuda-12.6.3-cudnn-devel-ubuntu22.04-py313\n# or\nFROM vastai/pytorch:2.6.0-cuda-12.6.3-py312\n\n# Install your applications into /opt/workspace-internal/\n# This ensures files can be properly synced between instances\nWORKDIR /opt/workspace-internal/\n\n# Activate virtual environment from base image \nRUN . /venv/main/bin/activate\n\nRUN your-installation-commands","customLabel":""}],"selectedLanguageId":"HiHRQoDPxO4nHNykwznWp"},"children":[{"text":""}]},{"id":"9TsfTyDJuIDAeidMQdgfW","type":"bulleted-list","data":null,"children":[{"type":"list-item","children":[{"id":"pqxhOzLwBFQHY14GixnTN","type":"list-item-child","data":null,"children":[{"text":""},{"id":"neD06SAy6eME4m3zdaDzX","type":"link","data":{"href":"https://docs.docker.com/build/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Build"}]},{"text":" and "},{"id":"7hy3IUj5auyIbvroDXNKo","type":"link","data":{"href":"https://docs.docker.com/reference/cli/docker/image/push/","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"push your image"}]},{"text":" to a container registry"}]}],"id":"07xb28ex7Gx8EyNCR6dnl"},{"type":"list-item","children":[{"id":"tcV78MZJveQt4jtBSlena","type":"list-item-child","data":null,"children":[{"text":"Create a new template using your custom image"}]},{"id":"_9JqZduz-czchSiZn3Fsm","type":"bulleted-list","children":[{"id":"udmM2HXHoUjO89ocmoVqY","type":"list-item","children":[{"id":"TnCP34UZ7ifZvylKtRO_y","type":"list-item-child","data":null,"children":[{"text":"See next approach for steps on how to do that"}]}]}]}],"id":"CxX4ozH1sP2LOWuIt0u0e"}]},{"id":"X8MZuFRBltS82MbWMWuiC","type":"h2","children":[{"text":"Approach 3: Create Template Using Existing Docker Images"},{"text":" ","id":"tn4ySw3IQxQBhpxbL_4fu"}]},{"type":"paragraph","children":[{"text":"You can use existing Docker Hub images, but please note:"}],"id":"g506sDm0g3lPYZfzoLRgL"},{"type":"bulleted-list","children":[{"type":"list-item","children":[{"id":"0t6mTuZuFo8sAQhYIouEz","type":"list-item-child","data":null,"children":[{"text":"Running external-facing services without proper security measures is not recommended"}]}],"id":"XJq_JZEdhs_qqsrvVOgIr"},{"type":"list-item","children":[{"id":"TAt7SIdp5mw7lQcQ_uFia","type":"list-item-child","data":null,"children":[{"text":"Consider wrapping the service in Vast.ai's security infrastructure"}]}],"id":"JBWIqCXIuKYoSINq2gmns"}],"id":"gAlvhVPBsgaAf_fEOo3pz"},{"id":"UyI7CJygLaSD7PRAqqBNB","type":"h3","children":[{"text":"Step 1 -  Navigate to the 'Templates' Section","id":"kLkPXIZVZ86mNYvJWzlN-"}]},{"id":"juJgaGznwA8PCG1HjosBV","type":"paragraph","children":[{"text":"Once logged in, click on ","id":"eVzMYhWHSCeqZrGFu9u8j"},{"text":"Templates ","bold":true,"id":"jpND6koFsLXlvPZW15hOd"},{"text":"to go to the template section. This is where you can create, manage, and edit templates.","id":"mHqz083lwVYJaZpobSxTl"}]},{"id":"FU5xZ7O4bQzk49HVmiTM3","type":"h3","children":[{"text":"Step 2 - Create a New Template","id":"GmtOfXRSSg16U0Mrw3l4g"}]},{"id":"S8UMBHKlP6tmqhXygsO5T","type":"paragraph","children":[{"text":"Click the button ","id":"uAGvOtpLKYd0EjeDPanab"},{"text":"+ New","bold":true,"id":"7g0-r0udmAtstW28bFqQZ"},{"text":" to create a new template.\n","id":"_HFUG0gXpRj5qU_OoAJDd"}]},{"id":"WHiisD4H2_W4OUXWqMyQ1","type":"image","data":{"src":"https://vast.ai/uploads/templates/Create.jpg","signedSrc":"https://vast.ai/uploads/templates/Create.jpg","size":100,"alt":"Create","caption":"Create","isUploading":false},"children":[{"text":"","id":"Ke7Ko9VLwhCro5TYpYwmE"}]},{"id":"W00orqzC7aC3gykoT24n-","type":"h3","children":[{"text":"Step 3 - Specify the image path/tag of your docker image","id":"-jPGp-3d3AhK5miTBKZ5j"}]},{"id":"_YumK-t4P1aZgTuJburxR","type":"paragraph","children":[{"text":"In the Image Path Tag, you can add the path/tag of the docker image you wish to use. The Version Tag is where you would set the version.","id":"U0GOP8laX8q5Z-0ntJ8K2"}]},{"id":"o7I6i_CidMoB36LcVIjTn","type":"image","data":{"src":"https://vast.ai/uploads/templates/Image\u0026Tag.png","signedSrc":"https://vast.ai/uploads/templates/Image\u0026Tag.png","size":100,"alt":"Image\u0026Tag","caption":"Image\u0026Tag","isUploading":false},"children":[{"text":"","id":"-muXYi8EPqWIQVyhruisu"}]},{"id":"sfmJBS87J51cTDMzDNCIX","type":"h3","children":[{"text":"Step 4 - Set Environment Variables","id":"J6IM7zE5b4sqzBD8odOvL"}]},{"id":"tgKFq7MgsQke1dpIMBQdv","type":"paragraph","children":[{"text":"You can use the -e option to set environment variables in the Docker Options field by adding something like:","id":"ap_kDld50eVhvhw_i5GgL"}]},{"id":"RIMq957QC-IkeG7JT_s6l","type":"paragraph","children":[{"text":"-e MYVAR1","bold":true,"id":"i4GKKuV1ydiWXUozDusUs"}]},{"id":"1d-VRFlbFZ3b_k1Piw6gw","type":"paragraph","children":[{"text":"You can also add environment variables one by one in the Environment Variables section. These entries will automatically sync with the Docker Options field. ","id":"7Blq-X5ivd2OetAGlL-KA"},{"text":"Make sure to click \"+\"  adding the environment variable and value."}]},{"id":"8FTrM7CqtsUqyI492ARH0","type":"image","data":{"src":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","signedSrc":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","size":100,"alt":"Env\u0026Stuff","caption":"Env\u0026Stuff","isUploading":false},"children":[{"text":"","id":"HyMj-6mG_SM7iQUgkiRpr"}]},{"id":"tHd-oZVHZ_bpyGiSgS8yg","type":"paragraph","children":[{"text":"These options will be appended to the docker run command before\nthe docker run command is executed within one of the machines. Similar to how\nit would be if you were executing \"docker run -e MYVAR1 IMAGE[","id":"IkHrSGh1dEaCLbfpzaT2E"},{"text":"|@DIGEST]\" on your local machine.","id":"Vh_Op48-ciBtMynu9kXL2"}]},{"id":"RQSVwhUIgzfLymPNwP8JJ","type":"paragraph","children":[{"text":"","id":"uFuEyp8GF9P6iH47HCsy8"},{"id":"Y2JRDDN_ZIYtFL2Z5hTUj","type":"link","data":{"href":"https://docs.docker.com/reference/cli/docker/container/run/#env","newTab":false},"children":[{"text":"Docker Run Env Option Documentation","id":"eY6Vuj9TPxeqBjSEmFoIJ"}]},{"text":"","id":"bdOVYyW2A7KSx02Y1miUf"}]},{"id":"d3CG08BGoRpKTRi4tZ_Gf","type":"h3","children":[{"text":"Step 5 - [Optional] Open Custom Ports","id":"p3acPF5xei6cRckoWoA0Z"}]},{"id":"LN5GESca5ldV21ylULklE","type":"paragraph","children":[{"text":"In addition to setting environment variables, you can also open custom ports by adding\n","id":"6Hp4-TLr0Hu7sipvIYC_A"},{"text":"-p ${HOST_MACHINE_PORT}:${CONTAINER_PORT}","bold":true,"id":"NaLx1a82pbma3Mr-6oa5V"},{"text":" to the Docker Options section.","id":"ZVqBnPKYVEDYO_MDk7TPu"}]},{"id":"x3-CR-HzGH9qxwVQ5DsXC","type":"paragraph","children":[{"text":"You can also add them to the Ports section. Once more, this will automatically sync with the Docker Options field.","id":"rSCXYf8kXlByAohj0YeJn"}]},{"id":"V9I5i371u4UeRe0fSJ2yF","type":"image","data":{"src":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","signedSrc":"https://vast.ai/uploads/templates/Env\u0026Stuff.png","size":100,"alt":"Env\u0026Stuff","caption":"Env\u0026Stuff","isUploading":false},"children":[{"text":"","id":"bXK4y4YNvislX9TOojjMx"}]},{"id":"W3lK2RkC8W2KXTryYq82e","type":"paragraph","children":[{"text":"","id":"Nue0kH5lyyBzngyYya65m"},{"id":"1t8U20XSiglRwcb9iecln","type":"mention-doc","data":{"label":"FAQ","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"_fGH0ZcKKlW1NZ3kTTMaL","version":"v2","docAnchorId":"#SrJLn","loadingMethod":"static","overridedLabel":"FAQ: How can I Open Custom Ports","href":"/faq"},"children":[{"text":"","id":"Dn_HF-RSdY6eij6aqxYN9"}]},{"text":" ","id":"vB8rpwUwV5lzQVvJOMw-J"}]},{"id":"iunW8m0BVPCfNWilttVY8","type":"paragraph","children":[{"text":"You can see what's happening inside a container at particular port by finding the public ip address","id":"cE2Tk0DN5I44gg2sjfcEh"},{"text":"\nmapped to that container port.","id":"JcjY5UHxZG-LWuj-3UPoq"}]},{"id":"mJQ9VWp2AS2uYqxiFxtA9","type":"paragraph","children":[{"text":"","id":"XbQ3VIfWDjoyFevQJXem0"},{"id":"-K74sDvawl0tnl7yRdM1y","type":"mention-doc","data":{"label":"Instances Guide","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"v5ZbddMWMtwvGHat6WNu-","version":"v2","docAnchorId":"#dkpT9","loadingMethod":"static","overridedLabel":"How to View Instance's IP Addresses and Open Ports","href":"/instances-guide"},"children":[{"text":"","id":"J08SJZoChuqAeZ7bnNKta"}]},{"text":" ","id":"U74K8lbhSTLfm8_OKwvPY"}]},{"id":"N0gCqfUphaIoX3ePoSucG","type":"paragraph","children":[{"text":"","id":"SMMerft0SoOsQe3k00xw2"},{"id":"bGCpHE1NoThRYrkhqTiIw","type":"mention-doc","data":{"label":"Instances Guide","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"v5ZbddMWMtwvGHat6WNu-","version":"v2","docAnchorId":"#E94QR","loadingMethod":"static","overridedLabel":"More details on How to View Instance's IP Addresses and Open Ports","href":"/instances-guide"},"children":[{"text":"","id":"6lA-ACrORBmvz4Nq8C8dH"}]},{"text":" ","id":"UjTwci8cKyc4tKG0gdrok"}]},{"id":"H00sSBa7XfOru7nvaDJ_E","type":"paragraph","children":[{"text":"Internally, one of the machine's open ports is mapped to this container port.","id":"yw-Ra_VZWGP5gyOMW9yal"}]},{"id":"OkpXf4siL5KwGHSSZTX5d","type":"paragraph","children":[{"text":"You can use ssh to open a ssh connection to the host machine's public ip address","id":"DSv_Agb8uhbtt38gR6hfm"},{"text":".\nThe machine will forward traffic from the host machine's public port to the container port you\nspecified.","id":"5nVxJ2Fhnr8FSoqWPylYg"}]},{"id":"F3wBbC33qnfnY_1gkIyTV","type":"paragraph","children":[{"text":"You can use a ssh command like the one below.","id":"IHjQB4MD9ki8mzKso4V86"}]},{"id":"75cxPZyL11C-SSimX_ZsE","type":"code-editor-v2","data":{"languages":[{"id":"Pnytp_LhzrUSIXFkns90E","language":null,"code":"ssh -p [SSH_PORT] [USER]@[REMOTE_HOST] -L [LOCAL_PORT]:localhost:[REMOTE_PORT]\nssh -p 22 <EMAIL> -L 8080:localhost:8080"}],"selectedLanguageId":"Pnytp_LhzrUSIXFkns90E"},"children":[{"text":"","id":"Lg2Jl6SBRTauKmqTRRSd7"}]},{"id":"kiD4z4jvmVVh0XbmUoM2Q","type":"h3","children":[{"text":"Step 6 - Pick a Launch Mode","id":"sTXLsPGsoi8fn85GLRGDq"}]},{"id":"HuadYnzzinL8HDNXIfTfZ","type":"paragraph","children":[{"text":"You can choose the jupyter, ssh, or entrypoint launch mode depending on your needs.","id":"WxnOAq_ovy4JkHoQracZS"}]},{"id":"YFsoXFSLDdmPljDTux1_M","type":"paragraph","children":[{"text":"","id":"THY7mxEAOV6K0sTLJCNI8"},{"id":"yrw9sMsX9jaXWXdvFxywr","type":"link","data":{"href":"https://docs.vast.ai/instances/launch-modes","newTab":false},"children":[{"text":"Vast Launch Modes","id":"7DJi4vdMPra2cKr2u0F46"}]},{"text":"","id":"0vTqsbMYMsNIcwI5Pou6T"}]},{"id":"TfjpYATZbI36BXgjBYeZD","type":"h3","children":[{"text":"Step 7 - Add Starting Bash Commands to On-start Script Section","id":"-RdGoOPJfIqI6sFNNEjpn"}]},{"id":"2430hQxyE7XQznFs5SIl2","type":"paragraph","children":[{"text":"These commands will be executed as soon the docker container starts, similar to how a bash script be\nwould be executed suppose you created a bash script and added a CMD command in the Dockerfile so Docker would\nexecute this bash script when the container starts.","id":"KSXgXhQy7RRfxcoovxjfc"}]},{"id":"CU2JHnOs7J9ZStY7q7_rA","type":"numbered-list","children":[{"id":"a3Fu6mhgc1v6SxGNs8C2G","type":"list-item","children":[{"id":"nVuIxVJcTVYnyvAOwTzP0","type":"list-item-child","children":[{"text":"Before Vast","id":"XeXqjZKzxqpcqy5huOzo0"}]}]}]},{"id":"9L-CYIYWm-L8UtK_SZmIU","type":"code-editor-v2","data":{"languages":[{"id":"qi5Pv4LhCMxwmxy5kQoFt","language":null,"code":"# Use a base image\nFROM ubuntu:20.04\n\n# Make the script executable\nRUN chmod +x /usr/local/bin/start.sh\n\n# Set the script as the default command\nCMD [\"/usr/local/bin/start.sh\"]"}],"selectedLanguageId":"qi5Pv4LhCMxwmxy5kQoFt"},"children":[{"text":"","id":"Ab5zstrmL7X5PlPyY4UsC"}]},{"id":"QWoeWtyc4p_4uYCPuieyR","type":"paragraph","children":[{"text":"In this case:\nCMD specifies the default command to run when the container starts, which is your start.sh script.","id":"IKlmQeZpqT6WIVqrr0TFQ"}]},{"id":"37k9HCW9hT4TjtZ74z9ih","type":"numbered-list","children":[{"id":"O7DeKpCqPqpFmBU5gKavu","type":"list-item","children":[{"id":"qhuuYatditKn8VC1S72gi","type":"list-item-child","children":[{"text":"In On-Start Script Section With Vast:","id":"ZVxbM_0wIDkoo99UoLMc4"}]}]}]},{"id":"aXWHEj94UrwX9JtdN_72B","type":"code-editor-v2","data":{"languages":[{"id":"st0aUdpkIXIA2yZO1F3v7","language":null,"code":"chmod +x /usr/local/bin/start.sh\nbash /usr/local/bin/start.sh"}],"selectedLanguageId":"st0aUdpkIXIA2yZO1F3v7"},"children":[{"text":"","id":"XvWIBazjxGG4FPesNGFw0"}]},{"id":"MTg9WaYgMKuvdFN6RvcDJ","type":"paragraph","children":[{"text":"You can also try to overwrite existing files built into the image.\nMake sure you can switch to a user that has write permissions to that particular file.","id":"nw1SNZyepuWfn-FEVNa4b"}]},{"id":"A7yKCdiTz7ilSr-IxLZQH","type":"paragraph","children":[{"text":"For example, you can remove all instances of '-sslOnly' in a particular file using sed.","id":"RFsvyTbvwZoRp7jc7ubwD"}]},{"id":"sJCN_XWdtH9ZoJqutvive","type":"code-editor-v2","data":{"languages":[{"id":"bRVNUsP_Z8pXYy57zgE-u","language":null,"code":"sed -i 's/-sslOnly//g' /dockerstartup/vnc_startup.sh"}],"selectedLanguageId":"bRVNUsP_Z8pXYy57zgE-u"},"children":[{"text":"","id":"FucYtfHiZGcCmnXPesgGT"}]},{"id":"P9obi_C-w4uwFB-18AgOs","type":"paragraph","children":[{"text":"You can also make directories.","id":"xuK9onZZbrJ6kUX-j_Ubp"}]},{"id":"3xSiOJYfqjAtwxYfpRLty","type":"code-editor-v2","data":{"languages":[{"id":"xfJyiljO7RjsCWz3dx-DJ","language":null,"code":"sudo -i -u kasm-user mkdir -p /home/<USER>/Desktop"}],"selectedLanguageId":"xfJyiljO7RjsCWz3dx-DJ"},"children":[{"text":"","id":"ocvAHCjfXaXF4uDVYCOLt"}]},{"id":"Cd2_812UNGx9z3qCkCNdG","type":"image","data":{"src":"https://vast.ai/uploads/templates/ExampleOnstart.png","signedSrc":"https://vast.ai/uploads/templates/ExampleOnstart.png","size":100,"alt":"Exampleonstart","caption":"Exampleonstart","isUploading":false},"children":[{"text":"","id":"xVF0_A_Oe4HyT7_xuDU6f"}]},{"id":"8jur0cBZys45ofeSheXps","type":"paragraph","children":[{"text":"Make sure to append environment variables to /etc/environment file in your on-start section because this makes environment variables available to all users and processes and ensures they are persisted even if your instance/docker container is rebooted. This code snippet will take care of this.","id":"WySC5sPL7WYOSyjit7_rj"}]},{"id":"d2_cOwjifZqbtHg83aE3C","type":"code-editor-v2","data":{"languages":[{"id":"grad92VGQy3TYTAlAgLom","language":null,"code":"env \u003e\u003e /etc/environment"}],"selectedLanguageId":"grad92VGQy3TYTAlAgLom"},"children":[{"text":"","id":"JY2spcuYEIAY0fJzOepIZ"}]},{"id":"FmJ-mp6yajH9op05Zugg7","type":"paragraph","children":[{"text":"Also make sure to find the image’s ENTRYPOINT or CMD command and call that command at the end of the on-start section. We overwrite that command to set up jupyter/ssh server, etc. under the hood.","id":"fpqnNM7hpD2ceMXNw3eBZ"}]},{"id":"sDcuOGC9f5KBvaH6tJtEm","type":"h3","children":[{"text":"Step 8 - Specify Docker Repository Server Name if Necessary","id":"5MEwtX8qNVH36TH-V7rzh"}]},{"id":"lqz5x_DRQh-I16r8HQLoO","type":"paragraph","children":[{"text":"You don't have to specify docker.io as the server name if your repository is Docker Hub.\nDocker automatically uses docker.io to pull the image if no other registry is specified.","id":"wroRpnNA5raSkeqSaU7bb"}]},{"id":"eSQiJsx21BnoGaNpQW1Tp","type":"paragraph","children":[{"text":"You do have to specify your server name if your repository is something else.","id":"2c8lXDea7q3PRp2nwCLHi"}]},{"id":"NdTto4HGtQJusHr-2GVW5","type":"paragraph","children":[{"text":"For example,","id":"s5DoVN1-o3-MPOBTJ8EV5"}]},{"id":"pn4Cufd45hP0ZOdUJlGBs","type":"paragraph","children":[{"text":"GitHub Container Registry (GHCR) - Server Name: ghcr.io","id":"J5dK-Tgj-2y0LLI3JH0PE"}]},{"id":"iH9KnwFl7gOb9xTbn_-4_","type":"paragraph","children":[{"text":"Google Container Registry (GCR) - Server Name: gcr.io","id":"4Dt4LcOXN1QlIXOmVXkII"}]},{"id":"KjeVxsKcYll2yA-UA75NK","type":"h3","children":[{"text":"Step 9 - Specify Docker Login Username and Password","id":"sq4ecr3tK9Uzk5FODCrpx"}]},{"id":"Fe9M1_hMpHGaZKnNvIjdK","type":"paragraph","children":[{"text":"We will have the same character set for docker login usernames and passwords as Docker soon.","id":"DSiE8aHGPNEqe7u5HAyJ7"}]},{"id":"xVaECxUzQhSMOqCw6_ZhT","type":"image","data":{"src":"https://vast.ai/uploads/templates/DockerLogin.png","signedSrc":"https://vast.ai/uploads/templates/DockerLogin.png","size":100,"alt":"Dockerlogin","caption":"Dockerlogin","isUploading":false},"children":[{"text":"","id":"4dSexir47lLaG1AJCE-LQ"}]},{"id":"1Sb8YB7CN2pHjljan9Qo7","type":"h3","children":[{"text":"Step 10 - Specify Template Name and Description","id":"MNcys80Q34XmqloLnJud3"}]},{"id":"lYQMdtpTOZ9eytmewkPp-","type":"image","data":{"src":"https://vast.ai/uploads/templates/TemplateName.png","signedSrc":"https://vast.ai/uploads/templates/TemplateName.png","size":100,"alt":"Templatename","caption":"Templatename","isUploading":false},"children":[{"text":"","id":"0vZ4z-wM5Pb5zchJHCMIj"}]},{"id":"YHDrASsDwWm1QBvo-9hwZ","type":"h3","children":[{"text":"Step 11- Select the Amount of Disk Space You Want","id":"AL-UifE_8eGSET0UxfqZa"}]},{"id":"88ndFWIhCoe4C70AuKZ53","type":"image","data":{"src":"https://vast.ai/uploads/templates/DiskSpace2.png","signedSrc":"https://vast.ai/uploads/templates/DiskSpace2.png","size":100,"alt":"Diskspace2","caption":"Diskspace2","isUploading":false},"children":[{"text":"","id":"LYsc2tFC2NA4u6d2fqitI"}]},{"id":"H1aVbbOtZhY_czzQivPic","type":"h3","children":[{"text":"Step 12 - Fill Out Accessibility Section","id":"3rfR3I5cf3U9Px8Oq_CYF"}]},{"id":"ziRQmRvo9SKbA7NxHFAdG","type":"paragraph","children":[{"text":"Choose whether to include ReadMe, make template public, and\nfill out readme if you desire.","id":"ggrN_dkV6TC6z14yqW8ao"}]},{"id":"RtZx6xPdXD_pvXPG2jw2Y","type":"image","data":{"src":"https://vast.ai/uploads/templates/Readme.png","signedSrc":"https://vast.ai/uploads/templates/Readme.png","size":100,"alt":"Readme","caption":"Readme","isUploading":false},"children":[{"text":"","id":"iEav1WxG1NhVvTgJ8_2pf"}]},{"id":"pIN1rvVDT58aINOqtxe9u","type":"h3","children":[{"text":"Step 13 - Choose to Save And/Or Select Your Template","id":"3N18JG9GK67rn5O1N-G7c"}]},{"id":"6EoR2CAQqiv9W9vtySZFY","type":"paragraph","children":[{"text":"A new template will be created when you edit, make changes, and save.","id":"BTodzIU_UoG9CBZPzcUqa"}]},{"id":"G79DY4RnNo5u2Svxh0Q-i","type":"image","data":{"src":"https://vast.ai/uploads/templates/CreateButtons.png","signedSrc":"https://vast.ai/uploads/templates/CreateButtons.png","size":100,"alt":"Createbuttons","caption":"Createbuttons","isUploading":false},"children":[{"text":"","id":"RyjatPSZ3iblLOkMQ8y_u"}]},{"id":"7jV1VunM26Q1u2f2BkmdM","type":"h2","children":[{"text":"[Optional] Download/Upload Files/Backup To Cloud Storage","id":"-c03YSn9RP-2gcDMxypJA"}]},{"id":"K77K1ntsnT3zgeeV68Sbr","type":"paragraph","children":[{"text":"You can download and upload files:\n","id":"GNh-7CONLnYmHXfQN2_Bd"},{"id":"ybukp8oETI7qjCrvpILMf","type":"link","data":{"href":"https://docs.vast.ai/instances/data-movement","newTab":false},"children":[{"text":"Copy Data To/From Instance Storage","id":"f7W_MIVAh9HW1M1kyCTer"}]},{"text":"","id":"LeZN8A-7k97G_g8WZx2-R"}]},{"id":"3QRHOrMcWJEsNHIWBWg4-","type":"paragraph","children":[{"text":"You can also backup data to cloud storage:\n","id":"5FJaj86CsBRESlBfofbPG"},{"id":"3S5gs5LB0g6RYEkeKqf7R","type":"link","data":{"href":"https://docs.vast.ai/instances/cloud-sync","newTab":false},"children":[{"text":"Backup Data to Cloud Storage","id":"kzTCH95lYI4i2ajNKFnKT"}]},{"text":"","id":"G4sj93FT_YtZoda7kA2KU"}]},{"id":"HiHb4QpVSE0xHN_mCvHN4","type":"h2","children":[{"text":"Troubleshooting","id":"jrQQG7gfLXEM7EgFdsU_q"}]},{"id":"64hrMdRSu-98wB06nd3d2","type":"bulleted-list","children":[{"id":"YMhOdowuQGKU5tms50v63","type":"list-item","children":[{"id":"_-73eXbbAz5UEqAEP0k9J","type":"list-item-child","children":[{"text":"If your image is built for a different CPU architecture than your Vast machine, then it won't work. You can try to find a machine with the required CPU architecture using our GUI or CLI. ","id":"irMyTsgICcR77mkbSfVT5"},{"id":"V4hQwrwHlV_ORjH7Q16Eq","type":"link","data":{"href":"https://docs.vast.ai/cli","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"CLI Documentation","id":"Vs-iwtYsj0glOxL2fN8LV"}]},{"text":"  ","id":"FWHQjc7GS88dmOAgYSbv9"},{"id":"P8Ns_yypH4H-pm_lvx3Kn","type":"mention-doc","data":{"label":"Commands","spaceId":"yC9YDSGxNOpyhS0i8A-6f","docId":"LxGnDPZuGf1INi1Sq2Gt0","version":"v2","docAnchorId":"#DtIdq","loadingMethod":"dynamic","overridedLabel":"Command to Search Offers Using CLI"},"children":[{"text":"","id":"sb49PD4aDtqm5pB2rKCdF"}]},{"text":" ","id":"mEdDrF0RKLWoHVNc4zVbN"}]}]},{"id":"eZtPlTF0HQ8pH6c8sEMUg","type":"list-item","children":[{"id":"bN1WQyYntg06NtMIXNe5p","type":"list-item-child","children":[{"text":"If your image requires a higher CUDA version, then look for a machine with a higher Max CUDA version. The Max CUDA version can be found on the instance card. ","id":"7v6jlJYgTIE4Jr_rkPOLm"},{"id":"SiRBhjORz6d9cOzCt7pAh","type":"mention-doc","data":{"label":"Instances Guide","spaceId":"Tmi8J7TXAqj2ZmTd6RJ3q","docId":"v5ZbddMWMtwvGHat6WNu-","version":"v2","docAnchorId":"#arsYL","loadingMethod":"static","overridedLabel":"How to Find Max CUDA version in Instance Card","href":"/instances-guide"},"children":[{"text":"","id":"oUwao9mvE6fqcThNDqBNU"}]},{"text":" ","id":"CSNiGmmdKjSoX4SMJy-0l"}]}]},{"id":"KMiRZiv_sBCyhAiy2GpXv","type":"list-item","children":[{"id":"UsT-ZLsyCzeB1VGx12bIy","type":"list-item-child","children":[{"text":"If your image is built to run jupyter, then try running it on a port different than 8080.","id":"kBKxHeLbf1vVxkSvg7zBB"}]}]},{"id":"rtthafHIUktPO_1chWmNh","type":"list-item","children":[{"id":"lyD9vhJNz4S0IONUQbI8b","type":"list-item-child","children":[{"text":"If you are having issues using ssh launch mode, add your public key to ~/.authorized_keys, install openssh, start openssh when the container starts, and forward the ssh server's port.","id":"qeoTo2DKszUeBsBux2GHg"}]}]}]},{"id":"8erML5DSsqoxM3gkdfOvb","type":"h2","children":[{"text":"Template Examples","id":"wcYKxtFc2a7hTrXE7aaDU"}]},{"id":"Svs3qmB-sHlSQ14CE2cHU","type":"paragraph","children":[{"text":"","id":"fuEzLI_4-uYAxFE7pD_if"},{"id":"9Mj4uMSnimgxjtbagfFAu","type":"link","data":{"href":"https://docs.vast.ai/creating-templates-for-grobid","newTab":true,"hasDisabledNofollow":false},"children":[{"text":"Creating Templates for GROBID","id":"15VOYHZt1GhKZH9-yHSlO"}]},{"text":"","id":"7ewACC93bQJClFk0N0hPj"}]}],"metadata":{"type":"doc","version":"v2","convertedDate":"2025-02-21T13:42:13.931Z"}},"version":21,"privacy":"shared with team","shareableToken":"THb7DMwOXMophifKd8yFC","tags":[],"docTags":[],"children":[],"hasDraft":false,"createdByUserId":"DXSM3LlVb4llv9ZkijIZ9","createdBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"lastModifiedByUserId":"e6iVwAuSsPyna9wLwboc9","lastModifiedBy":{"id":"","fullName":"","firstName":"","lastName":"","email":"","profilePhotoURL":""},"contributorsDetails":[],"watchers":[],"isArchbeeBrandVisible":false,"customCSS":"\u003cstyle\u003e\n  /* Adjust chat button position */\n  .crisp-client {\n    --crisp-position-reverse: 1 !important; /* Left side positioning */\n    --crisp-button-color: #4B5563 !important; /* Custom button color */\n  }\n  \n  \n  \n  /* Optional: Hide chat widget on mobile */\n  @media (max-width: 768px) {\n    .crisp-client {\n      display: none !important;\n    }\n  }\n\u003c/style\u003e","parentDocId":"PUBLISHED-oocGb1edF0t5aErXyJR2f","docSpaceId":"PUBLISHED-Tmi8J7TXAqj2ZmTd6RJ3q","updatedAt":"2025-03-05T20:15:46.849Z","createdAt":"2025-02-21T13:42:13.937Z","deletedAt":null,"editable":false,"expanded":true,"reusableContentVariables":[{"contentVariableId":"q3exSma0JEJZI5e1dH_V6","name":"Worker_Groups","content":"Worker Groups","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"UP_Tl2jcgO5gOIMbrO-GS","name":"Endpoints","content":"Endpoints","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"5aHM10OFigKWTwHuB3fMP","name":"PyWorker","content":"PyWorker","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The Vast PyWorker is a Python web server designed to run alongside a machine learning model instance, providing autoscaler compatibility."},{"contentVariableId":"4t0syUbNAbAxpMhfF1SS9","name":"Worker_Group","content":"Worker Group","scope":"global","spaceTargetMap":{},"type":"glossary","description":"A lower level organization that lives within an Endpoint. It consists of a template (with extra filters for search), a set of GPU instances (workers) created from that template, and hyperparameters."},{"contentVariableId":"07Mp-kz9OjvJv1gj7w4H2","name":"Endpoint","content":"Endpoint","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The highest level clustering of instances for the autoscaler, consisting of a named endpoint string, a collection of Worker groups, and hyperparameters."},{"contentVariableId":"sP383XCx12brqPeq_8qVA","name":"min_cold_workers","content":"min_cold_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The minimum number of workers you want to keep \"cold\" (meaning stopped and fully loaded) when your group has no load."},{"contentVariableId":"ud8V8Q4s-JoB5vW8wVEJS","name":"max_workers","content":"max_workers","scope":"global","spaceTargetMap":{},"type":"glossary","description":"The maximum number of workers your router group can have."}],"reusableContentDocRefs":{},"externalSync":"","contentFromExternalLink":"","leftDoc":{"id":"PUBLISHED-VfosYMdo2IumeiqSJdd2g","children":[],"expanded":false,"name":"Data Movement","urlKey":"data-movement","icon":"","parentDocId":"PUBLISHED-_fGH0ZcKKlW1NZ3kTTMaL","conditionalRuleId":"","docTags":[]},"rightDoc":{"id":"PUBLISHED-aNV7igw-IqgsWPPhf73et","children":[],"expanded":false,"name":"Creating Templates for GROBID","urlKey":"creating-templates-for-grobid","icon":"","parentDocId":"PUBLISHED-_Gwk9sUltxMJLN7sJTSml","conditionalRuleId":"","docTags":[]}},"lastPublishedToPreview":"2025-07-08T00:23:45.674Z","lastPublishedToProduction":"2025-07-08T19:41:39.988Z","customQAndAModalDescription":"","customEndingSignature":"","customForgotPasswordMessage":"","customNewAccountMessage":"","customEmailSenderName":"","autoPublishInterval":null,"autoPublishLastRun":null,"autoPublishEnabled":false,"isPublicSubscribeEnabled":false,"isCategoryPageDataEnabled":false,"portalId":"lOjtkBF-PmAqJkTBobhAY","showFullTitleInLeftNavDocsTree":false},"isHosted":true,"isMobile":false,"headerIncludes":"\u003cscript type=\"text/javascript\"\u003e\nwindow.$crisp=[];\nwindow.CRISP_WEBSITE_ID=\"734d7b1a-86fc-470d-b60a-f6d4840573ae\";\n\n// Set up the ready trigger before loading Crisp\nwindow.CRISP_READY_TRIGGER = function() {\n    // Set current page URL as session data\n    $crisp.push([\"set\", \"session:data\", [[\n        \"current_page\", window.location.href\n    ]]]);\n};\n\n(function(){\n    d=document;\n    s=d.createElement(\"script\");\n    s.src=\"https://client.crisp.chat/l.js\";\n    s.async=1;\n    d.getElementsByTagName(\"head\")[0].appendChild(s);\n})();\n\n// Also track URL changes if you have a single-page application\nwindow.addEventListener('popstate', function() {\n    if (window.$crisp.is(\"website:available\")) {\n        $crisp.push([\"set\", \"session:data\", [[\n            \"current_page\", window.location.href\n        ]]]);\n    }\n});\n\u003c/script\u003e\n\n\u003c!-- Google tag (gtag.js) --\u003e\n\u003cscript async src=\"https://www.googletagmanager.com/gtag/js?id=G-DG15WC8WXG\"\u003e\u003c/script\u003e\n\u003cscript\u003e\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-DG15WC8WXG');\n\u003c/script\u003e","passwordChangeToken":"","jwt":"","reloadAuth":false,"isBot":true,"isWidget":false,"template":"","docs":[],"search":""}},"page":"/public/[[...slug]]","query":{"slug":["","%2Fcreating-a-custom-template"],"hostname":"docs.vast.ai","isHosted":"true","pdfExport":"","paginationFromPdf":"","paginationToPdf":"","paginationLimitPdf":"","showToC":"","shareableToken":"","passwordChangeToken":"","search":"","template":"","jwt":"","isWidget":"false","tabNav":"","reload":"false"},"buildId":"dBe88brPNkUEoyQSYuIle","assetPrefix":"https://cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2","runtimeConfig":{"NEXT_PUBLIC_ENV_ID":"app.archbee.com","NEXT_PUBLIC_ENV":"live","NEXT_PUBLIC_DOMAIN":"app.archbee.com","NEXT_PUBLIC_ASSET_PREFIX":"https://cdn.archbee.com/96c7914b49966e400b4f53ac904fa80945d640a2","NEXT_PUBLIC_VERSION":"96c7914b49966e400b4f53ac904fa80945d640a2","NEXT_PUBLIC_BUILD_DATE":"2025-07-11T11:21:52.302Z","NEXT_RTS_DOMAIN":"rts.archbee.com"},"isFallback":false,"isExperimentalCompile":false,"dynamicIds":[5810,79658,2541],"gip":true,"scriptLoader":[]}</script><script defer src="../external.html?link=https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"95e062d2b49916d1","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.6.2","token":"cd97f360f847483b94d308aff2d8fe85"}' crossorigin="anonymous"></script>
</body>
<!-- Mirrored from docs.vast.ai/creating-a-custom-template by HTTrack Website Copier/3.x [XR&CO'2014], Sat, 12 Jul 2025 12:01:27 GMT -->
</html>