{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../../src/lib/security.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAU/B,MAAM,CAAN,IAAY,iBAGX;AAHD,WAAY,iBAAiB;IAC3B,8CAAyB,CAAA;IACzB,4EAAuD,CAAA;AACzD,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,QAG5B;AAED,MAAM,OAAO,aAAc,SAAQ,KAAK;IACtC,YACS,IAAuB,EAC9B,OAAe;QAEf,KAAK,CAAC,OAAO,CAAC,CAAC;QAHR,SAAI,GAAJ,IAAI,CAAmB;QAI9B,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,UAAU;QACf,OAAO,IAAI,aAAa,CACtB,iBAAiB,CAAC,UAAU,EAC5B,iEAAiE,CAClE,CAAC;IACJ,CAAC;IACD,MAAM,CAAC,gBAAgB,CAAC,IAAY;QAClC,OAAO,IAAI,aAAa,CACtB,iBAAiB,CAAC,wBAAwB,EAC1C,+BAA+B,IAAI,EAAE,CACtC,CAAC;IACJ,CAAC;CACF;AA6ED,MAAM,UAAU,eAAe,CAC7B,GAAG,OAA0B;IAE7B,MAAM,KAAK,GAAkB;QAC3B,KAAK,EAAE,EAAE;QACT,OAAO,EAAE,EAAE;QACX,WAAW,EAAE,EAAE;QACf,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;KACzB,CAAC;IAEF,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;QACnC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;YACtB,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;YACf,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBACnC,OAAO,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC;YAC9D,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBACxC,OAAO,CACL,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CACzC,CAAC;YACJ,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,2BAA2B,EAAE,CAAC;gBAClD,IAAI,OAAO,CAAC,CAAC,KAAK,IAAI,QAAQ,EAAE,CAAC;oBAC/B,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACnB,CAAC;gBACD,OAAO,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC;YAClE,CAAC;iBAAM,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,+BAA+B,CAAC,CAAC,IAAI,iBAAiB,OAAO,CAAC;qBAC3D,KAAK,GAAG,CACZ,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QACtB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QAEtB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,eAAe;gBAClB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3C,MAAM;YACR,KAAK,cAAc;gBACjB,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC/C,MAAM;YACR,KAAK,eAAe;gBAClB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3C,MAAM;YACR,KAAK,YAAY;gBACf,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACxB,MAAM;YACR,KAAK,aAAa;gBAChB,MAAM;YACR,KAAK,aAAa;gBAChB,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,QAAQ;gBACX,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,iBAAiB;gBACpB,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,2BAA2B;gBAC9B,MAAM;YACR,KAAK,eAAe;gBAClB,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACzB,MAAM;YACR;gBACE,IAAoB,CAAC;gBACrB,MAAM,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,UAAU,CACjB,KAAoB,EACpB,IAAwB;IAExB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO;IACT,CAAC;IAED,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,CAAC;AAED,SAAS,WAAW,CAClB,KAAoB,EACpB,IAI0C;IAE1C,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAClD,OAAO;IACT,CAAC;IAED,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE,CAAC;QAClD,KAAK,GAAG,UAAU,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QACjC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;IACxC,CAAC;AACH,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,QAAyD;IAEzD,OAAO,eAAe,CACpB;QACE;YACE,SAAS,EAAE,eAAe;YAC1B,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,QAAQ,EAAE,UAAU,IAAI,GAAG,EAAE,CAAC,oBAAoB;SAC1D;KACF,CACF,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,eAAe,CAEnC,GAAuC;IACvC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,OAAO;IACT,CAAC;IAED,OAAO,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;AACjD,CAAC"}