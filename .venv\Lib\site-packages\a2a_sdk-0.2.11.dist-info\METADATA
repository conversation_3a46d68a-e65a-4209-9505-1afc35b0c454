Metadata-Version: 2.4
Name: a2a-sdk
Version: 0.2.11
Summary: A2A Python SDK
Project-URL: homepage, https://a2aproject.github.io/A2A/
Project-URL: repository, https://github.com/a2aproject/a2a-python
Project-URL: changelog, https://github.com/a2aproject/a2a-python/blob/main/CHANGELOG.md
Project-URL: documentation, https://a2aproject.github.io/A2A/sdk/python/
Author-email: Google LLC <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE
Keywords: A2A,A2A Protocol,A2A SDK,Agent 2 Agent,Agent2Agent
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.10
Requires-Dist: fastapi>=0.115.2
Requires-Dist: google-api-core>=1.26.0
Requires-Dist: grpcio-reflection>=1.7.0
Requires-Dist: grpcio-tools>=1.60
Requires-Dist: grpcio>=1.60
Requires-Dist: httpx-sse>=0.4.0
Requires-Dist: httpx>=0.28.1
Requires-Dist: opentelemetry-api>=1.33.0
Requires-Dist: opentelemetry-sdk>=1.33.0
Requires-Dist: protobuf==5.29.5
Requires-Dist: pydantic>=2.11.3
Requires-Dist: sse-starlette
Requires-Dist: starlette
Provides-Extra: mysql
Requires-Dist: sqlalchemy[aiomysql,asyncio]>=2.0.0; extra == 'mysql'
Provides-Extra: postgresql
Requires-Dist: sqlalchemy[asyncio,postgresql-asyncpg]>=2.0.0; extra == 'postgresql'
Provides-Extra: sql
Requires-Dist: sqlalchemy[aiomysql,aiosqlite,asyncio,postgresql-asyncpg]>=2.0.0; extra == 'sql'
Provides-Extra: sqlite
Requires-Dist: sqlalchemy[aiosqlite,asyncio]>=2.0.0; extra == 'sqlite'
Description-Content-Type: text/markdown

# A2A Python SDK

[![License](https://img.shields.io/badge/License-Apache_2.0-blue.svg)](LICENSE)
[![PyPI version](https://img.shields.io/pypi/v/a2a-sdk)](https://pypi.org/project/a2a-sdk/)
![PyPI - Python Version](https://img.shields.io/pypi/pyversions/a2a-sdk)
[![PyPI - Downloads](https://img.shields.io/pypi/dw/a2a-sdk)](https://pypistats.org/packages/a2a-sdk)

<!-- markdownlint-disable no-inline-html -->

<html>
   <h2 align="center">
   <img src="https://raw.githubusercontent.com/a2aproject/A2A/refs/heads/main/docs/assets/a2a-logo-black.svg" width="256" alt="A2A Logo"/>
   </h2>
   <h3 align="center">A Python library that helps run agentic applications as A2AServers following the <a href="https://a2aproject.github.io/A2A">Agent2Agent (A2A) Protocol</a>.</h3>
</html>

<!-- markdownlint-enable no-inline-html -->

## Installation

You can install the A2A SDK using either `uv` or `pip`.

## Prerequisites

- Python 3.10+
- `uv` (optional, but recommended) or `pip`

### Using `uv`

When you're working within a uv project or a virtual environment managed by uv, the preferred way to add packages is using uv add.

```bash
uv add a2a-sdk
```

To install with database support:

```bash
# PostgreSQL support
uv add "a2a-sdk[postgresql]"

# MySQL support
uv add "a2a-sdk[mysql]"

# SQLite support
uv add "a2a-sdk[sqlite]"

# All database drivers
uv add "a2a-sdk[sql]"
```

### Using `pip`

If you prefer to use pip, the standard Python package installer, you can install `a2a-sdk` as follows

```bash
pip install a2a-sdk
```

To install with database support:

```bash
# PostgreSQL support
pip install "a2a-sdk[postgresql]"

# MySQL support
pip install "a2a-sdk[mysql]"

# SQLite support
pip install "a2a-sdk[sqlite]"

# All database drivers
pip install "a2a-sdk[sql]"
```

## Examples

### [Helloworld Example](https://github.com/a2aproject/a2a-samples/tree/main/samples/python/agents/helloworld)

1. Run Remote Agent

   ```bash
   git clone https://github.com/a2aproject/a2a-samples.git
   cd a2a-samples/samples/python/agents/helloworld
   uv run .
   ```

2. In another terminal, run the client

   ```bash
   cd a2a-samples/samples/python/agents/helloworld
   uv run test_client.py
   ```

3. You can validate your agent using the agent inspector. Follow the instructions at the [a2a-inspector](https://github.com/a2aproject/a2a-inspector) repo.

You can also find more Python samples [here](https://github.com/a2aproject/a2a-samples/tree/main/samples/python) and JavaScript samples [here](https://github.com/a2aproject/a2a-samples/tree/main/samples/js).

## License

This project is licensed under the terms of the [Apache 2.0 License](https://raw.githubusercontent.com/a2aproject/a2a-python/refs/heads/main/LICENSE).

## Contributing

See [CONTRIBUTING.md](https://github.com/a2aproject/a2a-python/blob/main/CONTRIBUTING.md) for contribution guidelines.
