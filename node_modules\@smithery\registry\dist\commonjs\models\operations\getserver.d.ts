import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type GetServerRequest = {
    /**
     * Qualified name of the server (e.g., 'exa')
     */
    qualifiedName: string;
};
/** @internal */
export declare const GetServerRequest$inboundSchema: z.ZodType<GetServerRequest, z.ZodTypeDef, unknown>;
/** @internal */
export type GetServerRequest$Outbound = {
    qualifiedName: string;
};
/** @internal */
export declare const GetServerRequest$outboundSchema: z.ZodType<GetServerRequest$Outbound, z.ZodTypeDef, GetServerRequest>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace GetServerRequest$ {
    /** @deprecated use `GetServerRequest$inboundSchema` instead. */
    const inboundSchema: z.ZodType<GetServerRequest, z.ZodTypeDef, unknown>;
    /** @deprecated use `GetServerRequest$outboundSchema` instead. */
    const outboundSchema: z.ZodType<GetServerRequest$Outbound, z.ZodTypeDef, GetServerRequest>;
    /** @deprecated use `GetServerRequest$Outbound` instead. */
    type Outbound = GetServerRequest$Outbound;
}
export declare function getServerRequestToJSON(getServerRequest: GetServerRequest): string;
export declare function getServerRequestFromJSON(jsonString: string): SafeParseResult<GetServerRequest, SDKValidationError>;
//# sourceMappingURL=getserver.d.ts.map