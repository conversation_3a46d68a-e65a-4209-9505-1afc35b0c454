import * as z from "zod";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
export type Pagination = {
    /**
     * Current page number
     */
    currentPage: number;
    /**
     * Number of items per page
     */
    pageSize: number;
    /**
     * Total number of pages
     */
    totalPages: number;
    /**
     * Total number of items
     */
    totalCount: number;
};
/** @internal */
export declare const Pagination$inboundSchema: z.ZodType<Pagination, z.ZodTypeDef, unknown>;
/** @internal */
export type Pagination$Outbound = {
    currentPage: number;
    pageSize: number;
    totalPages: number;
    totalCount: number;
};
/** @internal */
export declare const Pagination$outboundSchema: z.ZodType<Pagination$Outbound, z.ZodTypeDef, Pagination>;
/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export declare namespace Pagination$ {
    /** @deprecated use `Pagination$inboundSchema` instead. */
    const inboundSchema: z.ZodType<Pagination, z.ZodTypeDef, unknown>;
    /** @deprecated use `Pagination$outboundSchema` instead. */
    const outboundSchema: z.ZodType<Pagination$Outbound, z.ZodTypeDef, Pagination>;
    /** @deprecated use `Pagination$Outbound` instead. */
    type Outbound = Pagination$Outbound;
}
export declare function paginationToJSON(pagination: Pagination): string;
export declare function paginationFromJSON(jsonString: string): SafeParseResult<Pagination, SDKValidationError>;
//# sourceMappingURL=pagination.d.ts.map