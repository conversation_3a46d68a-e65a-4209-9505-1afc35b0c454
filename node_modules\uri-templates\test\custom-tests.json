{"Custom examples 1": {"variables": {"someString": "blah", "someBoolean": true, "someArray": ["a", "b", "c/d", "e.f", "g,h,i"], "someObject": {"a": "A", "b": "B", "c": "C"}}, "testcases": [["prefix/{someString}/suffix", "prefix/blah/suffix"], ["prefix/{someBoolean}/suffix", "prefix/true/suffix"], ["prefix/{someArray}/suffix", "prefix/a,b,c%2Fd,e.f,g%2Ch%2Ci/suffix"], ["prefix/{someArray*}/suffix", "prefix/a,b,c%2Fd,e.f,g%2Ch%2Ci/suffix"], ["prefix{/someArray}/suffix", "prefix/a,b,c%2Fd,e.f,g%2Ch%2Ci/suffix"], ["prefix{/someArray*}/suffix", "prefix/a/b/c%2Fd/e.f/g%2Ch%2Ci/suffix"], ["prefix{/someArray*}/", "prefix/a/b/c%2Fd/e.f/g%2Ch%2Ci/"], ["prefix{?someObject*}?", "prefix?a=A&b=B&c=C?"]]}}