https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=ComfyUI

vastai create instance <OFFER_ID> --image vastai/comfy:@vastai-automatic-tag --env '-p 1111:1111 -p 8080:8080 -p 8384:8384 -p 72299:72299 -p 8188:8188 -e OPEN_BUTTON_PORT=1111 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:8188:18188:/:ComfyUI|localhost:8080:18080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing" -e PROVISIONING_SCRIPT=https://raw.githubusercontent.com/vast-ai/base-image/refs/heads/main/derivatives/pytorch/derivatives/comfyui/provisioning_scripts/default.sh -e COMFYUI_ARGS="--disable-auto-launch --port 18188 --enable-cors-header"' --onstart-cmd 'entrypoint.sh' --disk 16 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Hashcat%20CUDA
vastai create instance <OFFER_ID> --image dizcza/docker-hashcat:cuda --disk 16 --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Unreal%20Engine%20Pixel%20Streaming
vastai create instance <OFFER_ID> --image vastai/unreal-pixel-streaming:UE5.******* --env '-p 1111:1111 -p 3000:3000 -p 70000:70000/udp -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:3000:13000:/:Pixel Streaming Interface|localhost:8080:8080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal" -e OPEN_BUTTON_PORT=1111 -p 8384:8384 -p 72299:72299' --onstart-cmd 'entrypoint.sh' --disk 32 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=SwarmUI
vastai create instance <OFFER_ID> --image vastai/swarmui:v0.9.5-Beta-cuda-12.1-pytorch-2.5.1-py311 --env '-p 1111:1111 -p 8080:8080 -p 8384:8384 -p 72299:72299 -p 7865:7865 -e OPEN_BUTTON_PORT=1111 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:7865:17865:/:SwarmUI|localhost:8080:18080:/:Jupyter|localhost:8080:18080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing" -e SWARMUI_ARGS="--launch_mode none --port 17865"' --onstart-cmd 'entrypoint.sh' --disk 16 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Whisper%20ASR%20Webservice
vastai create instance <OFFER_ID> --image onerahmet/openai-whisper-asr-webservice:latest-gpu --env '-e ENABLE_HTTPS=true -e ENABLE_AUTH=true -e ASR_MODEL=base -p 1111:1111 -p 8384:8384 -p 9000:9000 -p 72299:72299 -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:9000:19000:/:Whisper API (Swagger)|localhost:8080:8080:/:Jupyter|localhost:8384:18384:/:Syncthing" -e OPEN_BUTTON_PORT=1111 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/' --onstart-cmd 'env  >> /etc/environment;mkdir -p ${DATA_DIRECTORY:-/workspace/};mkdir -p /var/log/portal/;;apt install -y --no-install-recommends curl;[[ -d /opt/portal-aio ]] || curl -L https://vast2.s3.amazonaws.com/instance_portal/all-in-one/portal-aio.tar.gz | tar -xzv -C /opt;;whisper-asr-webservice -h 127.0.0.1 -p 19000 | tee /var/log/whisper.log &;;/opt/portal-aio/launch.sh' --disk 16 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Open-Sora
vastai create instance <OFFER_ID> --image vastai/pytorch:2.4.1-cuda-12.4.1-22.04 --env '-p 1111:1111 -p 7860:7860 -p 6006:6006 -p 8080:8080 -p 8384:8384 -p 72299:72299 -e OPEN_BUTTON_PORT=1111 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:7860:17860:/:Open Sora UI|localhost:8080:18080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing|localhost:6006:16006:/:Tensorboard" -e PROVISIONING_SCRIPT=https://raw.githubusercontent.com/vast-ai/base-image/refs/heads/main/derivatives/pytorch/provisioning_scripts/open_sora2.0.sh' --onstart-cmd 'entrypoint.sh' --disk 80 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Fooocus
vastai create instance <OFFER_ID> --image vastai/fooocus:59f183a-cuda-12.1-pytorch-2.5.1-ipv2 --env '-p 1111:1111 -p 8080:8080 -p 8384:8384 -p 72299:72299 -p 7865:7865 -e OPEN_BUTTON_PORT=1111 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:7865:17865:/:Fooocus|localhost:8080:18080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing" -e FOOOCUS_ARGS="--port 17865"' --onstart-cmd 'entrypoint.sh' --disk 16 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=ComfyUI%20%2B%20FLUX.1
vastai create instance <OFFER_ID> --image vastai/comfy:@vastai-automatic-tag --env '-p 1111:1111 -p 8080:8080 -p 8384:8384 -p 72299:72299 -p 8188:8188 -e OPEN_BUTTON_PORT=1111 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:8188:18188:/:ComfyUI|localhost:8080:18080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing" -e PROVISIONING_SCRIPT=https://raw.githubusercontent.com/vast-ai/base-image/refs/heads/main/derivatives/pytorch/derivatives/comfyui/provisioning_scripts/flux.sh -e COMFYUI_ARGS="--disable-auto-launch --port 18188 --enable-cors-header"' --onstart-cmd 'entrypoint.sh' --disk 48 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Ubuntu%20Desktop%20(VM)
vastai create instance <OFFER_ID> --image docker.io/vastai/kvm:ubuntu_desktop_22.04-2025-05-16 --env '-p 1111:1111 -p 3478:3478/udp -p 5900:5900 -p 6100:6100 -p 6200:6200 -p 741641:741641/udp -e OPEN_BUTTON_TOKEN=1 -e OPEN_BUTTON_PORT=1111 -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:6100:16100:/:Selkies WebRTC Desktop Viewer|localhost:6200:16200:/:NoVNC Desktop Viewer"' --disk 75 --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=Pinokio%20(Desktop)
vastai create instance <OFFER_ID> --image vastai/linux-desktop:@vastai-automatic-tag --env '-p 1111:1111 -p 6100:6100 -p 73478:73478 -p 8384:8384 -p 72299:72299 -p 6200:6200 -p 5900:5900 -e OPEN_BUTTON_TOKEN=1 -e JUPYTER_DIR=/ -e DATA_DIRECTORY=/workspace/ -e PORTAL_CONFIG="localhost:1111:11111:/:Instance Portal|localhost:6100:16100:/:Selkies Low Latency Desktop|localhost:6200:16200:/guacamole:Apache Guacamole Desktop (VNC)|localhost:8080:8080:/:Jupyter|localhost:8080:8080:/terminals/1:Jupyter Terminal|localhost:8384:18384:/:Syncthing" -e OPEN_BUTTON_PORT=1111 -e SELKIES_ENCODER=x264enc -e PROVISIONING_SCRIPT=https://raw.githubusercontent.com/vast-ai/base-image/refs/heads/main/derivatives/linux-desktop/provisioning_scripts/pinokio.sh' --onstart-cmd 'entrypoint.sh' --disk 64 --jupyter --ssh --direct

https://cloud.vast.ai/?ref_id=62897&creator_id=62897&name=PyTorch%20NGC
vastai create instance <OFFER_ID> --image nvcr.io/nvidia/pytorch:25.04-py3 --env '-e DATA_DIRECTORY=/workspace/ -e JUPYTER_DIR=/' --onstart-cmd 'env >> /etc/environment;mkdir -p ${DATA_DIRECTORY:-/workspace};;' --disk 16 --jupyter --ssh --direct

