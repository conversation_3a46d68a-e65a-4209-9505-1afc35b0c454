# PowerShell script to extract Vast.ai templates using Bright Data MCP
Write-Host "🚀 Starting Vast.ai Template Extraction..." -ForegroundColor Green

# Set environment variable
$env:API_TOKEN = "c2204591997072d2f0980981898439b1184b507295e49c085337bde43cf47805"

Write-Host "✅ API Token set" -ForegroundColor Green

# Function to run MCP command and capture output
function Invoke-MCPCommand {
    param(
        [string]$ToolName,
        [hashtable]$Arguments = @{}
    )
    
    Write-Host "🔧 Running $ToolName..." -ForegroundColor Yellow
    
    # Create JSON request
    $request = @{
        jsonrpc = "2.0"
        id = 1
        method = "tools/call"
        params = @{
            name = $ToolName
            arguments = $Arguments
        }
    } | ConvertTo-Json -Depth 10
    
    Write-Host "📤 Request: $request" -ForegroundColor Cyan
    
    # Start MCP process and send request
    $process = Start-Process -FilePath "npx" -ArgumentList "@brightdata/mcp" -PassThru -NoNewWindow -RedirectStandardInput -RedirectStandardOutput -RedirectStandardError
    
    # Send request
    $process.StandardInput.WriteLine($request)
    $process.StandardInput.Close()
    
    # Wait for response
    $output = $process.StandardOutput.ReadToEnd()
    $error = $process.StandardError.ReadToEnd()
    
    $process.WaitForExit(60000) # 60 second timeout
    
    Write-Host "📥 Output: $output" -ForegroundColor Green
    if ($error) {
        Write-Host "⚠️ Error: $error" -ForegroundColor Red
    }
    
    return @{
        Output = $output
        Error = $error
        ExitCode = $process.ExitCode
    }
}

try {
    Write-Host "🌐 Step 1: Navigating to Vast.ai templates page..." -ForegroundColor Blue
    $navResult = Invoke-MCPCommand -ToolName "scraping_browser_navigate" -Arguments @{
        url = "https://cloud.vast.ai/templates/"
    }
    
    Write-Host "⏳ Waiting for page to load..." -ForegroundColor Yellow
    Start-Sleep -Seconds 8
    
    Write-Host "📸 Step 2: Taking screenshot..." -ForegroundColor Blue
    $screenshotResult = Invoke-MCPCommand -ToolName "scraping_browser_screenshot"
    
    Write-Host "📝 Step 3: Getting page text..." -ForegroundColor Blue
    $textResult = Invoke-MCPCommand -ToolName "scraping_browser_get_text"
    
    Write-Host "🔗 Step 4: Getting page links..." -ForegroundColor Blue
    $linksResult = Invoke-MCPCommand -ToolName "scraping_browser_links"
    
    Write-Host "📄 Step 5: Getting HTML content..." -ForegroundColor Blue
    $htmlResult = Invoke-MCPCommand -ToolName "scraping_browser_get_html"
    
    # Try to find edit buttons
    Write-Host "✏️ Step 6: Looking for edit buttons..." -ForegroundColor Blue
    
    $editSelectors = @(
        'button[title="Edit"]',
        'button[aria-label="Edit"]',
        '.edit-btn',
        '.edit-button',
        '[data-testid="edit-button"]',
        'svg[data-icon="edit"]',
        '.fa-edit',
        '.fa-pencil'
    )
    
    $editResults = @()
    
    foreach ($selector in $editSelectors) {
        Write-Host "🎯 Trying selector: $selector" -ForegroundColor Yellow
        
        $clickResult = Invoke-MCPCommand -ToolName "scraping_browser_click" -Arguments @{
            selector = $selector
        }
        
        if ($clickResult.ExitCode -eq 0 -and $clickResult.Output -and !$clickResult.Output.Contains("error")) {
            Write-Host "✅ Successfully clicked edit button!" -ForegroundColor Green
            
            # Wait for modal
            Start-Sleep -Seconds 3
            
            # Get modal content
            $modalText = Invoke-MCPCommand -ToolName "scraping_browser_get_text"
            $modalHtml = Invoke-MCPCommand -ToolName "scraping_browser_get_html"
            
            $editResults += @{
                Selector = $selector
                ModalText = $modalText
                ModalHtml = $modalHtml
                Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            }
            
            # Try to close modal
            Write-Host "❌ Trying to close modal..." -ForegroundColor Yellow
            $closeResult = Invoke-MCPCommand -ToolName "scraping_browser_click" -Arguments @{
                selector = 'button[aria-label="Close"]'
            }
            
            Start-Sleep -Seconds 2
            break # Found working selector
        }
        else {
            Write-Host "❌ Failed with selector: $selector" -ForegroundColor Red
        }
    }
    
    # Compile results
    $results = @{
        ExtractedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Url = "https://cloud.vast.ai/templates/"
        Navigation = $navResult
        PageText = $textResult
        PageLinks = $linksResult
        PageHtml = $htmlResult
        EditButtonResults = $editResults
    }
    
    # Save results
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $filename = "vast-templates-extraction-$timestamp.json"
    $results | ConvertTo-Json -Depth 10 | Out-File -FilePath $filename -Encoding UTF8
    
    Write-Host "💾 Results saved to: $filename" -ForegroundColor Green
    Write-Host "📊 Extraction completed with $($editResults.Count) edit button interactions" -ForegroundColor Green
    Write-Host "✅ Extraction completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error during extraction: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
