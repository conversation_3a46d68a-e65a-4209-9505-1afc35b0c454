#!/usr/bin/env node

/**
 * Extract recommended templates from Vast.ai templates page
 * Uses Bright Data MCP tools for web scraping
 */

import { spawn } from 'child_process';
import { config } from 'dotenv';

// Load environment variables
config();

console.log('🚀 Extracting Vast.ai Recommended Templates...');

// Check if API token is available
if (!process.env.API_TOKEN) {
    console.error('❌ API_TOKEN not found in environment variables');
    process.exit(1);
}

// Function to send MCP request
function sendMCPRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
        const mcpClient = spawn('npx', ['@brightdata/mcp'], {
            env: {
                ...process.env,
                API_TOKEN: process.env.API_TOKEN
            },
            stdio: ['pipe', 'pipe', 'pipe']
        });

        const request = {
            jsonrpc: "2.0",
            id: Date.now(),
            method: method,
            params: params
        };

        mcpClient.stdin.write(JSON.stringify(request) + '\n');
        mcpClient.stdin.end();

        let output = '';
        mcpClient.stdout.on('data', (data) => {
            output += data.toString();
        });

        mcpClient.on('close', (code) => {
            try {
                const response = JSON.parse(output);
                resolve(response);
            } catch (e) {
                reject(new Error(`Failed to parse response: ${output}`));
            }
        });

        mcpClient.on('error', reject);
    });
}

async function extractVastTemplates() {
    try {
        console.log('🌐 Navigating to Vast.ai templates page...');
        
        // First, let's use web_data to extract structured data from the page
        const result = await sendMCPRequest('tools/call', {
            name: 'web_data',
            arguments: {
                url: 'https://cloud.vast.ai/templates/',
                data_schema: {
                    templates: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                name: { type: 'string' },
                                description: { type: 'string' },
                                image: { type: 'string' },
                                category: { type: 'string' },
                                recommended: { type: 'boolean' }
                            }
                        }
                    }
                }
            }
        });

        console.log('📊 Extraction result:', JSON.stringify(result, null, 2));
        
        // Filter for recommended templates only
        if (result.result && result.result.content) {
            const data = JSON.parse(result.result.content[0].text);
            const recommendedTemplates = data.templates?.filter(template => 
                template.recommended || 
                template.category?.toLowerCase().includes('recommended') ||
                template.name?.toLowerCase().includes('recommended')
            );
            
            console.log('✨ Recommended Templates Found:');
            console.log(JSON.stringify(recommendedTemplates, null, 2));
            
            return recommendedTemplates;
        }
        
    } catch (error) {
        console.error('❌ Error extracting templates:', error.message);
        
        // Fallback: Try browser automation approach
        console.log('🔄 Trying browser automation approach...');
        await extractWithBrowser();
    }
}

async function extractWithBrowser() {
    try {
        // Navigate to the page
        await sendMCPRequest('tools/call', {
            name: 'scraping_browser_navigate',
            arguments: {
                url: 'https://cloud.vast.ai/templates/'
            }
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Get page HTML
        const htmlResult = await sendMCPRequest('tools/call', {
            name: 'scraping_browser_get_html',
            arguments: {}
        });

        console.log('📄 Page HTML extracted, analyzing for recommended templates...');
        
        // Extract text content
        const textResult = await sendMCPRequest('tools/call', {
            name: 'scraping_browser_get_text',
            arguments: {}
        });

        console.log('📝 Page text content:', textResult);

    } catch (error) {
        console.error('❌ Browser automation error:', error.message);
    }
}

// Run the extraction
extractVastTemplates().then(() => {
    console.log('✅ Template extraction completed!');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
});
