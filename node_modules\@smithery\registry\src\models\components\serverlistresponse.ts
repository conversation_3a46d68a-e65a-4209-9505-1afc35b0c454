/*
 * Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT.
 */

import * as z from "zod";
import { safeParse } from "../../lib/schemas.js";
import { Result as SafeParseResult } from "../../types/fp.js";
import { SDKValidationError } from "../errors/sdkvalidationerror.js";
import {
  Pagination,
  Pagination$inboundSchema,
  Pagination$Outbound,
  Pagination$outboundSchema,
} from "./pagination.js";
import {
  ServerListItem,
  ServerListItem$inboundSchema,
  ServerListItem$Outbound,
  ServerListItem$outboundSchema,
} from "./serverlistitem.js";

export type ServerListResponse = {
  servers: Array<ServerListItem>;
  pagination: Pagination;
};

/** @internal */
export const ServerListResponse$inboundSchema: z.ZodType<
  ServerListResponse,
  z.ZodTypeDef,
  unknown
> = z.object({
  servers: z.array(ServerListItem$inboundSchema),
  pagination: Pagination$inboundSchema,
});

/** @internal */
export type ServerListResponse$Outbound = {
  servers: Array<ServerListItem$Outbound>;
  pagination: Pagination$Outbound;
};

/** @internal */
export const ServerListResponse$outboundSchema: z.ZodType<
  ServerListResponse$Outbound,
  z.ZodTypeDef,
  ServerListResponse
> = z.object({
  servers: z.array(ServerListItem$outboundSchema),
  pagination: Pagination$outboundSchema,
});

/**
 * @internal
 * @deprecated This namespace will be removed in future versions. Use schemas and types that are exported directly from this module.
 */
export namespace ServerListResponse$ {
  /** @deprecated use `ServerListResponse$inboundSchema` instead. */
  export const inboundSchema = ServerListResponse$inboundSchema;
  /** @deprecated use `ServerListResponse$outboundSchema` instead. */
  export const outboundSchema = ServerListResponse$outboundSchema;
  /** @deprecated use `ServerListResponse$Outbound` instead. */
  export type Outbound = ServerListResponse$Outbound;
}

export function serverListResponseToJSON(
  serverListResponse: ServerListResponse,
): string {
  return JSON.stringify(
    ServerListResponse$outboundSchema.parse(serverListResponse),
  );
}

export function serverListResponseFromJSON(
  jsonString: string,
): SafeParseResult<ServerListResponse, SDKValidationError> {
  return safeParse(
    jsonString,
    (x) => ServerListResponse$inboundSchema.parse(JSON.parse(x)),
    `Failed to parse 'ServerListResponse' from JSON`,
  );
}
